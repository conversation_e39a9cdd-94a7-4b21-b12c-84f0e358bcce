# 邮件模板功能说明

## 概述

本次更新为JavaFX邮件发送工具添加了完整的邮件模板功能，包括模板配置、文件过滤、目录扫描和模板管理等高级功能。

## 新增功能特性

### 1. 邮件模板配置

#### 模板字段
每个邮件模板包含以下配置字段：
- **模板名称**：唯一标识符，用于模板选择和管理
- **模板描述**：可选的模板说明信息
- **收件人列表**：预设的收件人邮箱地址（分号分隔）
- **抄送人列表**：预设的抄送人邮箱地址（分号分隔）
- **邮件主题**：预设的邮件主题
- **邮件正文**：预设的邮件内容
- **文件前缀过滤**：附件文件名前缀条件（逗号分隔）
- **文件扩展名过滤**：附件文件扩展名条件（逗号分隔）

#### 过滤条件说明
- **前缀过滤**：支持多个前缀，如 `report_,audit_,summary_`
- **扩展名过滤**：支持多个扩展名，如 `pdf,xlsx,docx,txt`
- **AND逻辑**：文件必须同时满足前缀和扩展名条件才会被选中
- **递归扫描**：自动扫描选定目录及其所有子目录

### 2. 数据持久化存储

#### SQLite数据库
- **数据库位置**：`{user.home}/.email-sender/templates.db3`
- **跨平台兼容**：支持Windows、macOS、Linux系统
- **自动初始化**：首次运行时自动创建数据库和表结构

#### 数据库表结构
```sql
CREATE TABLE email_templates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    recipients TEXT,
    cc_recipients TEXT,
    subject TEXT,
    body TEXT,
    file_prefixes TEXT,
    file_extensions TEXT,
    description TEXT,
    created_time TEXT NOT NULL,
    last_modified TEXT NOT NULL,
    use_count INTEGER DEFAULT 0,
    last_used TEXT,
    enabled INTEGER DEFAULT 1
);
```

### 3. 模板使用工作流

#### 步骤1：选择模板
1. 在主界面的"邮件模板"下拉框中选择预设模板
2. 模板显示格式：`模板名称 - 模板描述`
3. 支持模板的启用/禁用状态管理

#### 步骤2：选择目录
1. 点击"选择目录"按钮选择要扫描的父目录
2. 系统会记住上次选择的目录位置
3. 支持网络驱动器和外部存储设备

#### 步骤3：应用模板
1. 点击"应用模板"按钮执行模板应用
2. 系统自动填充邮件表单（收件人、主题、正文等）
3. 根据过滤条件递归扫描目录并添加匹配的附件
4. 显示扫描结果确认对话框

#### 步骤4：确认发送
1. 查看自动填充的邮件内容和附件列表
2. 可手动调整邮件内容或添加/删除附件
3. 点击"发送"按钮发送邮件

### 4. 模板管理界面

#### 模板管理对话框功能
- **模板列表**：显示所有已保存的模板
- **新建模板**：创建新的邮件模板
- **编辑模板**：修改现有模板配置
- **删除模板**：删除不需要的模板（带确认）
- **复制模板**：基于现有模板创建副本
- **测试过滤**：测试文件过滤条件是否正确

#### 表单验证
- **必填字段**：模板名称和邮件主题为必填项
- **唯一性检查**：模板名称不能重复
- **格式验证**：邮箱地址格式验证
- **实时反馈**：输入错误时显示具体提示信息

### 5. 文件过滤和扫描

#### 高级过滤功能
- **多前缀支持**：`report_,audit_,summary_,analysis_`
- **多扩展名支持**：`pdf,xlsx,docx,txt,csv,zip`
- **AND逻辑组合**：文件名必须匹配前缀AND扩展名
- **大小写不敏感**：自动处理大小写差异
- **递归目录扫描**：自动扫描所有子目录

#### 扫描结果预览
- **文件数量统计**：显示匹配文件的总数
- **大小统计**：显示文件总大小（自动格式化）
- **文件列表预览**：显示前10个匹配文件名
- **过滤条件摘要**：显示应用的过滤条件

### 6. UI集成和用户体验

#### 主界面集成
- **模板选择区域**：在发件人字段上方添加模板选择区域
- **一键应用**：选择模板和目录后一键应用所有配置
- **状态反馈**：实时显示操作状态和结果信息
- **错误处理**：友好的错误提示和恢复建议

#### 界面布局更新
```
1. 菜单栏（设置）
2. 邮件模板选择区域 ← 新增
   - 模板下拉框
   - 应用模板按钮
   - 选择目录按钮
   - 管理模板按钮
3. 发件人、收件人、抄送、主题输入区域
4. 附件管理区域
5. 邮件正文输入区域
6. 底部操作栏
```

## 技术实现

### 新增文件
1. **EmailTemplate.java**：邮件模板数据模型
2. **TemplateManager.java**：模板数据库操作管理器
3. **FileFilterUtil.java**：文件过滤和目录扫描工具
4. **TemplateManagementController.java**：模板管理对话框控制器
5. **TemplateManagementView.fxml**：模板管理对话框界面
6. **TemplateManagementTest.java**：功能测试类

### 修改文件
1. **pom.xml**：添加SQLite JDBC依赖
2. **EmailSenderView.fxml**：更新主界面布局
3. **EmailSenderController.java**：集成模板功能

### 架构特点
- **MVVM模式**：遵循现有的FXML + Controller架构
- **单例模式**：TemplateManager使用单例模式确保数据一致性
- **工厂模式**：FileFilterUtil提供统一的文件过滤接口
- **观察者模式**：UI组件监听数据变化自动更新

## 使用示例

### 创建模板示例
```
模板名称：月度报告模板
模板描述：用于发送月度财务和运营报告
收件人：<EMAIL>;<EMAIL>
抄送：<EMAIL>
主题：[月度报告] {当前月份} 财务和运营数据
正文：
尊敬的领导，

请查收本月的财务和运营报告。报告包含以下内容：
- 财务数据分析
- 运营指标统计
- 风险评估报告

如有疑问，请随时联系。

此致
敬礼

文件前缀：report_,monthly_,finance_
文件扩展名：pdf,xlsx,docx
```

### 过滤条件示例
- **审计报告**：前缀 `audit_,review_`，扩展名 `pdf,docx`
- **数据导出**：前缀 `export_,data_`，扩展名 `xlsx,csv,txt`
- **备份文件**：前缀 `backup_,archive_`，扩展名 `zip,rar,7z`

## 测试

### 运行测试
```bash
# 编译项目
mvn compile

# 运行模板管理测试
mvn test -Dtest=TemplateManagementTest

# 运行完整的邮件发送工具
mvn javafx:run
```

### 测试覆盖
- 模板CRUD操作（创建、读取、更新、删除）
- 文件过滤和目录扫描功能
- 模板应用和邮件表单填充
- 数据库初始化和数据持久化
- UI交互和错误处理

## 注意事项

### 性能考虑
- **大目录扫描**：扫描包含大量文件的目录可能需要较长时间
- **文件大小限制**：遵循现有的附件大小限制设置
- **内存使用**：大量文件列表可能占用较多内存

### 安全考虑
- **路径验证**：防止路径遍历攻击
- **文件权限**：检查文件读取权限
- **数据库安全**：SQLite文件存储在用户目录下

### 兼容性
- **Java版本**：要求Java 8或更高版本
- **JavaFX版本**：兼容JavaFX 11+
- **操作系统**：支持Windows、macOS、Linux

## 未来扩展

### 计划功能
1. **模板导入/导出**：支持模板的备份和分享
2. **模板分组**：按类别组织模板
3. **变量替换**：支持动态内容替换（如日期、用户名等）
4. **定时发送**：结合模板实现定时邮件发送
5. **模板统计**：使用频率和效果分析

### API扩展
- 提供REST API接口用于外部系统集成
- 支持命令行模式的模板应用
- 集成企业邮件系统和文档管理系统
