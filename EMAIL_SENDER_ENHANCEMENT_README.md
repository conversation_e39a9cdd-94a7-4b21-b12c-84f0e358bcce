# 邮件发送工具增强功能说明

## 概述

本次增强为JavaFX邮件发送工具添加了两个主要功能：
1. **发件人邮箱持久化和选择功能**
2. **增强的SMTP错误处理（特别是450错误）**

## 新增功能

### 1. 发件人邮箱管理

#### 主界面功能
- **发件人选择**：在主界面新增了"发件人"下拉框（ComboBox）
- **历史记录**：自动保存使用过的发件人邮箱地址
- **快速选择**：可以从下拉列表中选择之前使用过的邮箱
- **新邮箱输入**：支持直接输入新的邮箱地址，系统会自动保存

#### 设置界面功能
- **邮箱管理**：在设置对话框中可以管理保存的邮箱列表
- **删除功能**：可以删除不需要的邮箱地址
- **ComboBox集成**：设置界面也使用ComboBox进行邮箱选择

#### 数据持久化
- **配置文件**：邮箱列表保存在 `~/.email-sender/sender-emails.properties`
- **自动保存**：新输入的邮箱会自动添加到历史记录
- **去重处理**：重复的邮箱地址只会保存一次

### 2. 增强的错误处理

#### SMTP 450错误特殊处理
- **错误识别**：自动识别SMTP 450错误（邮件被临时拒绝）
- **用户友好提示**：显示专门的错误对话框，解释错误原因
- **重试选项**：提供"重试"和"取消"按钮供用户选择

#### 自动重试机制
- **重试次数**：最多自动重试3次
- **延迟机制**：每次重试间隔2秒
- **状态显示**：实时显示重试进度

#### 错误分类处理
- **450错误**：临时拒绝，提供重试选项
- **其他SMTP错误**：显示具体错误信息
- **网络错误**：显示连接相关的错误提示

## 使用方法

### 发件人邮箱管理

1. **选择已保存的邮箱**：
   - 点击主界面"发件人"下拉框
   - 从列表中选择之前使用过的邮箱

2. **添加新邮箱**：
   - 在"发件人"下拉框中直接输入新邮箱地址
   - 系统会自动保存到历史记录

3. **删除邮箱**：
   - 打开"设置"对话框
   - 在发件人邮箱下拉框中选择要删除的邮箱
   - 点击"删除"按钮

### 错误处理和重试

1. **遇到450错误时**：
   - 系统会显示专门的错误对话框
   - 选择"重试"继续发送，或"取消"停止发送

2. **自动重试**：
   - 系统会自动重试最多3次
   - 状态栏会显示重试进度
   - 如果所有重试都失败，会显示最终错误信息

3. **其他错误**：
   - 显示具体的错误信息
   - 提供相应的解决建议

## 技术实现

### 文件修改列表

1. **ConfigManager.java**：
   - 添加了发件人邮箱管理方法
   - 新增配置文件 `sender-emails.properties`

2. **EmailSenderController.java**：
   - 添加了发件人ComboBox的初始化和管理
   - 实现了简化的重试逻辑和450错误处理

3. **SettingsController.java**：
   - 集成了发件人邮箱管理功能
   - 添加了删除邮箱的功能

4. **EmailSenderView.fxml**：
   - 添加了发件人选择ComboBox
   - 调整了界面布局

5. **settings.fxml**：
   - 将发件人邮箱输入改为ComboBox
   - 添加了删除按钮

### 配置文件

- **邮件设置**：`~/.email-sender/email-settings.properties`
- **发件人邮箱列表**：`~/.email-sender/sender-emails.properties`

## 注意事项

1. **JavaFX版本兼容性**：确保FXML文件与运行时JavaFX版本匹配
2. **线程安全**：UI更新都在JavaFX应用线程中进行
3. **错误处理**：简化了异步处理，避免线程相关问题
4. **数据持久化**：配置文件保存在用户主目录下，确保数据安全

## 测试

运行 `EmailSenderEnhancementTest.java` 来测试新增功能：
- 发件人邮箱的添加、删除、去重
- 空值和无效输入的处理
- 配置文件的读写操作

## 故障排除

如果遇到编译或运行问题：

1. **检查JavaFX版本**：确保FXML文件中的版本号与运行时匹配
2. **检查依赖**：确保所有必要的JavaFX和JavaMail依赖都已正确配置
3. **检查编码**：确保源文件使用UTF-8编码
4. **检查权限**：确保应用有权限在用户目录创建配置文件
