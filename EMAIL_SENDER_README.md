# 邮件发送工具使用说明

## 功能概述

本邮件发送工具是基于JavaFX和JavaMail API开发的图形化邮件发送应用程序，支持以下功能：

- 📧 发送邮件到多个收件人和抄送人
- 📎 支持添加文件和文件夹作为附件
- ⚙️ 完整的SMTP服务器配置管理
- 🔒 支持SSL/TLS加密连接
- 💾 配置信息自动保存到本地
- 🧪 内置连接测试功能

## 主要组件

### 1. 核心类文件

- **EmailSenderController.java** - 主界面控制器，处理邮件发送逻辑
- **SettingsController.java** - 设置对话框控制器，管理SMTP配置
- **EmailSettings.java** - 邮件配置数据模型
- **ConfigManager.java** - 配置管理器，负责配置的保存和加载

### 2. FXML界面文件

- **aaa.fxml** - 主邮件发送界面
- **settings.fxml** - 设置对话框界面

### 3. 测试文件

- **EmailSenderTest.java** - 主应用程序启动类
- **EmailSettingsTest.java** - 配置功能测试类

## 使用方法

### 1. 启动应用程序

运行 `EmailSenderTest.java` 中的 `main` 方法启动应用程序。

### 2. 配置邮件服务器

首次使用时，需要配置邮件服务器信息：

1. 点击菜单栏中的 **"设置"** → **"发件人设置"**
2. 在弹出的对话框中填写以下信息：
   - **SMTP服务器**: 如 `smtp.qq.com`
   - **端口号**: 如 `587` 或 `465`
   - **邮箱地址**: 您的邮箱地址
   - **密码/授权码**: 邮箱密码或授权码
   - **启用SSL/TLS**: 建议勾选
   - **显示名称**: 发件人显示名称（可选）

3. 点击 **"测试连接"** 验证配置是否正确
4. 点击 **"保存"** 保存配置

### 3. 发送邮件

1. 填写收件人邮箱地址（多个地址用分号分隔）
2. 填写抄送人邮箱地址（可选）
3. 输入邮件主题
4. 编写邮件正文
5. 添加附件（可选）：
   - 点击 **"添加文件..."** 选择文件
   - 点击 **"添加文件夹..."** 选择整个文件夹
   - 选中附件后点击 **"删除选中"** 可移除附件
6. 点击 **"发送"** 按钮发送邮件

## 常见邮箱配置

### QQ邮箱
- **SMTP服务器**: smtp.qq.com
- **端口**: 587 或 465
- **SSL/TLS**: 启用
- **密码**: 使用授权码（不是QQ密码）
- **获取授权码**: QQ邮箱设置 → 账户 → POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务

### 网易邮箱（163/126）
- **SMTP服务器**: smtp.163.com 或 smtp.126.com
- **端口**: 465
- **SSL/TLS**: 启用
- **密码**: 使用授权码

### Gmail
- **SMTP服务器**: smtp.gmail.com
- **端口**: 587
- **SSL/TLS**: 启用
- **密码**: 使用应用专用密码

### Outlook/Hotmail
- **SMTP服务器**: smtp-mail.outlook.com
- **端口**: 587
- **SSL/TLS**: 启用

## 配置文件位置

配置信息保存在用户主目录下的 `.email-sender` 文件夹中：
- Windows: `C:\Users\<USER>\.email-sender\email-settings.json`
- Linux/Mac: `/home/<USER>/.email-sender/email-settings.json`

## 依赖项

项目使用了以下主要依赖：

```xml
<!-- JavaMail API -->
<dependency>
    <groupId>com.sun.mail</groupId>
    <artifactId>javax.mail</artifactId>
    <version>1.6.2</version>
</dependency>

<!-- Jackson for JSON -->
<dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-databind</artifactId>
    <version>2.12.7</version>
</dependency>
```

## 故障排除

### 1. 连接测试失败
- 检查网络连接
- 确认SMTP服务器地址和端口正确
- 验证邮箱地址和密码/授权码
- 确保已开启邮箱的SMTP服务

### 2. 发送邮件失败
- 检查收件人邮箱地址格式
- 确认邮件配置有效
- 查看控制台错误信息

### 3. 附件添加失败
- 确认文件存在且可读
- 检查文件大小限制
- 注意：文件夹附件会被跳过

## 安全注意事项

1. **密码安全**: 建议使用授权码而不是真实密码
2. **配置文件**: 配置文件以明文形式保存，请注意保护
3. **网络安全**: 建议使用SSL/TLS加密连接

## 开发说明

### 扩展功能
- 可以添加邮件模板功能
- 支持HTML格式邮件
- 添加邮件发送历史记录
- 支持批量发送

### 自定义配置
- 修改 `EmailSettings.java` 添加新的配置项
- 更新 `settings.fxml` 添加对应的UI控件
- 在 `SettingsController.java` 中处理新控件的逻辑

## 版本信息

- **版本**: 1.0.0
- **作者**: zhouyuan
- **创建日期**: 2025/6/30
- **Java版本**: 1.8+
- **JavaFX版本**: 8+
