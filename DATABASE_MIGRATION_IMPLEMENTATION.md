# 数据库迁移功能实现

## 概述

为了解决应用启动时因数据库架构不匹配导致的问题，实现了一套完整的数据库迁移解决方案。该方案确保从旧版本数据库平滑升级到新版本，同时保留所有现有数据。

## 问题背景

在添加模板变量替换功能时，需要在 `email_templates` 表中新增 `variables` 列来存储变量定义。但是现有用户的数据库中没有这个列，导致应用启动失败。

## 解决方案

### 1. 迁移策略

采用**增量迁移**策略，而不是删除重建：
- ✅ 保留现有数据
- ✅ 只添加缺失的列
- ✅ 为现有记录设置合理的默认值
- ✅ 支持多次运行（幂等性）

### 2. 实现架构

```
TemplateManager.initializeDatabase()
    ↓
创建基础表结构 (CREATE TABLE IF NOT EXISTS)
    ↓
performDatabaseMigration()
    ↓
检查各个列是否存在
    ↓
添加缺失的列并设置默认值
```

## 核心实现

### 1. 修改的 `initializeDatabase()` 方法

```java
private void initializeDatabase() {
    // 确保配置目录存在
    File configDir = new File(CONFIG_DIR);
    if (!configDir.exists()) {
        configDir.mkdirs();
    }
    
    // 创建基础表结构（不包含可能需要迁移的列）
    String createTableSQL = "CREATE TABLE IF NOT EXISTS email_templates (" +
            "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
            "name TEXT NOT NULL UNIQUE, " +
            // ... 其他基础列
            "enabled INTEGER DEFAULT 1" +
            ")";
    
    try (Connection conn = DriverManager.getConnection(JDBC_URL);
         Statement stmt = conn.createStatement()) {
        
        stmt.execute(createTableSQL);
        
        // 执行数据库迁移检查
        performDatabaseMigration(conn);
        
        System.out.println("邮件模板数据库初始化完成: " + DATABASE_FILE);
        
    } catch (SQLException e) {
        System.err.println("初始化邮件模板数据库失败: " + e.getMessage());
        e.printStackTrace();
    }
}
```

### 2. 新增的迁移方法

#### `performDatabaseMigration()`
```java
private void performDatabaseMigration(Connection conn) throws SQLException {
    // 检查并添加 variables 列（用于模板变量功能）
    if (!columnExists(conn, "email_templates", "variables")) {
        addVariablesColumn(conn);
    }
    
    // 未来可以在这里添加其他迁移逻辑
    // 例如：添加新列、修改列类型、创建索引等
}
```

#### `columnExists()` - 列存在性检查
```java
private boolean columnExists(Connection conn, String tableName, String columnName) throws SQLException {
    String sql = "PRAGMA table_info(" + tableName + ")";
    
    try (Statement stmt = conn.createStatement();
         ResultSet rs = stmt.executeQuery(sql)) {
        
        while (rs.next()) {
            String existingColumnName = rs.getString("name");
            if (columnName.equalsIgnoreCase(existingColumnName)) {
                return true;
            }
        }
    }
    
    return false;
}
```

#### `addVariablesColumn()` - 添加变量列
```java
private void addVariablesColumn(Connection conn) throws SQLException {
    String alterTableSQL = "ALTER TABLE email_templates ADD COLUMN variables TEXT";
    
    try (Statement stmt = conn.createStatement()) {
        stmt.execute(alterTableSQL);
        System.out.println("数据库迁移：成功添加 variables 列到 email_templates 表");
        
        // 为现有记录设置默认值（空字符串表示没有变量定义）
        String updateSQL = "UPDATE email_templates SET variables = '' WHERE variables IS NULL";
        stmt.execute(updateSQL);
        System.out.println("数据库迁移：为现有模板记录设置默认变量值");
        
    } catch (SQLException e) {
        System.err.println("添加 variables 列失败: " + e.getMessage());
        throw e;
    }
}
```

## 迁移流程

### 1. 应用启动时
1. **创建基础表**: 使用 `CREATE TABLE IF NOT EXISTS` 创建不包含新列的基础表结构
2. **检查列存在性**: 使用 SQLite 的 `PRAGMA table_info()` 检查 `variables` 列是否存在
3. **执行迁移**: 如果列不存在，执行 `ALTER TABLE` 添加列
4. **设置默认值**: 为现有记录的新列设置默认值（空字符串）

### 2. 迁移特点
- **幂等性**: 多次运行不会出错，已存在的列不会重复添加
- **数据安全**: 不会删除或修改现有数据
- **向后兼容**: 新列设置为可空，不影响现有功能
- **错误处理**: 完整的异常处理和错误日志

## 测试验证

### 1. 测试用例 (`DatabaseMigrationTest.java`)

测试覆盖以下场景：
- **旧数据库结构**: 创建没有 `variables` 列的旧版本数据库
- **数据插入**: 在旧结构中插入测试数据
- **迁移执行**: 模拟迁移过程
- **结果验证**: 确认列已添加且数据完整

### 2. 测试步骤
```java
@Test
public void testMigrationFromOldSchema() throws SQLException {
    // 1. 创建旧版本的数据库结构（没有 variables 列）
    createOldSchemaDatabase();
    
    // 2. 插入一些测试数据
    insertTestDataInOldSchema();
    
    // 3. 验证旧数据存在且没有 variables 列
    verifyOldSchemaAndData();
    
    // 4. 触发迁移
    performMigrationTest();
    
    // 5. 验证迁移后的结果
    verifyMigrationResults();
}
```

## 使用 SQLite PRAGMA 的优势

### 1. `PRAGMA table_info(table_name)`
- **功能**: 返回表的列信息
- **优势**: SQLite 原生支持，无需解析复杂的系统表
- **返回信息**: 列名、类型、是否可空、默认值等

### 2. 示例输出
```
cid | name         | type    | notnull | dflt_value | pk
----|--------------|---------|---------|------------|----
0   | id           | INTEGER | 0       | NULL       | 1
1   | name         | TEXT    | 1       | NULL       | 0
2   | variables    | TEXT    | 0       | NULL       | 0
```

## 扩展性设计

### 1. 未来迁移支持
`performDatabaseMigration()` 方法设计为可扩展：
```java
private void performDatabaseMigration(Connection conn) throws SQLException {
    // 现有迁移
    if (!columnExists(conn, "email_templates", "variables")) {
        addVariablesColumn(conn);
    }
    
    // 未来可以添加更多迁移
    if (!columnExists(conn, "email_templates", "new_column")) {
        addNewColumn(conn);
    }
    
    if (!indexExists(conn, "idx_template_name")) {
        createTemplateNameIndex(conn);
    }
}
```

### 2. 版本控制
可以进一步扩展为基于版本号的迁移系统：
```java
private void performDatabaseMigration(Connection conn) throws SQLException {
    int currentVersion = getDatabaseVersion(conn);
    
    if (currentVersion < 2) {
        migrateToVersion2(conn); // 添加 variables 列
    }
    
    if (currentVersion < 3) {
        migrateToVersion3(conn); // 添加其他功能
    }
    
    updateDatabaseVersion(conn, LATEST_VERSION);
}
```

## 错误处理

### 1. 异常处理策略
- **SQLException**: 捕获并重新抛出，确保调用方知道迁移失败
- **日志记录**: 详细记录迁移过程和错误信息
- **回滚机制**: 如果需要，可以添加事务回滚

### 2. 错误恢复
- **部分失败**: 如果某个迁移步骤失败，不影响其他步骤
- **重试机制**: 应用重启时会重新检查和执行迁移
- **手动修复**: 提供清晰的错误信息便于手动修复

## 总结

这个数据库迁移解决方案具有以下优点：

1. **数据安全**: 绝不删除现有数据
2. **向后兼容**: 支持从任何旧版本升级
3. **自动化**: 应用启动时自动执行
4. **可扩展**: 易于添加新的迁移逻辑
5. **测试完备**: 完整的测试用例覆盖
6. **错误处理**: 完善的异常处理和日志记录

通过这个实现，用户可以无缝从旧版本升级到新版本，无需手动处理数据库架构变更，确保了应用的稳定性和用户体验。
