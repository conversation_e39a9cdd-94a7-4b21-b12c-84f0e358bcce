# 邮件模板单文件智能压缩加密功能实现文档

## 概述

本次更新为邮件模板系统添加了单文件智能压缩加密功能，允许用户在模板中配置特定文件的自动压缩处理。该功能与现有的"模板压缩功能"（压缩所有文件）不同，专门针对指定的单个文件进行智能压缩，提供更精确的附件管理。

## 功能特性

### 1. 数据模型扩展

#### EmailTemplate 新增字段
- `singleFileCompressionEnabled` (boolean): 是否启用单文件压缩
- `targetFileName` (String): 需要压缩的目标文件名（支持通配符或正则表达式）
- 复用现有的 `compressionPassword` 字段作为压缩密码

#### 相关方法
- `isSingleFileCompressionEnabled()` / `setSingleFileCompressionEnabled(boolean)`: 单文件压缩状态控制
- `getTargetFileName()` / `setTargetFileName(String)`: 目标文件名模式操作
- `hasTargetFileName()`: 检查是否设置了目标文件名

### 2. 数据库架构更新

#### 新增数据库列
- `single_file_compression_enabled INTEGER DEFAULT 0`: 单文件压缩启用状态 (0=不启用, 1=启用)
- `target_file_name TEXT`: 目标文件名模式（支持通配符和正则表达式）

#### 数据库迁移
- 自动检测并添加新列到现有数据库
- 为现有模板设置默认值（不启用单文件压缩）
- 向后兼容，不影响现有功能

### 3. 单文件压缩处理逻辑

#### SingleFileCompressionHandler 核心功能
- **文件匹配**: 支持精确匹配、通配符模式、正则表达式模式
- **智能检测**: 自动检查是否已存在同名压缩文件
- **异步压缩**: 不阻塞UI线程的后台压缩处理
- **进度反馈**: 实时显示压缩进度和状态

#### 文件匹配模式
```java
// 精确匹配
"report.xlsx"

// 通配符模式
"*.pdf"
"report_*.xlsx"

// 正则表达式模式
"^audit.*\.xlsx$"
"(?i).*report.*\.pdf$"
```

#### 压缩处理流程
1. **文件扫描**: 在附件列表中查找匹配目标文件名模式的文件
2. **存在性检查**: 检查是否已存在同名的压缩文件（.zip格式）
3. **智能处理**:
   - 如果压缩文件已存在：直接使用现有压缩文件，移除源文件
   - 如果压缩文件不存在：创建新的加密压缩文件
4. **文件替换**: 压缩完成后，从附件列表中移除源文件，添加压缩文件

### 4. 文件命名规则

#### 压缩文件命名格式
```
原文件名（不含扩展名）.zip
```

#### 示例
- `report.xlsx` → `report.zip`
- `audit_2024.pdf` → `audit_2024.zip`
- `data_export.csv` → `data_export.zip`

### 5. 用户界面设计

#### 模板管理界面新增区域
```
文件压缩设置
├── 启用全部文件压缩 [复选框]
│   └── 压缩密码（可选）[密码输入框]
├── ─────────────────────────────
└── 启用单文件智能压缩 [复选框]
    └── 目标文件名模式 [文本输入框]
        ├── 支持的模式：
        ├── • 精确匹配: report.xlsx
        ├── • 通配符: *.pdf, report_*.xlsx
        └── • 正则表达式: ^audit.*\.xlsx$
```

#### UI交互逻辑
- 启用单文件压缩时显示配置区域
- 提供详细的模式说明和示例
- 与全部文件压缩功能清晰区分

### 6. 集成要求实现

#### 与现有功能的协调
- **优先级设计**: 单文件压缩优先级高于全部文件压缩
- **智能处理集成**: 与现有智能处理功能无缝配合
- **状态管理**: 统一的状态反馈和错误处理

#### 处理优先级
```
单文件压缩 > 全部文件压缩 > 普通附件处理
```

## 技术实现

### 1. 核心类和方法

#### SingleFileCompressionHandler
- `findTargetFile()`: 智能文件匹配算法
- `generateCompressedFilePath()`: 压缩文件路径生成
- `isCompressedFileExists()`: 压缩文件存在性检查
- `compressSingleFile()`: 同步压缩处理
- `compressSingleFileAsync()`: 异步压缩处理

#### EmailSenderController 集成方法
- `handleSingleFileCompression()`: 单文件压缩主控制逻辑
- `handleExistingCompressedFile()`: 处理已存在的压缩文件
- `createNewCompressedFile()`: 创建新压缩文件
- `onSingleFileCompressionSuccess()`: 压缩成功回调
- `onSingleFileCompressionError()`: 压缩失败回调

### 2. 文件匹配算法

#### 通配符转正则表达式
```java
private static String convertWildcardToRegex(String wildcard) {
    // * → .*
    // ? → .
    // 特殊字符转义
    return "^" + convertedPattern + "$";
}
```

#### 匹配优先级
1. **精确匹配**: 直接字符串比较
2. **通配符模式**: 包含 * 或 ? 字符
3. **正则表达式**: 以 ^ 开头或 $ 结尾，或包含转义字符

### 3. 异步处理机制

#### Task-based 异步压缩
```java
Task<CompressionResult> compressionTask = new Task<CompressionResult>() {
    @Override
    protected CompressionResult call() throws Exception {
        // 压缩处理逻辑
        return compressSingleFile(sourceFile, password);
    }
};
```

#### 进度反馈和状态更新
- 实时进度百分比显示
- 状态标签动态更新
- 成功/失败的用户友好提示

## 使用流程

### 1. 模板配置
1. 在模板管理界面勾选"启用单文件智能压缩"
2. 输入目标文件名模式（支持通配符或正则表达式）
3. 可选：设置压缩密码（与全部文件压缩共用）
4. 保存模板

### 2. 模板应用
1. 选择启用了单文件压缩的模板
2. 选择要扫描的目录
3. 点击"应用模板"
4. 系统自动扫描并匹配目标文件
5. 执行智能压缩处理
6. 显示压缩结果和文件信息

### 3. 智能处理逻辑
```
模板应用 → 文件扫描 → 目标匹配 → 存在性检查 → 压缩处理 → 文件替换 → 完成反馈
```

## 错误处理策略

### 1. 目标文件不存在
- **处理**: 显示友好提示，继续正常的附件处理流程
- **用户反馈**: "未找到匹配的目标文件"

### 2. 压缩失败
- **回退机制**: 保留原文件，不进行替换
- **错误提示**: 显示具体的失败原因
- **状态恢复**: 恢复到压缩前的附件状态

### 3. 文件权限问题
- **权限检查**: 压缩前检查文件读写权限
- **友好提示**: 提供权限相关的解决建议
- **优雅降级**: 权限不足时跳过压缩处理

### 4. 文件名冲突
- **覆盖策略**: 如果压缩文件已存在，直接使用现有文件
- **用户通知**: 明确告知使用了现有的压缩文件
- **大小显示**: 显示现有压缩文件的大小信息

## 性能优化

### 1. 异步处理
- 压缩操作在后台线程执行
- UI线程不被阻塞
- 实时进度反馈

### 2. 智能检测
- 优先使用已存在的压缩文件
- 避免重复压缩相同文件
- 减少不必要的IO操作

### 3. 内存管理
- 使用缓冲区进行文件读写
- 及时释放文件句柄
- 控制内存使用量

## 兼容性保证

### 1. 向后兼容
- 现有模板默认不启用单文件压缩
- 不影响现有的全部文件压缩功能
- 保持原有的用户界面布局

### 2. 功能隔离
- 单文件压缩与全部文件压缩相互独立
- 清晰的优先级设计
- 独立的错误处理机制

### 3. 数据迁移
- 自动数据库结构升级
- 无需手动数据转换
- 平滑的功能过渡

## 测试建议

### 1. 功能测试
- 各种文件匹配模式的正确性
- 压缩文件存在性检查
- 异步压缩的稳定性

### 2. 边界测试
- 大文件压缩性能
- 特殊字符文件名处理
- 网络驱动器文件访问

### 3. 集成测试
- 与全部文件压缩的协调
- 与智能处理功能的兼容性
- 模板导入/导出功能

### 4. 用户体验测试
- UI响应性和友好性
- 错误提示的清晰度
- 帮助信息的实用性

## 后续优化建议

### 1. 功能扩展
- 支持多个目标文件模式
- 压缩级别选择
- 批量单文件压缩

### 2. 用户体验
- 文件匹配预览功能
- 压缩效果统计
- 可视化配置界面

### 3. 性能优化
- 并行压缩处理
- 智能缓存机制
- 压缩算法优化

## 总结

单文件智能压缩加密功能的实现为邮件模板系统提供了更精确和灵活的附件管理能力。通过智能的文件匹配、异步的压缩处理和友好的用户界面，用户可以轻松配置特定文件的自动压缩，提高邮件发送的效率和安全性。

功能设计充分考虑了与现有系统的兼容性和集成性，确保了平滑的功能升级和良好的用户体验。
