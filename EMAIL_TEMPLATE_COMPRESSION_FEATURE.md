# 邮件模板文件压缩功能实现文档

## 概述

本次更新为邮件模板系统添加了完整的文件压缩功能，允许用户在模板中配置自动压缩选项，当应用模板时自动将扫描到的文件压缩为单个ZIP文件。

## 功能特性

### 1. 数据模型扩展

#### EmailTemplate 新增字段
- `enableCompression` (boolean): 是否启用文件压缩
- `compressionPassword` (String): 压缩文件的解压密码（可选）

#### 相关方法
- `isEnableCompression()`: 检查是否启用压缩
- `hasCompressionPassword()`: 检查是否设置了压缩密码
- `setEnableCompression(boolean)`: 设置压缩状态
- `setCompressionPassword(String)`: 设置压缩密码

### 2. 数据库架构更新

#### 新增数据库列
- `enable_compression INTEGER DEFAULT 0`: 压缩启用状态 (0=不压缩, 1=压缩)
- `compression_password TEXT`: 压缩密码（可为空）

#### 数据库迁移
- 自动检测并添加新列到现有数据库
- 为现有模板设置默认值（不启用压缩）
- 向后兼容，不影响现有功能

### 3. 模板管理界面增强

#### 新增UI控件
- **压缩复选框**: 启用/禁用文件压缩
- **密码输入框**: 设置压缩密码（可选）
- **动态显示**: 只有启用压缩时才显示密码输入区域

#### 表单验证
- 压缩设置与其他模板字段一起保存
- 支持编辑现有模板的压缩配置
- 复制模板时包含压缩设置

### 4. 智能处理逻辑集成

#### 模板应用流程
1. 用户选择启用压缩的模板
2. 系统扫描并添加匹配的文件到附件列表
3. 自动检测到模板启用压缩
4. 显示"模板压缩"按钮
5. 自动执行异步压缩处理

#### 压缩处理逻辑
- **异步压缩**: 不阻塞UI线程
- **进度反馈**: 实时显示压缩进度
- **密码保护**: 支持设置ZIP密码（如果模板配置了密码）
- **文件替换**: 压缩完成后替换附件列表为单个ZIP文件

### 5. 压缩实现技术

#### AttachmentCompressionHandler 扩展
- `compressFiles(File[], String, String)`: 支持密码的压缩方法
- `compressFilesAsync()`: 异步压缩方法
- 进度回调支持
- 错误处理和用户反馈

#### 文件命名规则
- 格式: `template_{模板名称}_{时间戳}.zip`
- 特殊字符替换为下划线
- 确保文件名唯一性

### 6. 用户体验优化

#### 视觉反馈
- 压缩进度百分比显示
- 状态标签实时更新
- 完成后显示确认对话框

#### 智能处理按钮状态
- **"模板压缩"**: 执行压缩处理
- **"查看历史"**: 查看压缩历史记录
- 动态文本和功能切换

#### 错误处理
- 压缩失败时的友好提示
- 自动回退到原始附件列表
- 详细的错误信息显示

## 技术架构

### 1. 数据流
```
模板选择 → 文件扫描 → 压缩检测 → 异步压缩 → UI更新 → 发送准备
```

### 2. 关键类和方法

#### EmailSenderController
- `handleTemplateCompression()`: 处理模板压缩逻辑
- `executeTemplateCompression()`: 执行异步压缩
- `onTemplateCompressionSuccess()`: 压缩成功回调
- `onTemplateCompressionError()`: 压缩失败回调

#### TemplateManager
- `addCompressionColumns()`: 数据库迁移方法
- 更新的保存和加载方法支持压缩字段

#### TemplateManagementController
- `setupCompressionControls()`: 设置压缩控件
- 表单验证和数据绑定

### 3. 配置管理
- 压缩设置持久化到SQLite数据库
- 支持导入/导出功能
- 模板复制包含压缩配置

## 使用流程

### 1. 创建带压缩的模板
1. 在模板管理界面点击"新建模板"
2. 填写基本信息（名称、主题、正文等）
3. 设置文件过滤条件
4. 勾选"启用文件压缩"
5. 可选：设置压缩密码
6. 保存模板

### 2. 应用压缩模板
1. 在主界面选择启用压缩的模板
2. 选择要扫描的目录
3. 点击"应用模板"
4. 系统自动填充邮件表单并扫描文件
5. 自动显示"模板压缩"按钮并执行压缩
6. 压缩完成后显示确认信息
7. 发送邮件

### 3. 压缩结果
- 原始文件被替换为单个ZIP文件
- ZIP文件名包含模板名称和时间戳
- 如果设置了密码，ZIP文件受密码保护
- 附件列表显示压缩文件信息和大小

## 兼容性和安全性

### 1. 向后兼容
- 现有模板默认不启用压缩
- 不影响现有的智能处理功能
- 数据库自动迁移，无需手动操作

### 2. 安全考虑
- 密码以明文存储在数据库中（考虑后续加密）
- 压缩文件保存在用户选择的目录或用户主目录
- 临时文件自动清理

### 3. 性能优化
- 异步压缩不阻塞UI
- 进度反馈提升用户体验
- 内存使用优化

## 后续优化建议

### 1. 安全增强
- 数据库中密码字段加密存储
- 支持更强的加密算法

### 2. 功能扩展
- 支持不同压缩级别选择
- 支持其他压缩格式（7z, RAR等）
- 批量压缩模板

### 3. 用户体验
- 压缩预览功能
- 压缩比统计
- 压缩历史管理

## 测试建议

### 1. 功能测试
- 创建和编辑压缩模板
- 应用压缩模板的完整流程
- 密码保护的ZIP文件创建和解压

### 2. 边界测试
- 大文件压缩性能
- 网络驱动器文件处理
- 权限不足的目录处理

### 3. 集成测试
- 与现有智能处理功能的兼容性
- 模板导入/导出功能
- 数据库迁移测试

## 总结

本次实现完整地集成了文件压缩功能到邮件模板系统中，提供了从UI配置到自动压缩的完整解决方案。功能设计遵循了现有的架构模式，确保了代码的一致性和可维护性。通过异步处理和友好的用户反馈，提供了良好的用户体验。
