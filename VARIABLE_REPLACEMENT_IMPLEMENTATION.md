# 邮件模板变量替换功能实现

## 概述

成功为邮件模板系统添加了完整的变量替换功能，支持用户在模板中定义可替换的变量，并在应用模板时通过对话框输入具体值进行替换。

## 实现的功能特性

### 1. 变量语法
- **占位符格式**: `{{variable_name}}`
- **变量名规则**: 只能包含字母、数字和下划线，且不能以数字开头
- **示例**: `{{billing_period}}`, `{{confirmation_time}}`, `{{user_name}}`

### 2. 变量类型支持
- **TEXT**: 文本类型，无特殊验证
- **DATE**: 日期类型，支持 yyyy-MM-dd 格式验证
- **NUMBER**: 数字类型，支持数值验证
- **EMAIL**: 邮箱类型，支持邮箱格式验证

### 3. 变量定义系统
每个变量可以定义以下属性：
- **变量名**: 用于模板中的占位符
- **显示标签**: 用户界面中显示的友好名称
- **变量类型**: 决定输入验证规则
- **默认值**: 可选的预设值
- **是否必填**: 控制是否为必填字段
- **描述信息**: 变量的详细说明

### 4. 预设变量
系统自动提供以下预设变量：
- `{{current_date}}`: 当前日期 (yyyy-MM-dd)
- `{{current_time}}`: 当前时间 (HH:mm:ss)
- `{{current_datetime}}`: 当前日期时间 (yyyy-MM-dd HH:mm:ss)
- `{{user_name}}`: 当前系统用户名

## 新增文件

### 1. 模型类
- **TemplateVariable.java**: 变量定义模型，包含变量类型枚举和验证方法
- **VariableInputController.java**: 变量输入对话框控制器

### 2. 工具类
- **TemplateVariableProcessor.java**: 变量处理核心工具类，负责：
  - 变量提取和替换
  - 预设变量处理
  - JSON序列化/反序列化
  - 输入验证

### 3. 界面文件
- **VariableInputView.fxml**: 变量输入对话框界面

### 4. 测试文件
- **TemplateVariableTest.java**: 完整的功能测试用例

## 修改的文件

### 1. 数据模型更新
- **EmailTemplate.java**: 添加 `variables` 字段存储变量定义

### 2. 数据库更新
- **TemplateManager.java**: 
  - 更新数据库表结构，添加 `variables` 列
  - 更新保存和加载方法支持变量字段

### 3. 界面集成
- **TemplateManagementView.fxml**: 添加变量管理区域
- **TemplateManagementController.java**: 
  - 添加变量管理功能
  - 集成变量编辑对话框
  - 支持变量插入到主题和正文

### 4. 模板应用流程
- **EmailSenderController.java**: 
  - 集成变量处理流程
  - 在应用模板时显示变量输入对话框
  - 执行变量替换

## 使用流程

### 1. 创建带变量的模板
1. 在模板管理界面点击"新建模板"
2. 在主题或正文中使用 `{{variable_name}}` 语法
3. 点击"管理变量"定义变量属性
4. 保存模板

### 2. 应用模板
1. 在主界面选择包含变量的模板
2. 选择目录并点击"应用模板"
3. 系统自动检测变量并显示输入对话框
4. 填写变量值并确认
5. 模板内容自动替换变量并填入邮件表单

### 3. 变量管理功能
- **添加变量**: 定义新的模板变量
- **编辑变量**: 修改现有变量属性
- **删除变量**: 移除不需要的变量
- **插入变量**: 在编辑时快速插入变量占位符
- **预览效果**: 查看变量替换后的效果

## 技术特点

### 1. 架构设计
- **MVVM模式**: 遵循现有的FXML + Controller架构
- **单一职责**: 每个类职责明确，便于维护
- **可扩展性**: 支持添加新的变量类型

### 2. 数据持久化
- **JSON存储**: 变量定义以JSON格式存储在数据库中
- **向后兼容**: 现有模板不受影响
- **数据完整性**: 完整的验证和错误处理

### 3. 用户体验
- **直观界面**: 清晰的变量管理界面
- **实时验证**: 输入时即时验证格式
- **预览功能**: 支持预览替换效果
- **错误提示**: 详细的错误信息和修复建议

## 示例用法

### 模板示例
```
主题: {{billing_period}} 月度财务报告

正文:
尊敬的领导，

请查收 {{billing_period}} 的财务报告。

报告生成时间：{{current_datetime}}
确认截止时间：{{confirmation_deadline}}

如有疑问，请联系：{{contact_email}}

此致
敬礼
```

### 变量定义示例
- `billing_period`: 账期 (TEXT, 必填, 默认值: "2025年1月")
- `confirmation_deadline`: 确认截止时间 (DATE, 必填)
- `contact_email`: 联系邮箱 (EMAIL, 非必填, 默认值: "<EMAIL>")

### 替换结果示例
```
主题: 2025年1月 月度财务报告

正文:
尊敬的领导，

请查收 2025年1月 的财务报告。

报告生成时间：2025-07-26 14:30:00
确认截止时间：2025-01-31

如有疑问，请联系：<EMAIL>

此致
敬礼
```

## 测试覆盖

实现了完整的单元测试，覆盖：
- 变量提取功能
- 变量替换功能
- 预设变量处理
- 序列化/反序列化
- 输入验证
- 完整工作流程

## 总结

成功实现了功能完整、用户友好的邮件模板变量替换系统，满足了所有需求：
- ✅ 支持 `{{variable_name}}` 语法
- ✅ 完整的变量定义系统
- ✅ 多种变量类型和验证
- ✅ 用户友好的输入对话框
- ✅ 数据库持久化存储
- ✅ 与现有架构完美集成
- ✅ 预设变量支持
- ✅ 完整的测试覆盖

该实现遵循了JavaFX最佳实践，保持了代码的可维护性和可扩展性，为用户提供了强大而易用的模板变量功能。
