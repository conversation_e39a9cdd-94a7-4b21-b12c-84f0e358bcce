# 文件路径显示功能增强

## 概述

增强了EmailSenderController.java中的文件路径显示功能，在两个特定上下文中提供更详细的路径信息，帮助用户更好地识别文件位置。

## 增强功能

### Context 1: 邮件模板应用 - 附件列表显示

**模板应用的文件显示格式：**
```
[文件] filename.ext (size) - 相对路径: relative/path/to/file
```

**手动添加的文件显示格式：**
```
[文件] filename.ext (size) - 绝对路径: C:/full/path/to/file
```

**目录显示格式：**
```
[目录-压缩] foldername (10个文件, 25.6 MB) - 绝对路径: C:/full/path/to/folder
```

### Context 2: 智能处理历史对话框

**被移除文件信息显示格式：**
```
📄 filename.ext
   大小: file_size
   路径: /full/path/to/file
   原因: removal_reason
   时间: timestamp
```

## 技术实现

### 1. 增强的formatAttachmentDisplay方法

**新增重载方法：**
```java
private String formatAttachmentDisplay(String filePath, boolean isDirectory, boolean showPath)
```

**路径信息获取方法：**
```java
private String getPathDisplayInfo(String filePath)
```

**智能路径判断逻辑：**
- 如果文件在`selectedDirectoryPath`下 → 显示相对路径
- 否则 → 显示绝对路径

### 2. 路径显示逻辑

**相对路径计算：**
```java
if (fileCanonical.startsWith(selectedDirCanonical)) {
    String relativePath = fileCanonical.substring(selectedDirCanonical.length());
    return "相对路径: " + relativePath;
}
```

**绝对路径显示：**
```java
return "绝对路径: " + file.getAbsolutePath();
```

### 3. 增强的RemovedFileInfo类

**新增字段：**
```java
private final String filePath;
```

**更新构造函数：**
```java
public RemovedFileInfo(String fileName, String filePath, long fileSize, String reason, long removalTime)
```

## 修改的方法

### 1. formatAttachmentDisplay方法
- 添加了`showPath`参数的重载版本
- 集成了路径信息显示逻辑

### 2. getPathDisplayInfo方法（新增）
- 智能判断显示相对路径还是绝对路径
- 基于`selectedDirectoryPath`进行路径计算

### 3. 附件添加方法
- `applySelectedTemplate()`: 模板应用时显示路径信息
- `addFiles()`: 手动添加文件时显示绝对路径
- `addFolder()`: 手动添加目录时显示绝对路径

### 4. 智能处理历史
- `RemovedFileInfo`类: 增加文件路径字段
- `recordProcessing()`: 记录完整文件路径
- `showRemovedFilesDialog()`: 显示文件路径信息

## 使用场景

### 场景1: 模板应用
1. 用户选择目录: `D:/projects/reports`
2. 应用模板加载文件
3. **显示效果**:
   ```
   [文件] report1.pdf (2.3 MB) - 相对路径: 2025/january/report1.pdf
   [文件] report2.xlsx (1.8 MB) - 相对路径: 2025/january/report2.xlsx
   ```

### 场景2: 手动添加文件
1. 用户通过"添加文件"按钮选择文件
2. **显示效果**:
   ```
   [文件] document.docx (1.2 MB) - 绝对路径: C:/Users/<USER>/Documents/document.docx
   ```

### 场景3: 智能处理历史
1. 超大文件被移除
2. 用户点击"查看历史"
3. **显示效果**:
   ```
   📄 large_file.zip
      大小: 38.9 MB
      路径: D:/projects/reports/2025/large_file.zip
      原因: 超过单文件大小限制
      时间: 2025-07-09 15:39:19
   ```

## 用户体验改进

### 1. 路径可见性
- **模板文件**: 显示相对于选定目录的路径，便于理解文件结构
- **手动文件**: 显示完整路径，便于定位文件位置

### 2. 历史追踪
- **完整路径**: 被移除文件的完整路径信息
- **便于恢复**: 用户可以根据路径信息重新找到文件

### 3. 智能判断
- **自动识别**: 系统自动判断显示相对路径还是绝对路径
- **上下文相关**: 根据文件来源选择最合适的显示方式

## 兼容性保证

### 1. 向后兼容
- 保持原有的`formatAttachmentDisplay(filePath, isDirectory)`方法
- 新增重载方法不影响现有调用

### 2. 架构保持
- 维持FXML + Controller架构
- 不影响其他功能模块

### 3. 性能优化
- 路径计算仅在需要时执行
- 缓存规范路径避免重复计算

## 测试验证

### 测试用例1: 模板应用路径显示
- [ ] 选择目录并应用模板
- [ ] 验证相对路径正确显示
- [ ] 验证路径格式正确

### 测试用例2: 手动添加路径显示
- [ ] 手动添加文件
- [ ] 验证绝对路径正确显示
- [ ] 验证目录路径正确显示

### 测试用例3: 智能处理历史
- [ ] 触发文件移除
- [ ] 查看处理历史
- [ ] 验证路径信息完整显示

### 测试用例4: 混合场景
- [ ] 同时有模板文件和手动文件
- [ ] 验证不同路径显示方式
- [ ] 验证UI布局正常

## 预期效果

1. **提高用户体验**: 用户可以清楚地知道文件的位置
2. **便于文件管理**: 相对路径帮助理解项目结构
3. **增强可追溯性**: 历史记录包含完整的文件位置信息
4. **减少用户困惑**: 明确区分不同来源的文件
