# 变量插入功能简化

## 简化说明

根据用户反馈，删除了复杂的"插入变量"功能，改为让用户手动输入变量占位符，使功能更加简洁直观。

## 删除的功能

### 1. 插入变量按钮
- **删除位置**: `TemplateManagementView.fxml` 中的 `insertVariableButton`
- **原功能**: 点击按钮弹出变量选择对话框，自动插入到光标位置

### 2. 相关控制器代码
删除了以下方法和字段：
- `insertVariableButton` 控件引用
- `insertVariable()` 方法
- `getFocusedTextControl()` 方法
- `setupTextInputFocusListeners()` 方法
- `lastActiveTextControl` 字段
- 相关的焦点监听器设置

## 保留的功能

### 1. 变量管理
- ✅ **管理变量按钮**: 仍可以定义、编辑、删除变量
- ✅ **变量预览区域**: 显示变量使用说明和占位符格式
- ✅ **变量计数**: 显示当前定义的变量数量

### 2. 变量处理
- ✅ **变量替换**: 应用模板时仍会自动处理变量替换
- ✅ **变量输入对话框**: 使用模板时仍会弹出变量值输入界面
- ✅ **预设变量**: 系统预设变量仍会自动填充

## 新的使用方式

### 1. 手动输入变量
用户现在需要在主题或正文中手动输入变量占位符：
```
主题: {{period}} 月度报告
正文: 请查收 {{period}} 的报告，确认时间：{{confirmation_time}}
```

### 2. 变量预览增强
变量预览区域现在显示更详细的使用说明：

```
使用说明：在主题或正文中手动输入以下变量占位符

自定义变量：
• {{period}} - 账期 (财务报告期间)
• {{confirmation_time}} - 确认时间

预设变量（自动填充）：
• {{current_date}} - 当前日期 (yyyy-MM-dd)
• {{current_time}} - 当前时间 (HH:mm:ss)
• {{current_datetime}} - 当前日期时间 (yyyy-MM-dd HH:mm:ss)
• {{user_name}} - 当前系统用户名
```

## 简化的优势

### 1. 用户体验
- **更直观**: 用户直接看到变量占位符的完整格式
- **更灵活**: 可以在任意位置输入变量，不受光标位置限制
- **更简单**: 减少了复杂的UI交互步骤

### 2. 代码维护
- **代码更简洁**: 删除了复杂的焦点管理和光标定位逻辑
- **减少bug**: 避免了焦点检测不准确的问题
- **易于维护**: 功能更加直接，逻辑更清晰

### 3. 学习成本
- **降低门槛**: 用户只需要记住变量格式 `{{variable_name}}`
- **自助服务**: 变量预览区域提供了完整的使用指南
- **减少困惑**: 避免了"光标定位"等技术概念

## 修改的文件

### 1. FXML文件
- **TemplateManagementView.fxml**: 删除 `insertVariableButton`

### 2. 控制器文件
- **TemplateManagementController.java**: 
  - 删除插入变量相关的字段和方法
  - 增强变量预览区域的内容显示
  - 简化初始化逻辑

## 使用流程

### 1. 创建带变量的模板
1. 点击"新建模板"
2. 点击"管理变量"定义所需变量
3. 在主题或正文中手动输入变量占位符（如 `{{period}}`）
4. 保存模板

### 2. 应用模板
1. 选择模板
2. 系统自动检测变量并显示输入对话框
3. 填写变量值
4. 确认后自动替换所有变量占位符

## 向后兼容性

- ✅ **现有模板**: 所有现有模板继续正常工作
- ✅ **变量定义**: 已定义的变量不受影响
- ✅ **变量替换**: 变量处理逻辑完全保持不变

## 总结

这次简化删除了复杂的"插入变量"功能，改为更直观的手动输入方式。用户现在可以：

1. **直接输入**: 在主题或正文中直接输入 `{{variable_name}}` 格式的占位符
2. **参考预览**: 通过变量预览区域查看所有可用变量的格式
3. **正常使用**: 应用模板时的变量替换功能完全不变

这种方式更加简洁、直观，减少了用户的学习成本和操作复杂度，同时保持了变量功能的完整性和强大性。
