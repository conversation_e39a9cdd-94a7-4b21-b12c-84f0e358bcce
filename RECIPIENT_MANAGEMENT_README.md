# 收件人管理功能说明

## 概述

本次更新为JavaFX邮件发送工具添加了完整的收件人管理功能，包括收件人信息持久化存储、快速选择、标签分组管理等高级功能。

## 新增功能特性

### 1. 收件人信息持久化存储

#### 自动保存机制
- **邮件发送成功后自动保存**：每次成功发送邮件后，系统会自动保存所有收件人和抄送人信息
- **避免重复保存**：相同邮箱地址不会重复保存，但会更新使用频次和最后使用时间
- **支持多种格式**：支持 `<EMAIL>` 和 `Name <<EMAIL>>` 两种格式

#### 存储信息
每个收件人包含以下信息：
- **邮箱地址**：唯一标识符
- **显示名称**：用于界面显示的友好名称
- **标签列表**：可分配多个自定义标签
- **使用频次**：记录该收件人被使用的次数
- **最后使用时间**：记录最近一次使用的时间
- **类型**：TO（收件人）、CC（抄送）、BCC（密送）

### 2. 收件人快速选择功能

#### 主界面增强
- **收件人ComboBox**：在"收件人"字段添加下拉选择功能
- **抄送ComboBox**：在"抄送"字段添加下拉选择功能
- **智能显示**：显示格式为 `显示名称 <邮箱地址> [标签]`
- **快速添加按钮**：点击"+"按钮快速添加选中的收件人

#### 选择方式
1. **下拉选择**：从历史记录中选择收件人
2. **直接输入**：在ComboBox中直接输入新邮箱地址
3. **批量添加**：支持通过分号或逗号分隔添加多个收件人

#### 智能排序
- **最近使用优先**：默认按最后使用时间排序
- **使用频次排序**：可按使用频次排序显示
- **标签筛选**：可按标签筛选显示特定分组的收件人

### 3. 标签（分组）管理功能

#### 标签系统
- **自定义标签**：支持创建任意名称的标签
- **多标签支持**：每个收件人可以分配多个标签
- **预设标签**：系统预设"客户"、"同事"、"朋友"、"家人"等常用标签

#### 标签操作
- **添加标签**：在收件人管理界面添加新标签
- **编辑标签**：修改现有标签名称
- **删除标签**：删除标签（会从所有收件人中移除）
- **批量分配**：为多个收件人同时分配或移除标签

#### 标签筛选
- **按标签筛选**：在收件人选择时可按标签筛选
- **标签显示**：在收件人列表中显示标签信息
- **颜色区分**：不同标签可以有不同的显示样式

### 4. 收件人管理界面

#### 管理对话框功能
- **收件人列表**：表格形式显示所有收件人信息
- **搜索功能**：支持按邮箱地址或显示名称搜索
- **标签筛选**：按标签筛选收件人
- **批量操作**：支持多选进行批量编辑或删除

#### 操作功能
- **添加收件人**：手动添加新收件人
- **编辑信息**：修改收件人的显示名称和标签
- **删除收件人**：删除不需要的收件人记录
- **标签管理**：统一管理所有标签

## 使用方法

### 基本使用

#### 1. 快速选择收件人
1. 点击"收件人"或"抄送"字段的下拉箭头
2. 从列表中选择历史收件人
3. 点击"+"按钮将选中的收件人添加到文本区域

#### 2. 直接输入新收件人
1. 在ComboBox中直接输入邮箱地址
2. 点击"+"按钮添加到文本区域
3. 系统会自动保存新输入的邮箱地址

#### 3. 批量添加收件人
1. 在文本区域直接输入多个邮箱地址
2. 使用分号(;)或逗号(,)分隔多个地址
3. 支持 `Name <<EMAIL>>` 格式

### 高级功能

#### 1. 收件人管理
1. 点击主界面的"管理"按钮
2. 打开收件人管理对话框
3. 可以查看、编辑、删除收件人信息

#### 2. 标签管理
1. 在收件人管理对话框中
2. 右侧标签管理区域可以：
   - 添加新标签
   - 编辑现有标签
   - 删除不需要的标签

#### 3. 为收件人分配标签
1. 在收件人管理对话框中选择收件人
2. 在右侧选择要分配的标签
3. 点击"添加标签"或"移除标签"按钮

#### 4. 按标签筛选
1. 在收件人管理对话框中
2. 使用"标签筛选"下拉框
3. 选择特定标签查看对应收件人

## 数据存储

### 配置文件位置
- **收件人信息**：`~/.email-sender/recipients.properties`
- **标签信息**：`~/.email-sender/recipient-tags.properties`

### 数据格式
收件人信息以Properties格式存储：
```
<EMAIL>=用户名
<EMAIL>=客户,重要
<EMAIL>=5
<EMAIL>=2025-07-01 14:30:00
<EMAIL>=TO
```

标签信息存储：
```
tag.0=客户
tag.1=同事
tag.2=朋友
tag.3=家人
```

## 界面更新

### 主界面变化
1. **收件人字段**：从单一文本框改为ComboBox + 文本区域组合
2. **抄送字段**：同样改为ComboBox + 文本区域组合
3. **新增按钮**：
   - "+" 按钮：快速添加选中的收件人
   - "管理" 按钮：打开收件人管理对话框

### 收件人管理对话框
- **左侧**：收件人列表表格，支持搜索和筛选
- **右侧**：标签管理区域
- **底部**：操作按钮（导出、导入、关闭等）

## 技术实现

### 新增文件
1. **RecipientInfo.java**：收件人信息数据模型
2. **RecipientManager.java**：收件人管理器，负责数据操作
3. **RecipientManagementController.java**：收件人管理对话框控制器
4. **RecipientManagementView.fxml**：收件人管理对话框界面
5. **RecipientManagementTest.java**：功能测试类

### 修改文件
1. **EmailSenderView.fxml**：更新主界面布局
2. **EmailSenderController.java**：集成收件人管理功能

### 架构特点
- **MVVM模式**：遵循现有的FXML + Controller架构
- **单例模式**：RecipientManager使用单例模式确保数据一致性
- **观察者模式**：UI组件监听数据变化自动更新
- **策略模式**：支持多种排序和筛选策略

## 测试

### 运行测试
```bash
mvn test -Dtest=RecipientManagementTest
```

### 测试覆盖
- 收件人添加、更新、删除
- 标签管理功能
- 邮箱地址提取和格式化
- 按标签和类型筛选
- 使用频次统计
- 数据持久化

## 注意事项

1. **数据安全**：配置文件保存在用户目录，确保数据隐私
2. **性能优化**：大量收件人时使用分页或虚拟化显示
3. **兼容性**：保持与现有发件人管理功能的一致性
4. **用户体验**：提供直观的操作界面和及时的反馈

## 未来扩展

1. **导入导出功能**：支持CSV、Excel格式的批量导入导出
2. **收件人分组**：支持更复杂的分组管理
3. **智能推荐**：基于使用历史智能推荐收件人
4. **同步功能**：支持与邮箱客户端的收件人同步
