package com.bboss.report;

import com.bboss.report.model.RecipientInfo;
import com.bboss.report.util.RecipientManager;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import java.util.List;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

/**
 * 收件人管理功能测试
 * 测试收件人信息的保存、加载、标签管理等功能
 */
public class RecipientManagementTest {

    private RecipientManager recipientManager;
    private static final String TEST_EMAIL_1 = "<EMAIL>";
    private static final String TEST_EMAIL_2 = "<EMAIL>";
    private static final String TEST_TAG_1 = "测试标签1";
    private static final String TEST_TAG_2 = "测试标签2";

    @Before
    public void setUp() {
        recipientManager = RecipientManager.getInstance();
        // 清理测试数据
        cleanupTestData();
    }

    @After
    public void tearDown() {
        // 清理测试数据
        cleanupTestData();
    }

    private void cleanupTestData() {
        recipientManager.removeRecipient(TEST_EMAIL_1);
        recipientManager.removeRecipient(TEST_EMAIL_2);
        recipientManager.removeTag(TEST_TAG_1);
        recipientManager.removeTag(TEST_TAG_2);
    }

    @Test
    public void testAddAndRetrieveRecipient() {
        // 添加收件人
        recipientManager.addOrUpdateRecipient(TEST_EMAIL_1, "测试用户1", "TO");
        
        // 验证收件人已添加
        RecipientInfo recipient = recipientManager.getRecipient(TEST_EMAIL_1);
        assertNotNull("收件人应该存在", recipient);
        assertEquals("邮箱地址应该匹配", TEST_EMAIL_1, recipient.getEmail());
        assertEquals("显示名称应该匹配", "测试用户1", recipient.getDisplayName());
        assertEquals("类型应该匹配", "TO", recipient.getType());
        assertEquals("使用次数应该为1", 1, recipient.getUseCount());
    }

    @Test
    public void testUpdateRecipientUseCount() {
        // 添加收件人
        recipientManager.addOrUpdateRecipient(TEST_EMAIL_1, "测试用户1", "TO");
        
        // 再次添加相同收件人（模拟再次发送邮件）
        recipientManager.addOrUpdateRecipient(TEST_EMAIL_1, "测试用户1", "TO");
        
        // 验证使用次数增加
        RecipientInfo recipient = recipientManager.getRecipient(TEST_EMAIL_1);
        assertEquals("使用次数应该为2", 2, recipient.getUseCount());
    }

    @Test
    public void testAddRecipientsFromEmail() {
        String recipients = "<EMAIL>; <EMAIL>, <EMAIL>";
        String ccRecipients = "<EMAIL>; <EMAIL>";
        
        // 批量添加收件人
        recipientManager.addRecipientsFromEmail(recipients, ccRecipients);
        
        // 验证主收件人
        RecipientInfo user1 = recipientManager.getRecipient("<EMAIL>");
        assertNotNull("user1应该存在", user1);
        assertEquals("user1类型应该为TO", "TO", user1.getType());
        
        RecipientInfo user2 = recipientManager.getRecipient("<EMAIL>");
        assertNotNull("user2应该存在", user2);
        assertEquals("user2类型应该为TO", "TO", user2.getType());
        
        // 验证抄送收件人
        RecipientInfo cc1 = recipientManager.getRecipient("<EMAIL>");
        assertNotNull("cc1应该存在", cc1);
        assertEquals("cc1类型应该为CC", "CC", cc1.getType());
        
        // 清理测试数据
        recipientManager.removeRecipient("<EMAIL>");
        recipientManager.removeRecipient("<EMAIL>");
        recipientManager.removeRecipient("<EMAIL>");
        recipientManager.removeRecipient("<EMAIL>");
        recipientManager.removeRecipient("<EMAIL>");
    }

    @Test
    public void testTagManagement() {
        // 添加标签
        recipientManager.addTag(TEST_TAG_1);
        recipientManager.addTag(TEST_TAG_2);
        
        // 验证标签已添加
        Set<String> tags = recipientManager.getAvailableTags();
        assertTrue("应该包含测试标签1", tags.contains(TEST_TAG_1));
        assertTrue("应该包含测试标签2", tags.contains(TEST_TAG_2));
        
        // 删除标签
        boolean removed = recipientManager.removeTag(TEST_TAG_1);
        assertTrue("应该能成功删除标签", removed);
        
        // 验证标签已删除
        Set<String> updatedTags = recipientManager.getAvailableTags();
        assertFalse("不应该包含已删除的标签", updatedTags.contains(TEST_TAG_1));
        assertTrue("应该仍包含未删除的标签", updatedTags.contains(TEST_TAG_2));
    }

    @Test
    public void testRecipientTagAssignment() {
        // 添加收件人和标签
        recipientManager.addOrUpdateRecipient(TEST_EMAIL_1, "测试用户1", "TO");
        recipientManager.addTag(TEST_TAG_1);
        recipientManager.addTag(TEST_TAG_2);
        
        // 为收件人添加标签
        recipientManager.addTagToRecipient(TEST_EMAIL_1, TEST_TAG_1);
        recipientManager.addTagToRecipient(TEST_EMAIL_1, TEST_TAG_2);
        
        // 验证标签已添加
        RecipientInfo recipient = recipientManager.getRecipient(TEST_EMAIL_1);
        assertTrue("应该包含标签1", recipient.hasTag(TEST_TAG_1));
        assertTrue("应该包含标签2", recipient.hasTag(TEST_TAG_2));
        
        // 移除标签
        recipientManager.removeTagFromRecipient(TEST_EMAIL_1, TEST_TAG_1);
        
        // 验证标签已移除
        recipient = recipientManager.getRecipient(TEST_EMAIL_1);
        assertFalse("不应该包含已移除的标签", recipient.hasTag(TEST_TAG_1));
        assertTrue("应该仍包含未移除的标签", recipient.hasTag(TEST_TAG_2));
    }

    @Test
    public void testGetRecipientsByTag() {
        // 添加收件人和标签
        recipientManager.addOrUpdateRecipient(TEST_EMAIL_1, "测试用户1", "TO");
        recipientManager.addOrUpdateRecipient(TEST_EMAIL_2, "测试用户2", "TO");
        recipientManager.addTag(TEST_TAG_1);
        
        // 为第一个收件人添加标签
        recipientManager.addTagToRecipient(TEST_EMAIL_1, TEST_TAG_1);
        
        // 按标签筛选收件人
        List<RecipientInfo> taggedRecipients = recipientManager.getRecipientsByTag(TEST_TAG_1);
        
        // 验证筛选结果
        assertEquals("应该有1个带标签的收件人", 1, taggedRecipients.size());
        assertEquals("应该是正确的收件人", TEST_EMAIL_1, taggedRecipients.get(0).getEmail());
    }

    @Test
    public void testGetRecipientsByType() {
        // 添加不同类型的收件人
        recipientManager.addOrUpdateRecipient(TEST_EMAIL_1, "测试用户1", "TO");
        recipientManager.addOrUpdateRecipient(TEST_EMAIL_2, "测试用户2", "CC");
        
        // 按类型筛选收件人
        List<RecipientInfo> toRecipients = recipientManager.getRecipientsByType("TO");
        List<RecipientInfo> ccRecipients = recipientManager.getRecipientsByType("CC");
        
        // 验证筛选结果
        assertTrue("TO类型收件人列表应该包含测试用户1", 
                  toRecipients.stream().anyMatch(r -> r.getEmail().equals(TEST_EMAIL_1)));
        assertTrue("CC类型收件人列表应该包含测试用户2", 
                  ccRecipients.stream().anyMatch(r -> r.getEmail().equals(TEST_EMAIL_2)));
    }

    @Test
    public void testMostUsedRecipients() {
        // 添加收件人并设置不同的使用次数
        recipientManager.addOrUpdateRecipient(TEST_EMAIL_1, "测试用户1", "TO");
        recipientManager.addOrUpdateRecipient(TEST_EMAIL_2, "测试用户2", "TO");
        
        // 增加第一个收件人的使用次数
        recipientManager.addOrUpdateRecipient(TEST_EMAIL_1, "测试用户1", "TO");
        recipientManager.addOrUpdateRecipient(TEST_EMAIL_1, "测试用户1", "TO");
        
        // 获取最常用的收件人
        List<RecipientInfo> mostUsed = recipientManager.getMostUsedRecipients(10);
        
        // 验证排序正确
        assertTrue("应该有收件人", mostUsed.size() >= 2);
        
        // 找到我们的测试收件人
        RecipientInfo user1 = mostUsed.stream()
                .filter(r -> r.getEmail().equals(TEST_EMAIL_1))
                .findFirst().orElse(null);
        RecipientInfo user2 = mostUsed.stream()
                .filter(r -> r.getEmail().equals(TEST_EMAIL_2))
                .findFirst().orElse(null);
        
        assertNotNull("应该找到用户1", user1);
        assertNotNull("应该找到用户2", user2);
        assertTrue("用户1的使用次数应该更多", user1.getUseCount() > user2.getUseCount());
    }

    @Test
    public void testEmailExtraction() {
        String testInput1 = "张三 <<EMAIL>>";
        String testInput2 = "<EMAIL>";
        String testInput3 = "王五<<EMAIL>>";
        
        // 测试邮箱提取（这里需要访问RecipientManager的私有方法，所以我们通过添加收件人来间接测试）
        recipientManager.addRecipientsFromEmail(testInput1 + ";" + testInput2 + ";" + testInput3, "");
        
        // 验证邮箱地址被正确提取
        RecipientInfo recipient1 = recipientManager.getRecipient("<EMAIL>");
        RecipientInfo recipient2 = recipientManager.getRecipient("<EMAIL>");
        RecipientInfo recipient3 = recipientManager.getRecipient("<EMAIL>");
        
        assertNotNull("应该正确提取邮箱1", recipient1);
        assertNotNull("应该正确提取邮箱2", recipient2);
        assertNotNull("应该正确提取邮箱3", recipient3);
        
        // 清理测试数据
        recipientManager.removeRecipient("<EMAIL>");
        recipientManager.removeRecipient("<EMAIL>");
        recipientManager.removeRecipient("<EMAIL>");
    }

    @Test
    public void testRemoveRecipient() {
        // 添加收件人
        recipientManager.addOrUpdateRecipient(TEST_EMAIL_1, "测试用户1", "TO");
        
        // 验证收件人存在
        assertNotNull("收件人应该存在", recipientManager.getRecipient(TEST_EMAIL_1));
        
        // 删除收件人
        boolean removed = recipientManager.removeRecipient(TEST_EMAIL_1);
        assertTrue("应该能成功删除收件人", removed);
        
        // 验证收件人已删除
        assertNull("收件人应该已被删除", recipientManager.getRecipient(TEST_EMAIL_1));
    }
}
