package com.bboss.report;

import com.bboss.report.util.ConfigManager;
import org.junit.Test;

import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * 邮件发送增强功能测试
 * 测试发件人邮箱管理和错误处理功能
 */
public class EmailSenderEnhancementTest {

    @Test
    public void testSenderEmailManagement() {
        ConfigManager configManager = ConfigManager.getInstance();
        
        // 测试添加发件人邮箱
        String testEmail = "<EMAIL>";
        boolean added = configManager.addSenderEmail(testEmail);
        assertTrue("应该能成功添加邮箱", added);
        
        // 测试获取保存的邮箱列表
        List<String> savedEmails = configManager.getSavedSenderEmails();
        assertTrue("保存的邮箱列表应该包含测试邮箱", savedEmails.contains(testEmail));
        
        // 测试删除邮箱
        boolean removed = configManager.removeSenderEmail(testEmail);
        assertTrue("应该能成功删除邮箱", removed);
        
        // 验证邮箱已被删除
        List<String> updatedEmails = configManager.getSavedSenderEmails();
        assertFalse("删除后的邮箱列表不应该包含测试邮箱", updatedEmails.contains(testEmail));
    }
    
    @Test
    public void testDuplicateEmailHandling() {
        ConfigManager configManager = ConfigManager.getInstance();
        
        String testEmail = "<EMAIL>";
        
        // 添加邮箱两次
        configManager.addSenderEmail(testEmail);
        configManager.addSenderEmail(testEmail);
        
        // 获取邮箱列表
        List<String> savedEmails = configManager.getSavedSenderEmails();
        
        // 计算重复邮箱的数量
        long count = savedEmails.stream().filter(email -> email.equals(testEmail)).count();
        assertEquals("重复的邮箱应该只保存一次", 1, count);
        
        // 清理
        configManager.removeSenderEmail(testEmail);
    }
    
    @Test
    public void testEmptyEmailHandling() {
        ConfigManager configManager = ConfigManager.getInstance();
        
        // 测试添加空邮箱
        boolean addedNull = configManager.addSenderEmail(null);
        assertFalse("不应该能添加null邮箱", addedNull);
        
        boolean addedEmpty = configManager.addSenderEmail("");
        assertFalse("不应该能添加空字符串邮箱", addedEmpty);
        
        boolean addedWhitespace = configManager.addSenderEmail("   ");
        assertFalse("不应该能添加只有空格的邮箱", addedWhitespace);
    }
}
