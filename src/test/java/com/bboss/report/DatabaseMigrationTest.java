package com.bboss.report;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 数据库迁移功能测试类
 * 测试从旧版本数据库结构升级到新版本的迁移过程
 * 
 * <AUTHOR>
 * @create 2025/7/26
 * @since 1.0.0
 */
public class DatabaseMigrationTest {
    
    private static final String TEST_DB_DIR = System.getProperty("user.home") + "/.email-sender-test";
    private static final String TEST_DB_FILE = TEST_DB_DIR + "/templates_test.db3";
    private static final String TEST_JDBC_URL = "jdbc:sqlite:" + TEST_DB_FILE;
    
    @BeforeEach
    public void setUp() {
        // 清理测试数据库
        cleanupTestDatabase();
    }
    
    @AfterEach
    public void tearDown() {
        // 清理测试数据库
        cleanupTestDatabase();
    }
    
    private void cleanupTestDatabase() {
        File testDbFile = new File(TEST_DB_FILE);
        if (testDbFile.exists()) {
            testDbFile.delete();
        }
        File testDbDir = new File(TEST_DB_DIR);
        if (testDbDir.exists()) {
            testDbDir.delete();
        }
    }
    
    @Test
    public void testMigrationFromOldSchema() throws SQLException {
        // 1. 创建旧版本的数据库结构（没有 variables 列）
        createOldSchemaDatabase();
        
        // 2. 插入一些测试数据
        insertTestDataInOldSchema();
        
        // 3. 验证旧数据存在且没有 variables 列
        verifyOldSchemaAndData();
        
        // 4. 触发迁移（通过创建 TemplateManager 实例）
        // 注意：这里需要修改 TemplateManager 以支持测试数据库路径
        // 或者直接调用迁移方法
        performMigrationTest();
        
        // 5. 验证迁移后的结果
        verifyMigrationResults();
    }
    
    private void createOldSchemaDatabase() throws SQLException {
        // 确保测试目录存在
        File testDir = new File(TEST_DB_DIR);
        if (!testDir.exists()) {
            testDir.mkdirs();
        }
        
        // 创建旧版本的表结构（没有 variables 列）
        String createOldTableSQL = "CREATE TABLE email_templates (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
                "name TEXT NOT NULL UNIQUE, " +
                "recipients TEXT, " +
                "cc_recipients TEXT, " +
                "subject TEXT, " +
                "body TEXT, " +
                "file_prefixes TEXT, " +
                "file_extensions TEXT, " +
                "description TEXT, " +
                "created_time TEXT NOT NULL, " +
                "last_modified TEXT NOT NULL, " +
                "use_count INTEGER DEFAULT 0, " +
                "last_used TEXT, " +
                "enabled INTEGER DEFAULT 1" +
                ")";
        
        try (Connection conn = DriverManager.getConnection(TEST_JDBC_URL);
             Statement stmt = conn.createStatement()) {
            
            stmt.execute(createOldTableSQL);
        }
    }
    
    private void insertTestDataInOldSchema() throws SQLException {
        String insertSQL = "INSERT INTO email_templates " +
                "(name, recipients, subject, body, description, created_time, last_modified, use_count, enabled) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = DriverManager.getConnection(TEST_JDBC_URL);
             PreparedStatement pstmt = conn.prepareStatement(insertSQL)) {
            
            // 插入测试模板1
            pstmt.setString(1, "测试模板1");
            pstmt.setString(2, "<EMAIL>");
            pstmt.setString(3, "测试主题1");
            pstmt.setString(4, "测试正文1");
            pstmt.setString(5, "测试描述1");
            pstmt.setString(6, LocalDateTime.now().toString());
            pstmt.setString(7, LocalDateTime.now().toString());
            pstmt.setInt(8, 0);
            pstmt.setInt(9, 1);
            pstmt.executeUpdate();
            
            // 插入测试模板2
            pstmt.setString(1, "测试模板2");
            pstmt.setString(2, "<EMAIL>");
            pstmt.setString(3, "测试主题2 {{variable1}}");
            pstmt.setString(4, "测试正文2 包含变量 {{variable1}} 和 {{variable2}}");
            pstmt.setString(5, "测试描述2");
            pstmt.setString(6, LocalDateTime.now().toString());
            pstmt.setString(7, LocalDateTime.now().toString());
            pstmt.setInt(8, 5);
            pstmt.setInt(9, 1);
            pstmt.executeUpdate();
        }
    }
    
    private void verifyOldSchemaAndData() throws SQLException {
        try (Connection conn = DriverManager.getConnection(TEST_JDBC_URL)) {
            
            // 验证 variables 列不存在
            assertFalse(columnExists(conn, "email_templates", "variables"), 
                       "variables 列不应该在旧版本数据库中存在");
            
            // 验证数据存在
            String countSQL = "SELECT COUNT(*) FROM email_templates";
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(countSQL)) {
                
                assertTrue(rs.next());
                assertEquals(2, rs.getInt(1), "应该有2条测试数据");
            }
            
            // 验证具体数据
            String selectSQL = "SELECT name, subject, body FROM email_templates ORDER BY name";
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(selectSQL)) {
                
                assertTrue(rs.next());
                assertEquals("测试模板1", rs.getString("name"));
                assertEquals("测试主题1", rs.getString("subject"));
                
                assertTrue(rs.next());
                assertEquals("测试模板2", rs.getString("name"));
                assertEquals("测试主题2 {{variable1}}", rs.getString("subject"));
                assertTrue(rs.getString("body").contains("{{variable1}}"));
                assertTrue(rs.getString("body").contains("{{variable2}}"));
            }
        }
    }
    
    private void performMigrationTest() throws SQLException {
        // 直接调用迁移逻辑
        try (Connection conn = DriverManager.getConnection(TEST_JDBC_URL)) {
            
            // 模拟 TemplateManager 的迁移逻辑
            if (!columnExists(conn, "email_templates", "variables")) {
                addVariablesColumn(conn);
            }
        }
    }
    
    private void verifyMigrationResults() throws SQLException {
        try (Connection conn = DriverManager.getConnection(TEST_JDBC_URL)) {
            
            // 验证 variables 列已添加
            assertTrue(columnExists(conn, "email_templates", "variables"), 
                      "variables 列应该在迁移后存在");
            
            // 验证原有数据完整性
            String countSQL = "SELECT COUNT(*) FROM email_templates";
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(countSQL)) {
                
                assertTrue(rs.next());
                assertEquals(2, rs.getInt(1), "迁移后应该仍有2条数据");
            }
            
            // 验证原有数据内容未变
            String selectSQL = "SELECT name, subject, body, variables FROM email_templates ORDER BY name";
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(selectSQL)) {
                
                assertTrue(rs.next());
                assertEquals("测试模板1", rs.getString("name"));
                assertEquals("测试主题1", rs.getString("subject"));
                assertEquals("", rs.getString("variables")); // 默认为空字符串
                
                assertTrue(rs.next());
                assertEquals("测试模板2", rs.getString("name"));
                assertEquals("测试主题2 {{variable1}}", rs.getString("subject"));
                assertTrue(rs.getString("body").contains("{{variable1}}"));
                assertEquals("", rs.getString("variables")); // 默认为空字符串
            }
            
            // 验证可以插入新数据（包含 variables 列）
            String insertSQL = "INSERT INTO email_templates " +
                    "(name, subject, body, variables, created_time, last_modified, use_count, enabled) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            
            try (PreparedStatement pstmt = conn.prepareStatement(insertSQL)) {
                pstmt.setString(1, "新模板");
                pstmt.setString(2, "新主题 {{test_var}}");
                pstmt.setString(3, "新正文 {{test_var}}");
                pstmt.setString(4, "[{\"name\":\"test_var\",\"label\":\"测试变量\",\"type\":\"TEXT\"}]");
                pstmt.setString(5, LocalDateTime.now().toString());
                pstmt.setString(6, LocalDateTime.now().toString());
                pstmt.setInt(7, 0);
                pstmt.setInt(8, 1);
                
                int result = pstmt.executeUpdate();
                assertEquals(1, result, "应该成功插入新记录");
            }
        }
    }
    
    // 辅助方法：检查列是否存在
    private boolean columnExists(Connection conn, String tableName, String columnName) throws SQLException {
        String sql = "PRAGMA table_info(" + tableName + ")";
        
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            while (rs.next()) {
                String existingColumnName = rs.getString("name");
                if (columnName.equalsIgnoreCase(existingColumnName)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    // 辅助方法：添加 variables 列
    private void addVariablesColumn(Connection conn) throws SQLException {
        String alterTableSQL = "ALTER TABLE email_templates ADD COLUMN variables TEXT";
        
        try (Statement stmt = conn.createStatement()) {
            stmt.execute(alterTableSQL);
            
            // 为现有记录设置默认值
            String updateSQL = "UPDATE email_templates SET variables = '' WHERE variables IS NULL";
            stmt.execute(updateSQL);
        }
    }
}
