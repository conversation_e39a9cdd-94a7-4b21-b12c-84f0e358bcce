package com.bboss.report;

import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Stage;

import java.io.IOException;
import java.net.URL;

/**
 * 〈一句话功能简述〉<br>
 * 〈〉
 *
 * <AUTHOR>
 * @create 2025/6/30
 * @since 1.0.0
 */
public class EmailSenderTest extends Application {
    @Override
    public void start(Stage stage) throws IOException {
        // 1. 构造 FXMLLoader
        // 注意：确保 FXML 文件与这个类在同一个包路径下，或者提供正确的资源路径
        URL fxmlLocation = getClass().getResource("aaa.fxml");
        if (fxmlLocation == null) {
            System.err.println("错误: 无法在 classpath 中找到 FXML 文件。请检查路径。");
            return;
        }
        FXMLLoader fxmlLoader = new FXMLLoader(fxmlLocation);

        // 2. 加载 FXML 并创建场景
        // 注意：不需要手动设置控制器，因为 FXML 文件中已经通过 fx:controller 属性指定了控制器
        Parent root = fxmlLoader.load();
        Scene scene = new Scene(root, 800, 900);

        // 3. 设置并显示舞台 (Stage)
        stage.setTitle("邮件发送工具");
        stage.setMinWidth(650);
        stage.setMinHeight(750);
        stage.setScene(scene);
        stage.show();
    }

    public static void main(String[] args) {
        launch(args);
    }

}