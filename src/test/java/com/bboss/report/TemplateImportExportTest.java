package com.bboss.report;

import com.bboss.report.model.EmailTemplate;
import com.bboss.report.util.TemplateManager;
import javafx.application.Application;
import javafx.application.Platform;
import javafx.stage.Stage;
import org.junit.Test;

import java.io.File;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 模板导入导出功能测试类
 * 
 * <AUTHOR>
 * @create 2025/7/3
 * @since 1.0.0
 */
public class TemplateImportExportTest extends Application {

    @Override
    public void start(Stage primaryStage) throws Exception {
        // 在JavaFX应用线程中运行测试
        Platform.runLater(() -> {
            try {
                testTemplateImportExport();
                System.out.println("模板导入导出测试完成！");
                Platform.exit();
            } catch (Exception e) {
                e.printStackTrace();
                Platform.exit();
            }
        });
    }

    /**
     * 测试模板导入导出功能
     */
    private void testTemplateImportExport() {
        TemplateManager templateManager = TemplateManager.getInstance();
        
        // 创建测试模板
        EmailTemplate template1 = createTestTemplate("测试模板1", "这是第一个测试模板");
        EmailTemplate template2 = createTestTemplate("测试模板2", "这是第二个测试模板");
        
        List<EmailTemplate> testTemplates = Arrays.asList(template1, template2);
        
        // 测试导出
        File exportFile = new File(System.getProperty("user.home"), "test_templates_export.xml");
        System.out.println("开始导出测试...");
        boolean exportSuccess = templateManager.exportTemplatesToXML(testTemplates, exportFile);
        
        if (exportSuccess) {
            System.out.println("✓ 导出成功: " + exportFile.getAbsolutePath());
        } else {
            System.out.println("✗ 导出失败");
            return;
        }
        
        // 测试导入
        System.out.println("开始导入测试...");
        List<EmailTemplate> importedTemplates = templateManager.importTemplatesFromXML(exportFile);
        
        if (importedTemplates != null && !importedTemplates.isEmpty()) {
            System.out.println("✓ 导入成功，共导入 " + importedTemplates.size() + " 个模板");
            
            // 显示导入的模板信息
            for (EmailTemplate template : importedTemplates) {
                System.out.println("  - " + template.getName() + ": " + template.getDescription());
            }
        } else {
            System.out.println("✗ 导入失败");
            return;
        }
        
        // 测试批量保存
        System.out.println("开始批量保存测试...");
        int savedCount = templateManager.batchSaveTemplates(importedTemplates, true);
        System.out.println("✓ 批量保存完成，成功保存 " + savedCount + " 个模板");
        
        // 验证保存结果
        List<EmailTemplate> allTemplates = templateManager.getAllTemplates();
        System.out.println("数据库中现有模板数量: " + allTemplates.size());
        
        // 清理测试文件
        if (exportFile.exists()) {
            exportFile.delete();
            System.out.println("✓ 清理测试文件完成");
        }
    }
    
    /**
     * 创建测试模板
     */
    private EmailTemplate createTestTemplate(String name, String description) {
        EmailTemplate template = new EmailTemplate();
        template.setName(name);
        template.setDescription(description);
        template.setRecipients("<EMAIL>");
        template.setCcRecipients("<EMAIL>");
        template.setSubject("测试邮件主题 - " + name);
        template.setBody("这是一个测试邮件内容。\n\n模板名称: " + name + "\n描述: " + description);
        template.setFilePrefixes("test,demo");
        template.setFileExtensions("txt,pdf,doc");
        template.setEnabled(true);
        template.setCreatedTime(LocalDateTime.now());
        template.setLastModified(LocalDateTime.now());
        
        return template;
    }

    public static void main(String[] args) {
        System.out.println("启动模板导入导出功能测试...");
        launch(args);
    }

    @Test
    public void test2() {
        //不要将中文替换
        System.out.println("省专报表".replaceAll("[^a-zA-Z0-9]", "_"));
        String s = "省专报表".replaceAll("[^a-zA-Z0-9]", "_");
        System.out.println(s);
    }
}
