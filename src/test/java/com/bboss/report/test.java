package com.bboss.report;

import com.bboss.report.service.BaseService;
import com.bboss.report.service.ReportPartnersService;
import com.bboss.report.util.StringUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.List;
import java.util.Map;

public class test {
    public static void main(String[] args) {
//        String value = "制表单位:中国移动信息技术中心     制表人:                     IT中心部门审核：                   ";
//        Boolean b=StringUtil.StringContains(value, "制表人") &&
//                StringUtil.StringContains(value, "IT中心部门审核") &&
//                StringUtil.StringContains(value, "制表单位");
//
//        System.out.println("两数相乘的结果是: " + b);

//        Integer[] columns= new Integer[6];
//        BaseService bs=new ReportPartnersService();
//        List<Map<String, Object>> columnDataList2 = bs.getColumnDataList2("C:\\HPE\\创新产品\\结算稽核工具\\测试用\\中国移动CDN合作伙伴分省结算单(西藏)_202312.xls",
//                null, 5, columns);
//        System.out.println(columnDataList2.toString());


    }
}
