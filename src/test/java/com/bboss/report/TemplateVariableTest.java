package com.bboss.report;

import com.bboss.report.model.TemplateVariable;
import com.bboss.report.util.TemplateVariableProcessor;
import org.junit.jupiter.api.Test;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 模板变量功能测试类
 * 
 * <AUTHOR>
 * @create 2025/7/26
 * @since 1.0.0
 */
public class TemplateVariableTest {
    
    @Test
    public void testVariableExtraction() {
        String subject = "{{billing_period}} 月度报告";
        String body = "尊敬的用户，\n\n请查收 {{billing_period}} 的报告。\n确认时间：{{confirmation_time}}\n\n谢谢！";
        
        Set<String> variables = TemplateVariableProcessor.extractVariableNames(subject, body);
        
        assertEquals(2, variables.size());
        assertTrue(variables.contains("billing_period"));
        assertTrue(variables.contains("confirmation_time"));
    }
    
    @Test
    public void testVariableReplacement() {
        String template = "{{billing_period}} 月度报告 - 确认时间：{{confirmation_time}}";
        
        Map<String, String> values = new HashMap<>();
        values.put("billing_period", "2025年1月");
        values.put("confirmation_time", "2025-01-26 10:30:00");
        
        String result = TemplateVariableProcessor.replaceVariables(template, values);
        
        assertEquals("2025年1月 月度报告 - 确认时间：2025-01-26 10:30:00", result);
    }
    
    @Test
    public void testPresetVariables() {
        String template = "当前日期：{{current_date}}，用户：{{user_name}}";
        
        Map<String, String> values = new HashMap<>();
        String result = TemplateVariableProcessor.replaceVariables(template, values);
        
        // 预设变量应该被自动替换
        assertFalse(result.contains("{{current_date}}"));
        assertFalse(result.contains("{{user_name}}"));
        assertTrue(result.contains("当前日期："));
        assertTrue(result.contains("用户："));
    }
    
    @Test
    public void testVariableSerialization() {
        List<TemplateVariable> variables = new ArrayList<>();
        
        TemplateVariable var1 = new TemplateVariable();
        var1.setName("billing_period");
        var1.setLabel("账期");
        var1.setType(TemplateVariable.VariableType.TEXT);
        var1.setRequired(true);
        var1.setDescription("财务账期");
        
        TemplateVariable var2 = new TemplateVariable();
        var2.setName("confirmation_time");
        var2.setLabel("确认时间");
        var2.setType(TemplateVariable.VariableType.DATE);
        var2.setRequired(false);
        var2.setDefaultValue("2025-01-01");
        
        variables.add(var1);
        variables.add(var2);
        
        // 序列化
        String json = TemplateVariableProcessor.serializeVariables(variables);
        assertNotNull(json);
        assertFalse(json.isEmpty());
        
        // 反序列化
        List<TemplateVariable> deserializedVars = TemplateVariableProcessor.deserializeVariables(json);
        assertEquals(2, deserializedVars.size());
        
        TemplateVariable deserializedVar1 = deserializedVars.get(0);
        assertEquals("billing_period", deserializedVar1.getName());
        assertEquals("账期", deserializedVar1.getLabel());
        assertEquals(TemplateVariable.VariableType.TEXT, deserializedVar1.getType());
        assertTrue(deserializedVar1.isRequired());
        assertEquals("财务账期", deserializedVar1.getDescription());
    }
    
    @Test
    public void testVariableValidation() {
        List<TemplateVariable> variables = new ArrayList<>();
        
        TemplateVariable emailVar = new TemplateVariable();
        emailVar.setName("email");
        emailVar.setLabel("邮箱");
        emailVar.setType(TemplateVariable.VariableType.EMAIL);
        emailVar.setRequired(true);
        

        
        variables.add(emailVar);

        // 测试有效值
        Map<String, String> validValues = new HashMap<>();
        validValues.put("email", "<EMAIL>");
        validValues.put("amount", "123.45");
        
        TemplateVariableProcessor.ValidationResult validResult = 
            TemplateVariableProcessor.validateVariableValues(variables, validValues);
        assertTrue(validResult.isValid());
        
        // 测试无效值
        Map<String, String> invalidValues = new HashMap<>();
        invalidValues.put("email", "invalid-email");
        invalidValues.put("amount", "not-a-number");
        
        TemplateVariableProcessor.ValidationResult invalidResult = 
            TemplateVariableProcessor.validateVariableValues(variables, invalidValues);
        assertFalse(invalidResult.isValid());
        assertEquals(2, invalidResult.getErrors().size());
    }
    
    @Test
    public void testVariableNameValidation() {
        assertTrue(TemplateVariable.isValidVariableName("valid_name"));
        assertTrue(TemplateVariable.isValidVariableName("validName123"));
        assertTrue(TemplateVariable.isValidVariableName("_underscore"));
        
        assertFalse(TemplateVariable.isValidVariableName("123invalid"));
        assertFalse(TemplateVariable.isValidVariableName("invalid-name"));
        assertFalse(TemplateVariable.isValidVariableName("invalid name"));
        assertFalse(TemplateVariable.isValidVariableName(""));
        assertFalse(TemplateVariable.isValidVariableName(null));
    }
    
    @Test
    public void testCompleteWorkflow() {
        // 创建模板变量
        List<TemplateVariable> variables = new ArrayList<>();
        
        TemplateVariable periodVar = new TemplateVariable();
        periodVar.setName("period");
        periodVar.setLabel("报告期间");
        periodVar.setType(TemplateVariable.VariableType.TEXT);
        periodVar.setRequired(true);
        
        variables.add(periodVar);
        
        // 模板内容
        String subject = "{{period}} 财务报告";
        String body = "请查收 {{period}} 的财务报告。\n生成时间：{{current_datetime}}";
        
        // 用户输入值
        Map<String, String> userValues = new HashMap<>();
        userValues.put("period", "2025年第一季度");
        
        // 验证输入
        TemplateVariableProcessor.ValidationResult validation = 
            TemplateVariableProcessor.validateVariableValues(variables, userValues);
        assertTrue(validation.isValid());
        
        // 替换变量
        String processedSubject = TemplateVariableProcessor.replaceVariables(subject, userValues);
        String processedBody = TemplateVariableProcessor.replaceVariables(body, userValues);
        
        assertEquals("2025年第一季度 财务报告", processedSubject);
        assertTrue(processedBody.contains("2025年第一季度"));
        assertFalse(processedBody.contains("{{current_datetime}}"));
    }

    @Test
    public void test2() {
        String reg = "S201_中国移动带宽型业务省间结算单.+_特殊分表\\.xls";
        String name = "S201_中国移动带宽型业务省间结算单_202506_特殊分表.xls";
        System.out.println(name.matches(reg));
    }
}
