package com.bboss.report;

import com.bboss.report.model.EmailSettings;
import com.bboss.report.util.ConfigManager;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.util.Properties;
import java.util.Scanner;

/**
 * 简化的邮件发送测试类
 * 不依赖JavaFX，使用控制台界面测试邮件发送功能
 * 
 * <AUTHOR>
 * @create 2025/6/30
 * @since 1.0.0
 */
public class SimpleEmailSenderTest {
    
    private static ConfigManager configManager = ConfigManager.getInstance();
    private static Scanner scanner = new Scanner(System.in);
    
    public static void main(String[] args) {
        System.out.println("=== 简化邮件发送工具测试 ===");
        
        while (true) {
            showMenu();
            int choice = getChoice();
            
            switch (choice) {
                case 1:
                    configureEmailSettings();
                    break;
                case 2:
                    viewCurrentSettings();
                    break;
                case 3:
                    testConnection();
                    break;
                case 4:
                    sendTestEmail();
                    break;
                case 5:
                    System.out.println("退出程序。");
                    return;
                default:
                    System.out.println("无效选择，请重新输入。");
            }
        }
    }
    
    private static void showMenu() {
        System.out.println("\n=== 菜单 ===");
        System.out.println("1. 配置邮件设置");
        System.out.println("2. 查看当前设置");
        System.out.println("3. 测试连接");
        System.out.println("4. 发送测试邮件");
        System.out.println("5. 退出");
        System.out.print("请选择操作 (1-5): ");
    }
    
    private static int getChoice() {
        try {
            return Integer.parseInt(scanner.nextLine());
        } catch (NumberFormatException e) {
            return -1;
        }
    }
    
    private static void configureEmailSettings() {
        System.out.println("\n=== 配置邮件设置 ===");
        
        EmailSettings settings = new EmailSettings();
        
        System.out.print("SMTP服务器地址 (默认: smtp.139.com): ");
        String smtpHost = scanner.nextLine().trim();
        if (!smtpHost.isEmpty()) {
            settings.setSmtpHost(smtpHost);
        }
        
        System.out.print("SMTP端口号 (默认: 465): ");
        String portStr = scanner.nextLine().trim();
        if (!portStr.isEmpty()) {
            try {
                settings.setSmtpPort(Integer.parseInt(portStr));
            } catch (NumberFormatException e) {
                System.out.println("端口号格式错误，使用默认值 587");
            }
        }
        
        System.out.print("发件人邮箱地址: ");
        String email = scanner.nextLine().trim();
        settings.setSenderEmail(email);
        
        System.out.print("邮箱密码/授权码: ");
        String password = scanner.nextLine().trim();
        settings.setSenderPassword(password);
        
        System.out.print("发件人显示名称 (可选): ");
        String name = scanner.nextLine().trim();
        settings.setSenderName(name);
        
        System.out.print("启用SSL/TLS加密? (y/n, 默认: y): ");
        String sslChoice = scanner.nextLine().trim().toLowerCase();
        settings.setEnableSSL(!sslChoice.equals("n"));
        
        if (settings.isValid()) {
            boolean saved = configManager.saveSettings(settings);
            if (saved) {
                System.out.println("✓ 邮件设置保存成功！");
            } else {
                System.out.println("✗ 邮件设置保存失败！");
            }
        } else {
            System.out.println("✗ 配置信息不完整，请确保填写所有必需字段。");
        }
    }
    
    private static void viewCurrentSettings() {
        System.out.println("\n=== 当前邮件设置 ===");
        EmailSettings settings = configManager.getCurrentSettings();
        
        System.out.println("SMTP服务器: " + settings.getSmtpHost());
        System.out.println("SMTP端口: " + settings.getSmtpPort());
        System.out.println("发件人邮箱: " + settings.getSenderEmail());
        System.out.println("发件人名称: " + (settings.getSenderName().isEmpty() ? "未设置" : settings.getSenderName()));
        System.out.println("SSL/TLS加密: " + (settings.isEnableSSL() ? "启用" : "禁用"));
        System.out.println("配置有效性: " + (settings.isValid() ? "✓ 有效" : "✗ 无效"));
    }
    
    private static void testConnection() {
        System.out.println("\n=== 测试邮件服务器连接 ===");
        
        EmailSettings settings = configManager.getCurrentSettings();
        if (!settings.isValid()) {
            System.out.println("✗ 邮件配置无效，请先配置邮件设置。");
            return;
        }
        
        try {
            Properties props = new Properties();
            props.put("mail.smtp.host", settings.getSmtpHost());
            props.put("mail.smtp.port", String.valueOf(settings.getSmtpPort()));
            props.put("mail.smtp.auth", "true");
            
            if (settings.isEnableSSL()) {
                props.put("mail.smtp.starttls.enable", "true");
                props.put("mail.smtp.ssl.enable", "true");
            }
            
            Session session = Session.getInstance(props, new Authenticator() {
                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(settings.getSenderEmail(), settings.getSenderPassword());
                }
            });
            
            Transport transport = session.getTransport("smtp");
            transport.connect();
            transport.close();
            
            System.out.println("✓ 连接测试成功！邮件服务器配置正确。");
            
        } catch (Exception e) {
            System.out.println("✗ 连接测试失败: " + e.getMessage());
        }
    }
    
    private static void sendTestEmail() {
        System.out.println("\n=== 发送测试邮件 ===");
        
        EmailSettings settings = configManager.getCurrentSettings();
        if (!settings.isValid()) {
            System.out.println("✗ 邮件配置无效，请先配置邮件设置。");
            return;
        }
        
        System.out.print("收件人邮箱地址: ");
        String recipient = scanner.nextLine().trim();
        if (recipient.isEmpty()) {
            System.out.println("✗ 收件人邮箱地址不能为空。");
            return;
        }
        
        System.out.print("邮件主题 (默认: 测试邮件): ");
        String subject = scanner.nextLine().trim();
        if (subject.isEmpty()) {
            subject = "测试邮件";
        }
        
        System.out.print("邮件内容 (默认: 这是一封测试邮件): ");
        String content = scanner.nextLine().trim();
        if (content.isEmpty()) {
            content = "这是一封测试邮件，用于验证邮件发送功能是否正常工作。\n\n发送时间: " + new java.util.Date();
        }
        
        try {
            Properties props = new Properties();
            props.put("mail.smtp.host", settings.getSmtpHost());
            props.put("mail.smtp.port", String.valueOf(settings.getSmtpPort()));
            props.put("mail.smtp.auth", "true");
            
            if (settings.isEnableSSL()) {
                props.put("mail.smtp.starttls.enable", "true");
                props.put("mail.smtp.ssl.enable", "true");
            }
            
            Session session = Session.getInstance(props, new Authenticator() {
                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(settings.getSenderEmail(), settings.getSenderPassword());
                }
            });
            
            Message message = new MimeMessage(session);
            
            // 设置发件人
            if (settings.getSenderName() != null && !settings.getSenderName().trim().isEmpty()) {
                message.setFrom(new InternetAddress(settings.getSenderEmail(), settings.getSenderName()));
            } else {
                message.setFrom(new InternetAddress(settings.getSenderEmail()));
            }
            
            // 设置收件人
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(recipient));
            
            // 设置主题和内容
            message.setSubject(subject);
            message.setText(content);
            
            // 发送邮件
            Transport.send(message);
            
            System.out.println("✓ 邮件发送成功！");
            System.out.println("  收件人: " + recipient);
            System.out.println("  主题: " + subject);
            
        } catch (Exception e) {
            System.out.println("✗ 邮件发送失败: " + e.getMessage());
        }
    }
}
