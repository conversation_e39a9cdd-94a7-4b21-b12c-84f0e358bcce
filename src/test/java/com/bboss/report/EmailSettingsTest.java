package com.bboss.report;

import com.bboss.report.model.EmailSettings;
import com.bboss.report.util.ConfigManager;

/**
 * 邮件设置功能测试类
 * 用于测试配置的保存和加载功能
 * 
 * <AUTHOR>
 * @create 2025/6/30
 * @since 1.0.0
 */
public class EmailSettingsTest {
    
    public static void main(String[] args) {
        System.out.println("=== 邮件设置功能测试 ===");
        
        // 测试配置管理器
        testConfigManager();
        
        // 测试邮件设置验证
        testEmailSettingsValidation();
        
        System.out.println("=== 测试完成 ===");
    }
    
    /**
     * 测试配置管理器的保存和加载功能
     */
    private static void testConfigManager() {
        System.out.println("\n--- 测试配置管理器 ---");
        
        ConfigManager configManager = ConfigManager.getInstance();
        
        // 创建测试配置
        EmailSettings testSettings = new EmailSettings();
        testSettings.setSmtpHost("smtp.139.com");
        testSettings.setSmtpPort(465);
        testSettings.setSenderEmail("<EMAIL>");
        testSettings.setSenderPassword("test_password");
        testSettings.setEnableSSL(true);
        testSettings.setSenderName("测试发件人");
        
        System.out.println("原始配置: " + testSettings);
        
        // 保存配置
        boolean saved = configManager.saveSettings(testSettings);
        System.out.println("保存结果: " + (saved ? "成功" : "失败"));
        
        // 加载配置
        EmailSettings loadedSettings = configManager.loadSettings();
        System.out.println("加载的配置: " + loadedSettings);
        
        // 验证配置是否一致
        boolean isConsistent = testSettings.getSmtpHost().equals(loadedSettings.getSmtpHost()) &&
                              testSettings.getSmtpPort() == loadedSettings.getSmtpPort() &&
                              testSettings.getSenderEmail().equals(loadedSettings.getSenderEmail()) &&
                              testSettings.getSenderPassword().equals(loadedSettings.getSenderPassword()) &&
                              testSettings.isEnableSSL() == loadedSettings.isEnableSSL() &&
                              testSettings.getSenderName().equals(loadedSettings.getSenderName());
        
        System.out.println("配置一致性检查: " + (isConsistent ? "通过" : "失败"));
        
        // 显示配置文件路径
        System.out.println("配置文件路径: " + configManager.getConfigFilePath());
    }
    
    /**
     * 测试邮件设置的验证功能
     */
    private static void testEmailSettingsValidation() {
        System.out.println("\n--- 测试邮件设置验证 ---");
        
        // 测试有效配置
        EmailSettings validSettings = new EmailSettings();
        validSettings.setSmtpHost("smtp.qq.com");
        validSettings.setSmtpPort(587);
        validSettings.setSenderEmail("<EMAIL>");
        validSettings.setSenderPassword("password123");
        validSettings.setEnableSSL(true);
        
        System.out.println("有效配置验证: " + (validSettings.isValid() ? "通过" : "失败"));
        
        // 测试无效配置 - 空的SMTP主机
        EmailSettings invalidSettings1 = new EmailSettings();
        invalidSettings1.setSmtpHost("");
        invalidSettings1.setSmtpPort(587);
        invalidSettings1.setSenderEmail("<EMAIL>");
        invalidSettings1.setSenderPassword("password123");
        
        System.out.println("无效配置1（空SMTP主机）验证: " + (!invalidSettings1.isValid() ? "通过" : "失败"));
        
        // 测试无效配置 - 无效端口
        EmailSettings invalidSettings2 = new EmailSettings();
        invalidSettings2.setSmtpHost("smtp.qq.com");
        invalidSettings2.setSmtpPort(0);
        invalidSettings2.setSenderEmail("<EMAIL>");
        invalidSettings2.setSenderPassword("password123");
        
        System.out.println("无效配置2（无效端口）验证: " + (!invalidSettings2.isValid() ? "通过" : "失败"));
        
        // 测试无效配置 - 空邮箱地址
        EmailSettings invalidSettings3 = new EmailSettings();
        invalidSettings3.setSmtpHost("smtp.qq.com");
        invalidSettings3.setSmtpPort(587);
        invalidSettings3.setSenderEmail("");
        invalidSettings3.setSenderPassword("password123");
        
        System.out.println("无效配置3（空邮箱地址）验证: " + (!invalidSettings3.isValid() ? "通过" : "失败"));
        
        // 测试无效配置 - 空密码
        EmailSettings invalidSettings4 = new EmailSettings();
        invalidSettings4.setSmtpHost("smtp.qq.com");
        invalidSettings4.setSmtpPort(587);
        invalidSettings4.setSenderEmail("<EMAIL>");
        invalidSettings4.setSenderPassword("");
        
        System.out.println("无效配置4（空密码）验证: " + (!invalidSettings4.isValid() ? "通过" : "失败"));
    }
}
