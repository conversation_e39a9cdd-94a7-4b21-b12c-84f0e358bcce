package com.bboss.report;

import com.bboss.report.model.TemplateVariable;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 年月日期控件功能测试
 * 
 * <AUTHOR>
 * @create 2025/7/26
 * @since 1.0.0
 */
public class YearMonthDatePickerTest {
    
    @Test
    public void testYearMonthFormatValidation() {
        TemplateVariable dateVariable = new TemplateVariable();
        dateVariable.setName("period");
        dateVariable.setLabel("账期");
        dateVariable.setType(TemplateVariable.VariableType.DATE);
        dateVariable.setRequired(true);
        
        // 测试年月格式验证
        assertTrue(dateVariable.validateValue("2025-01"), "应该接受年月格式 yyyy-MM");
        assertTrue(dateVariable.validateValue("2024-12"), "应该接受年月格式 yyyy-MM");
        
        // 测试完整日期格式验证（向后兼容）
        assertTrue(dateVariable.validateValue("2025-01-15"), "应该接受完整日期格式 yyyy-MM-dd");
        
        // 测试无效格式
        assertFalse(dateVariable.validateValue("2025"), "不应该接受只有年份的格式");
        assertFalse(dateVariable.validateValue("25-01"), "不应该接受两位年份的格式");
        assertFalse(dateVariable.validateValue("2025/01"), "不应该接受斜杠分隔的格式");
        assertFalse(dateVariable.validateValue("invalid"), "不应该接受无效的文本");
        
        // 测试必填验证
        assertFalse(dateVariable.validateValue(""), "必填字段不应该接受空值");
        assertFalse(dateVariable.validateValue(null), "必填字段不应该接受null值");
        
        // 测试非必填字段
        dateVariable.setRequired(false);
        assertTrue(dateVariable.validateValue(""), "非必填字段应该接受空值");
        assertTrue(dateVariable.validateValue(null), "非必填字段应该接受null值");
    }
    
    @Test
    public void testYearMonthParsing() {
        // 测试年月解析
        String yearMonthStr = "2025-01";
        YearMonth yearMonth = YearMonth.parse(yearMonthStr);
        LocalDate firstDayOfMonth = yearMonth.atDay(1);
        
        assertEquals(2025, firstDayOfMonth.getYear());
        assertEquals(1, firstDayOfMonth.getMonthValue());
        assertEquals(1, firstDayOfMonth.getDayOfMonth());
        
        // 测试格式化
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        String formatted = firstDayOfMonth.format(formatter);
        assertEquals("2025-01", formatted);
    }
    
    @Test
    public void testLastMonthDefault() {
        // 测试上个月的默认值计算
        LocalDate now = LocalDate.now();
        LocalDate lastMonth = now.minusMonths(1).withDayOfMonth(1);
        
        // 验证是上个月的第一天
        assertEquals(1, lastMonth.getDayOfMonth());
        
        if (now.getMonthValue() == 1) {
            // 如果当前是1月，上个月应该是去年12月
            assertEquals(12, lastMonth.getMonthValue());
            assertEquals(now.getYear() - 1, lastMonth.getYear());
        } else {
            // 其他情况，上个月应该是同年的上一个月
            assertEquals(now.getMonthValue() - 1, lastMonth.getMonthValue());
            assertEquals(now.getYear(), lastMonth.getYear());
        }
        
        // 测试格式化为年月字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        String lastMonthStr = lastMonth.format(formatter);
        assertTrue(lastMonthStr.matches("\\d{4}-\\d{2}"));
    }
    
    @Test
    public void testValidationHint() {
        TemplateVariable dateVariable = new TemplateVariable();
        dateVariable.setType(TemplateVariable.VariableType.DATE);
        
        String hint = dateVariable.getValidationHint();
        assertEquals("请输入年月格式 (yyyy-MM)", hint);
    }
    
    @Test
    public void testDefaultValueParsing() {
        TemplateVariable dateVariable = new TemplateVariable();
        dateVariable.setType(TemplateVariable.VariableType.DATE);
        
        // 测试年月格式默认值
        dateVariable.setDefaultValue("2024-12");
        assertTrue(dateVariable.validateValue(dateVariable.getDefaultValue()));
        
        // 测试解析年月格式默认值
        String defaultValue = dateVariable.getDefaultValue();
        if (defaultValue != null && defaultValue.matches("\\d{4}-\\d{2}")) {
            YearMonth yearMonth = YearMonth.parse(defaultValue);
            LocalDate date = yearMonth.atDay(1);
            
            assertEquals(2024, date.getYear());
            assertEquals(12, date.getMonthValue());
            assertEquals(1, date.getDayOfMonth());
        }
    }
    
    @Test
    public void testYearMonthEdgeCases() {
        // 测试边界情况
        
        // 测试2月（最短月份）
        assertTrue(new TemplateVariable("test", "测试", TemplateVariable.VariableType.DATE)
                .validateValue("2024-02"));
        
        // 测试12月
        assertTrue(new TemplateVariable("test", "测试", TemplateVariable.VariableType.DATE)
                .validateValue("2024-12"));
        
        // 测试无效月份
        assertFalse(new TemplateVariable("test", "测试", TemplateVariable.VariableType.DATE)
                .validateValue("2024-13"));
        assertFalse(new TemplateVariable("test", "测试", TemplateVariable.VariableType.DATE)
                .validateValue("2024-00"));
        
        // 测试闰年
        YearMonth feb2024 = YearMonth.of(2024, 2); // 2024是闰年
        assertEquals(29, feb2024.lengthOfMonth());
        
        YearMonth feb2023 = YearMonth.of(2023, 2); // 2023不是闰年
        assertEquals(28, feb2023.lengthOfMonth());
    }
    
    @Test
    public void testStringConverterLogic() {
        // 模拟 StringConverter 的逻辑
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        
        // 测试 toString 逻辑
        LocalDate date = LocalDate.of(2025, 1, 15);
        String formatted = date.format(formatter);
        assertEquals("2025-01", formatted);
        
        // 测试 fromString 逻辑
        String input = "2025-01";
        YearMonth yearMonth = YearMonth.parse(input, formatter);
        LocalDate parsed = yearMonth.atDay(1);
        
        assertEquals(2025, parsed.getYear());
        assertEquals(1, parsed.getMonthValue());
        assertEquals(1, parsed.getDayOfMonth());
        
        // 测试空字符串
        String emptyInput = "";
        assertThrows(Exception.class, () -> {
            YearMonth.parse(emptyInput, formatter);
        });
        
        // 测试null
        String nullInput = null;
        // 在实际的 StringConverter 中，null 会被处理为返回 null
    }
}
