package com.bboss.report;

import com.bboss.report.model.EmailTemplate;
import com.bboss.report.util.FileFilterUtil;
import com.bboss.report.util.TemplateManager;
import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Stage;

import java.io.File;
import java.net.URL;
import java.util.Arrays;
import java.util.List;

/**
 * 邮件模板管理功能测试类
 * 
 * <AUTHOR>
 * @create 2025/7/2
 * @since 1.0.0
 */
public class TemplateManagementTest extends Application {
    
    @Override
    public void start(Stage primaryStage) throws Exception {
        // 测试数据库和模板管理功能
        testTemplateManager();
        
        // 测试文件过滤功能
        testFileFilter();
        
        // 启动模板管理界面
        URL fxmlLocation = getClass().getResource("/fxml/TemplateManagementView.fxml");
        if (fxmlLocation == null) {
            System.err.println("错误: 无法找到 TemplateManagementView.fxml 文件");
            return;
        }
        
        FXMLLoader fxmlLoader = new FXMLLoader(fxmlLocation);
        Parent root = fxmlLoader.load();
        Scene scene = new Scene(root, 800, 600);
        
        primaryStage.setTitle("邮件模板管理测试 - 保存按钮调试");
        primaryStage.setScene(scene);
        primaryStage.show();

        System.out.println("=== 保存按钮测试说明 ===");
        System.out.println("测试步骤:");
        System.out.println("1. 点击 '新建' 按钮");
        System.out.println("2. 观察控制台调试信息");
        System.out.println("3. 填写模板名称和邮件主题");
        System.out.println("4. 检查 '保存模板' 按钮是否启用");
        System.out.println("5. 观察文本变化监听器是否触发");
        System.out.println("========================");
    }
    
    /**
     * 测试模板管理器功能
     */
    private void testTemplateManager() {
        System.out.println("=== 测试模板管理器功能 ===");
        
        TemplateManager templateManager = TemplateManager.getInstance();
        
        // 创建测试模板
        EmailTemplate testTemplate = new EmailTemplate();
        testTemplate.setName("测试模板");
        testTemplate.setDescription("这是一个测试模板");
        testTemplate.setRecipients("<EMAIL>;<EMAIL>");
        testTemplate.setCcRecipients("<EMAIL>");
        testTemplate.setSubject("测试邮件主题");
        testTemplate.setBody("这是测试邮件的正文内容。");
        testTemplate.setFilePrefixes("report_,audit_");
        testTemplate.setFileExtensions("pdf,xlsx,docx");
        
        // 保存模板
        boolean saved = templateManager.saveTemplate(testTemplate);
        System.out.println("保存模板结果: " + saved);
        
        if (saved) {
            System.out.println("模板ID: " + testTemplate.getId());
            
            // 获取所有模板
            List<EmailTemplate> templates = templateManager.getAllTemplates();
            System.out.println("模板总数: " + templates.size());
            
            for (EmailTemplate template : templates) {
                System.out.println("模板: " + template.getName() + " - " + template.getDescription());
            }
            
            // 根据名称查找模板
            EmailTemplate foundTemplate = templateManager.getTemplateByName("测试模板");
            if (foundTemplate != null) {
                System.out.println("找到模板: " + foundTemplate.getName());
                System.out.println("文件前缀: " + foundTemplate.getFilePrefixList());
                System.out.println("文件扩展名: " + foundTemplate.getFileExtensionList());
            }
        }
        
        System.out.println();
    }
    
    /**
     * 测试文件过滤功能
     */
    private void testFileFilter() {
        System.out.println("=== 测试文件过滤功能 ===");
        
        // 测试当前目录
        String currentDir = System.getProperty("user.dir");
        System.out.println("测试目录: " + currentDir);
        
        // 测试不同的过滤条件
        List<String> prefixes = Arrays.asList("pom", "README");
        List<String> extensions = Arrays.asList("xml", "md", "txt");
        
        FileFilterUtil.FilterResult result = FileFilterUtil.filterFiles(
            currentDir, prefixes, extensions, false);
        
        System.out.println("过滤结果:");
        System.out.println(result.getSummary());
        System.out.println("匹配文件数: " + result.getFileCount());
        System.out.println("总大小: " + result.getFormattedSize());
        
        if (result.getFileCount() > 0) {
            System.out.println("匹配的文件:");
            for (File file : result.getMatchedFiles()) {
                System.out.println("  - " + file.getName());
            }
        }
        
        System.out.println();
        
        // 测试常用扩展名和前缀
        System.out.println("常用文件扩展名: " + FileFilterUtil.getCommonFileExtensions());
        System.out.println("常用文件前缀: " + FileFilterUtil.getCommonFilePrefixes());
        
        System.out.println();
    }
    
    public static void main(String[] args) {
        launch(args);
    }
}
