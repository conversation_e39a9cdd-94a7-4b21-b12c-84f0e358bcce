package com.bboss.report.service.special;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bboss.report.component.DictMap;
import com.bboss.report.model.AuditResponse;
import com.bboss.report.model.ConfigData;
import com.bboss.report.model.ExcelParam;
import com.bboss.report.service.BaseService;
import com.bboss.report.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.eventusermodel.HSSFEventFactory;
import org.apache.poi.hssf.eventusermodel.HSSFListener;
import org.apache.poi.hssf.eventusermodel.HSSFRequest;
import org.apache.poi.hssf.record.BoundSheetRecord;
import org.apache.poi.hssf.record.Record;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.poifs.filesystem.DirectoryNode;
import org.apache.poi.poifs.filesystem.Entry;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xssf.eventusermodel.XSSFReader;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamConstants;
import javax.xml.stream.XMLStreamReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

@Service
@Slf4j
public class AggregateSumService extends BaseService{

    /**
     *
     * @param directoryAbsolutePath  选定的目录
     * @param settleMonth  账期
     * @param data  稽核项配置
     * @param responseList 稽核结果
     * @return
     * // 1 拼装指定目录   分表和总表
     *    2 读取所有分表的数据到内存
     *          1 确认excel的位置
     *          2 有特殊的产品  A|B  对应总表两条数据的稽核
     *
     *    3 读取总表数据
     *          1 读取所有总表数据
     *          2 进行分组
     *          3 总表特殊处理   默认行数  45  和特殊的配置 56
     *          4 D313 省份下有很多的产品
     */

    private static BigDecimal calculateTotalAmount(Map<String, String> subtableMsg) {
        // 初始化总金额为0
        BigDecimal total = BigDecimal.ZERO;

        // 遍历Map中的所有values
        for (String amountStr : subtableMsg.values()) {
            try {
                // 将字符串转换为BigDecimal并累加到总金额
                if(CommonUtil.isNotNull(amountStr)){
                    BigDecimal amount = new BigDecimal(amountStr);
                    total = total.add(amount);
                }else{
                    log.info("amountStr为空");
                }
            } catch (NumberFormatException e) {
                // 如果转换失败，可以打印错误信息或者采取其他错误处理措施
                System.err.println("Error parsing '" + amountStr + "' to BigDecimal.");
            }
        }
        // 返回总金额
        return total;
    }


    private static boolean isDifferenceGreaterOrEqual(BigDecimal amount1, BigDecimal amount2, BigDecimal threshold) {
        // 计算两个金额的差值
        BigDecimal difference = amount1.add(amount2).abs(); // 使用abs()取绝对值
        // 使用compareTo方法比较差值与阈值
        return difference.compareTo(threshold) >= 0;
    }

    public List<AuditResponse> searchSubtableMsg(String directoryAbsolutePath,String settleMonth,ConfigData data,List<AuditResponse> responseList){
        String[] pathArr = data.getFileName().split(",");
        directoryAbsolutePath = directoryAbsolutePath + data.getDirectory();
        String subtablePath = directoryAbsolutePath+"\\"+pathArr[0];
        String totalPath = directoryAbsolutePath+"\\"+pathArr[1];
        log.info("subtablePath={}",subtablePath);
        List<String> subtableFiles =  listExcelFiles(subtablePath);
        List<String> totalFiles =  listExcelFiles(totalPath);
        log.info("subtableFiles={}",subtableFiles.toString());

        //key =  报表编码+产品编码+产品名字+省份
        Map<String,Map<String,String>> subFileNameMsg = getSubtableMsg(subtablePath,subtableFiles);
        Map<String,String> subtableMsg =subFileNameMsg.get("money");
        Map<String,String> subFileNameMap =subFileNameMsg.get("file");

        log.info("开始加载总表");
        Map<String,Map<String,String>> sumFileNameMsg = getTotalMsg(totalPath,totalFiles,data);
        Map<String,String> totalMsg =sumFileNameMsg.get("money");
        Map<String,String> sumFileNameMap =sumFileNameMsg.get("file");

        //D313-2&智慧校园云平台&160205&浙江-value=null

        //进行比对  以总表数据为主
        for (String key : subtableMsg.keySet()) {   //key = D313-2_政企条线成研院产品支撑费结算单(6%)_2...&3&1
            AuditResponse response = new AuditResponse();
            response.setReportName(data.getReportName());
            response.setSettleMonth(settleMonth);
            response.setDirectory(data.getDirectory());
            response.setAuditEntryStr(DictMap.auditEntryMap.get(data.getAuditEntry()));

            String value = String.valueOf(subtableMsg.get(key)).replace(",", "");//230
            log.info("key={}",key);

            //如果分表的 key的产品有多个值  是用A|B进行分隔的
            String sumValue = "";
            if(key.contains("|")){//如果有 那么是多个产品的  需要从主表中找到多个金额进行相加
                sumValue = totalMsg.get(key);

                String[] keyArr = key.split("&");
                String reportCode = keyArr[0];
                String productName = keyArr[1];
                String productNum = keyArr[2];
                String companyName = keyArr[3];

                String[] productNameList = productName.split("\\|");
                String[] productNumList = productNum.split("\\|");
                BigDecimal totalMoney = new BigDecimal(0);
                //如果有多组 那么从主表中找到多组数据   进行求和
                for(int i = 0;i<productNameList.length;i++){
                    productName = productNameList[i];
                    productNum = productNumList[i];
                    String totalKey = reportCode+"&"+productName+"&"+productNum+"&"+companyName;
                    log.info("totalKey={}-value={}",totalKey,totalMsg.get(totalKey));
                    totalMoney = totalMoney.add(new BigDecimal(totalMsg.get(totalKey)));
                }
                sumValue = totalMoney.toString();
            }else{
                sumValue = totalMsg.get(key);
            }
            //转换为2位小数
            //String newValue = "-"+value;
            //newValue = new BigDecimal(newValue).setScale(2,BigDecimal.ROUND_HALF_UP).toString();
            String subFileNames = subFileNameMap.get(key);
            String totalFileNames = sumFileNameMap.get(key);
            response.setFileName(subFileNames);//报表名称

            String newValue = new BigDecimal(value).setScale(2,BigDecimal.ROUND_HALF_UP).toString();
            log.info("newValue={}---sumValue={}",value,sumValue);

            if(CommonUtil.isNotNull(newValue)&&CommonUtil.isNotNull(sumValue)){
                if (new BigDecimal(newValue).add(new BigDecimal(sumValue)).compareTo(BigDecimal.ZERO) == 0) {
                    log.info("key={}稽核成功",key);
                    response.setRspCode(response.SUCCESS);
                    response.setRspDesc(AuditResponse.SUCCESS_DESC);
                }else{
                    response.setRspCode(response.FAIL);
                    String[] keyArr = key.split("&");
                    String reportCode = keyArr[0];
                    String productName = keyArr[1];
                    String productNum = keyArr[2];
                    String companyName = keyArr[3];
                    String rspDesc = "报表编码:"+reportCode+",产品名称:"+productName+",产品编码:"+productNum+",省份:"+companyName+"稽核失败  分表总金额:"+value+" 总表总金额:"+sumValue+ "   分表文件:"+subFileNames+"   总表文件:"+totalFileNames;
                    response.setRspDesc(rspDesc);
                }
            }else{
                response.setRspCode(response.FAIL);
                String[] keyArr = key.split("&");
                String reportCode = keyArr[0];
                String productName = keyArr[1];
                String productNum = keyArr[2];
                String companyName = keyArr[3];
                String rspDesc = "报表编码:"+reportCode+",产品名称:"+productName+",产品编码:"+productNum+",省份:"+companyName+"稽核失败  分表总金额:"+value+" 总表总金额:"+sumValue+ "   分表文件:"+subFileNames+"   总表文件:"+totalFileNames;
                response.setRspDesc(rspDesc);
            }
            responseList.add(response);
        }

        //对总金额进行比对和稽核
        BigDecimal subtableTotalMoney = calculateTotalAmount(subtableMsg);
        BigDecimal totalMoney = calculateTotalAmount(totalMsg);
        log.info("明细总金额={}--总表总金额={}",subtableTotalMoney.toString(),totalMoney.toString());
        if(isDifferenceGreaterOrEqual(subtableTotalMoney,totalMoney,new BigDecimal(100))){
            AuditResponse response = new AuditResponse();
            response.setRspCode(response.FAIL);
            response.setReportName(data.getReportName());
            response.setSettleMonth(settleMonth);
            response.setDirectory(data.getDirectory());
            response.setAuditEntryStr(DictMap.auditEntryMap.get(data.getAuditEntry()));
            String rspDesc = "明细和总表总金额相差过大超过100,分表总金额:"+subtableTotalMoney.toString()+" 总表总金额:"+totalMoney.toString();
            response.setRspDesc(rspDesc);
            responseList.add(response);
        }

        return responseList;
    }

    public List<AuditResponse> fileCheckDoubleInfo(String directoryAbsolutePath,String settleMonth,ConfigData data,List<AuditResponse> responseList){
        Path parent = Paths.get(directoryAbsolutePath);
        File file = parent.toFile();
        JSONObject param = JSONObject.parseObject(data.getParameter());
        JSONArray fileNameList = param.getJSONArray("fileList");
        Map<String, String> fileMap = tranMap(fileNameList);
        List<String> fileListInfo = new ArrayList<>();
        this.searchXlsFile(file,fileListInfo);
        for (Map.Entry<String, String> entry  : fileMap.entrySet()) {
            String SubTablekey = entry.getKey();
            String totaleExcelName = entry.getValue();
            String[] split = SubTablekey.split(",");
            List<String> taxRates = Arrays.asList("0%","6%","9%","10%","11%","13%","16%","17%");
            for (String taxRate : taxRates) {
                for (String subName : split) {
                   String subNameNew = subName.replace("taxRate", taxRate).replace("YYYYMM", settleMonth);
                    if (fileListInfo.contains(subNameNew)){
                        //分表存在校验总表是否存在
                       String totaleExcelNameNew = totaleExcelName.replace("taxRate", taxRate).replace("YYYYMM",settleMonth);
                       if (!fileListInfo.contains(totaleExcelNameNew)){
                           AuditResponse response = new AuditResponse();
                           response.setRspCode(response.FAIL);
                           response.setReportName(data.getReportName());
                           response.setSettleMonth(settleMonth);
                           response.setDirectory(data.getDirectory());
                           response.setAuditEntryStr(DictMap.auditEntryMap.get(data.getAuditEntry()));
                           String rspDesc = "分表文件和总表文件稽核，分表名称为："+subNameNew+" 总表名称为:"+totaleExcelNameNew+"总表文件不存在";
                           response.setRspDesc(rspDesc);
                           responseList.add(response);
                           break;
                       }
                    }
                }
            }
        }
        return responseList;
    }

    public void searchXlsFile(File directory,List<String> pdfFiles) {
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile()) {
                        if (file.isFile() && file.getName().toLowerCase().endsWith(".xls") ) {
                            pdfFiles.add(file.getName());
                        }
                    } else if (file.isDirectory()) {
                        searchXlsFile(file,pdfFiles);
                    }
                }
            }
        }
    }

    public Map<String, String> tranMap(JSONArray array) {
        // 创建 Map 来存储结果
        Map<String, String> map = new HashMap<>();

        // 遍历 JSON 数组
        for (int i = 0; i < array.size(); i++) {
            JSONObject jsonObject = array.getJSONObject(i);

            // 获取 JSONObject 中的键和值
            String key = new ArrayList<>(jsonObject.keySet()).get(0);
            String value = jsonObject.getString(key);
            // 将键值对添加到 Map 中
            map.put(key, value);
        }
        return map;
    }

    public static boolean contains(String[] array, String element) {
        for (String e : array) {
            if (element.equals(e)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取到所有总表的数据
     * getColumnDataList2(String pathName, Integer startRow, Integer endRow, Integer[] columns, Integer startColumn)
     *
     * 入参totalMsg 是所有的金额对应关系
     * 返参         是key和总表文件名称的对应关系   总表可能有多个  通&分隔
     *
     */
    public Map<String,Map<String,String>> getTotalMsg(String totalPath,List<String> totalFiles,ConfigData data){
        Map<String,Map<String,String>> mapResult = new HashMap<>();
        Map<String,String> sumFileNameMap = new HashMap<>();
        Map<String,String> totalMsg = new HashMap<>();


        int rowStartParm = 0;//从0开始  3就是第四行
        JSONObject param = JSONObject.parseObject(data.getParameter());
        JSONObject aggregateSum = param.getJSONObject("aggregateSum");
        ExcelParam excelParam = JSONObject.parseObject(aggregateSum.toJSONString(), ExcelParam.class);
        String[] row5FileList = null;
        if(CommonUtil.isNotNull(excelParam)){
            rowStartParm = excelParam.getParam1().getStartRow();
            row5FileList = excelParam.getParam1().getColumns().split(",");
        }

        //循环获取所有的分表数据 D313-2&智慧校园云平台&160205&浙江-value=null
        int num = 1;
        for(String totalFile : totalFiles){
            int rowStart = 3;

            //
            String[] nameArr = totalFile.split("_");
            //报表编码
            String reportCode = nameArr[0];

            String filePath = totalPath+"\\"+totalFile;
            log.info("加载总表文件{}-待加载数量={}",totalFile,totalFiles.size()-num);
            num++;

            //取分表数据 取
            Integer sheetNums =  getsheetNum(new File(filePath));
            //SQL Results&1&1 -> 230   1行 1列  230
            for(int i=0;i<sheetNums;i++){//轮询sheet
                //从第4或者5行取数据到最后一行    从第2列开始到最后一列
                List<Map<String, Object>> fileMsg = getColumnDataList2(filePath, rowStart, 0, null,1 );
                //从第10列开始取产品
                //取到第10列 到最后1列的数据   10 12 14。。。

                //获取到产品名称  第一行
                Map<String,String> productNameMap = new HashMap<>();
                Map<String, Object> mapMsg1 = fileMsg.get(0);
                for (String key : mapMsg1.keySet()) {   //key = D313-2_政企条线成研院产品支撑费结算单(6%)_2...&3&1
                    String value = String.valueOf(mapMsg1.get(key)).replace(",", "");//230
                    String[] keyArr = key.split("&");
                    Integer line = Integer.parseInt(keyArr[2]);// 列数 从10 开始 存数据  10 12 14。。。
                    //总列数
                    if(10<=line && line <= mapMsg1.size()){
                        if(line%2==0){
                            productNameMap.put(String.valueOf(line),value);
                        }
                    }
                }

                //获取产品编码
                Map<String, Object> mapMsg2 = fileMsg.get(1);
                Map<String,String> productNumMap = new HashMap<>();
                for (String key : mapMsg2.keySet()) {   //key = D313-2_政企条线成研院产品支撑费结算单(6%)_2...&3&1
                    String value = String.valueOf(mapMsg2.get(key)).replace(",", "");//230
                    String[] keyArr = key.split("&");
                    Integer line = Integer.parseInt(keyArr[2]);// 列数 从10 开始 存数据  10 12 14。。。
                    //总列数
                    if(10<=line && line <= mapMsg2.size()){
                        if(line%2==1){
                            productNumMap.put(String.valueOf(line-1),value);//如果是11 那么key=10  value = 产品编码
                        }
                    }
                }
                //key = line+col   value 是金额的和   比如 2&10    2&12  2&14
                Map<String,String> sumMoneyMap = new HashMap<>();
                // 之后循环每一个省  从6开始  往后取值31行
                int a = 3;
                int lineCount=34;
                if(contains(row5FileList,totalFile)){
                    a++;
                    lineCount++;
                }
                for (int j = a; j < lineCount; j++) {//每一行数据
                    Map<String, Object> mapMsg = fileMsg.get(j);
                    //
                    Iterator<Map.Entry<String, Object>> iterator = mapMsg.entrySet().iterator();
                    String companyName = "";
                    while (iterator.hasNext()) {
                        Map.Entry<String, Object> entry = iterator.next();
                        String key = entry.getKey();
                        String value = String.valueOf(entry.getValue()).replace(",", "");
                        String[] keyArr = key.split("&");
                        String line = keyArr[1];//行数
                        Integer col = Integer.parseInt(keyArr[2]);// 列数 从10 开始 存数据  10 12 14。。。

                        //获取到省份  第2列
                        if("1".equals(keyArr[2])){
                            companyName = value;
                        }

                        //总列数
                        if(10<=col && col <= mapMsg.size()){
                            if(col%2==0){
                                //获取两个金额  A+B
                                //获取当前元素的下一个元素
                                Map.Entry<String, Object> nextEntry = iterator.next();
                                String nextValue = String.valueOf(nextEntry.getValue()).replace(",", "");
                              //  log.info("value={}--nextValue={}",value,nextValue);
                                BigDecimal sumMoney = new BigDecimal(value).add(new BigDecimal(nextValue));
                                sumMoneyMap.put(companyName+"&"+line+"&"+String.valueOf(col),sumMoney.toString());
                            }
                        }
                        // 在这里处理你的键和值
                    }
                }

                //对一个sheet中的所有数据   3个map进行组合
                for (String key : sumMoneyMap.keySet()) {
                    String value = String.valueOf(sumMoneyMap.get(key)).replace(",", "");//230
                    String[] keyArr = key.split("&");
                    String companyName = keyArr[0];
                    String line = keyArr[1];
                    String col = keyArr[2];

                    //通过col 获取 产品名称  和产品编码
                    String productName = productNameMap.get(col);
                    String productNum = productNumMap.get(col);

                    //重新组转Map
                    String totalKey = reportCode+"&"+productName+"&"+productNum+"&"+companyName;
                  //  log.info("totalKey={}",totalKey);
                    String hisMoney = totalMsg.get(totalKey);
                    if(CommonUtil.isNotNull(hisMoney)){
                    //    log.info("hisMoney={}--value={}",hisMoney,value);
                        BigDecimal newMoney = new BigDecimal(hisMoney).add(new BigDecimal(value));
                        totalMsg.put(totalKey,newMoney.toString());
                    }else{
                        totalMsg.put(totalKey,value);
                    }

                    //通过key 组装文件列表
                    String hisFileName = sumFileNameMap.get(totalKey);
                    if(CommonUtil.isNotNull(hisFileName)){
                        String newFileName = hisFileName+"&"+totalFile;
                        sumFileNameMap.put(totalKey,newFileName);
                    }else{
                        sumFileNameMap.put(totalKey,totalFile);
                    }
                }
            }
        }
        log.info("总表加载完毕");
        mapResult.put("file",sumFileNameMap);
        mapResult.put("money",totalMsg);
        return mapResult;
    }


    /**
     * 获取到所有分表的数据稽核
     * 收集了所有的sheet
     */
    public Map<String,Map<String,String>> getSubtableMsg(String subtablePath,List<String> subtableFiles){
        Map<String,Map<String,String>> mapResult = new HashMap<>();
        Map<String,String> subFileNameMap = new HashMap<>();
        Map<String,String> subtableMsg = new HashMap<>();
        //循环获取所有的分表数据
        log.info("开始加载分表数据");
        int num=1;
        for(String subtableFile : subtableFiles){
            //
            String[] nameArr = subtableFile.split("_");
            //报表编码
            String reportCode = nameArr[0];

            String filePath = subtablePath+"\\"+subtableFile;
            log.info("分表文件={}---还剩余{}个文件待加载",filePath,subtableFiles.size()-num);
            num++;
            //取分表数据 从第二行 到最后一行  前四列
            Integer[] columns = new Integer[]{0,1,2,3};
            Integer sheetNums =  getsheetNum(new File(filePath));
            //SQL Results&1&1 -> 230   1行 1列  230
            for(int i=0;i<sheetNums;i++){//轮询sheet
                List<Map<String, Object>> fileMsg = getColumnDataList(filePath, i,1, 0, columns);
                for (int j = 0; j < fileMsg.size(); j++) {//每一行数据
                    Map<String, Object> mapFromList1 = fileMsg.get(j);
                    String productName ="";
                    String productNum = "";
                    String comNum = "";
                    String money = "";
                    for (String key : mapFromList1.keySet()) {//key = SQL Results&1&1
                        String value = String.valueOf(mapFromList1.get(key)).replace(",", "");//230
                        String[] keyArr = key.split("&");
                        String line = keyArr[2];// 1,2,3,4  分别代表了 产品列名    产品科目编码  主办省编码  结算含税金额
                        switch (line) {
                            case "0":
                                productName = value;
                                break;
                            case "1":
                                productNum = value;
                                break;
                            case "2":
                                comNum = value;
                                break;
                            case "3":
                                money = value;
                                break;
                            default:
                                log.info("数据异常,取到的数据不是0,1,2,3列");
                        }
                    }
                    String key = reportCode+"&"+productName+"&"+productNum+"&"+comNum;
                    String hisMoney = subtableMsg.get(key);
                    if(CommonUtil.isNotNull(hisMoney)){
                      //  log.info("hisMoney={}");
                        BigDecimal newMoney = new BigDecimal(hisMoney).add(new BigDecimal(money));
                        subtableMsg.put(key,newMoney.toString());
                    }else{
                        subtableMsg.put(key,money);
                    }

                    //通过key 组装文件列表
                    String hisFileName = subFileNameMap.get(key);
                    if(CommonUtil.isNotNull(hisFileName)){
                        // 因为可能有多个sheet 要判断下
                        if(!hisFileName.contains(subtableFile)){//名字在map中没有 才会加入到名字列表中
                            String newFileName = hisFileName+"&"+subtableFile;
                            subFileNameMap.put(key,newFileName);
                        }
                    }else{
                        subFileNameMap.put(key,subtableFile);
                    }
                }
            }
        }
        log.info("分表加载完毕");
        mapResult.put("file",subFileNameMap);
        mapResult.put("money",subtableMsg);
        return mapResult;
    }




    /**
     * 读取该目录下的所有xlsx文件名字
     */
    public static List<String> listExcelFiles(String directoryPath) {
        List<String> excelFiles = new ArrayList<>();
        File directory = new File(directoryPath);
        File[] files = directory.listFiles();

        if (files != null) {
            for (File file : files) {
                if (file.isFile() && (file.getName().toLowerCase().endsWith(".xlsx")||file.getName().toLowerCase().endsWith(".xls"))) {
                    excelFiles.add(file.getName());
                }
            }
        }

        return excelFiles;
    }


    public Integer getsheetNum(File file){
       /* int numberOfSheets = 1;
        try{
            FileInputStream fis = new FileInputStream(file);
            Workbook workbook = WorkbookFactory.create(fis);
            //sheet的数量
            numberOfSheets = workbook.getNumberOfSheets();
        }catch (Exception e){
            log.info("sheet数量异常");
        }
        return numberOfSheets;*/

        if (file == null || !file.exists()) return 1;

        try {
            // 根据扩展名选择解析方式
            if (file.getName().toLowerCase().endsWith(".xlsx")) {
                try (OPCPackage pkg = OPCPackage.open(file.getPath())) {
                    XSSFReader reader = new XSSFReader(pkg);
                    try (InputStream is = reader.getWorkbookData()) {
                        XMLInputFactory factory = XMLInputFactory.newInstance();
                        XMLStreamReader xmlReader = factory.createXMLStreamReader(is);
                        int count = 0;
                        while (xmlReader.hasNext()) {
                            int event = xmlReader.next();
                            if (event == XMLStreamConstants.START_ELEMENT
                                    && xmlReader.getLocalName().equals("sheet")) {
                                count++;
                            }
                        }
                        return count;
                    }
                }
            } else if (file.getName().toLowerCase().endsWith(".xls")) {

                int numberOfSheets = 1;
                try{
                    FileInputStream fis = new FileInputStream(file);
                    Workbook workbook = WorkbookFactory.create(fis);
                    //sheet的数量
                    numberOfSheets = workbook.getNumberOfSheets();
                }catch (Exception e){
                    log.info("sheet数量异常");
                }
                return numberOfSheets;
            }
            else {
                throw new IllegalArgumentException("不支持的文件格式");
            }
        } catch (Exception e) {
            log.error("读取Sheet数量失败: {}", e.getMessage());
            return 1; // 默认返回1
        }

    }

}
