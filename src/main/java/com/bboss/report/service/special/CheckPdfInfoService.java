package com.bboss.report.service.special;

import com.alibaba.fastjson.JSONObject;
import com.bboss.report.component.DictMap;
import com.bboss.report.model.AuditResponse;
import com.bboss.report.model.ConfigData;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CheckPdfInfoService {
    public List<AuditResponse> checkPdfInfo(String directoryAbsolutePath, String settleMonth, ConfigData data, List<AuditResponse> responseList){
        String directory = directoryAbsolutePath+data.getDirectory();
        log.info("检查Pdf的目录为:"+directory);
        List<File> productDataFileList = new ArrayList<>();
        File directorys = new File(directory);
        searchPdfFile(directorys,productDataFileList);
        for (File file : productDataFileList) {
            try {
                PDDocument document = PDDocument.load(file);
                int pageCount = document.getNumberOfPages();
                boolean flag = false;
                for (int i = 0; i < pageCount; i++) {
                    PDPage page = document.getPage(i);
                    boolean hasText = hasTextContent(page, document);
                    if (hasText) {
                       log.info("Page " + (i + 1) + " contains text."+file.getName());
                    } else {
                        flag = true;
                        String rspDesc = file.getPath()+"是图片;";
                        AuditResponse response = new AuditResponse();
                        response.setReportName(data.getReportName());
                        response.setSettleMonth(settleMonth);
                        response.setDirectory(directory);
                        response.setRspCode(response.FAIL);
                        response.setAuditEntryStr(DictMap.auditEntryMap.get(data.getAuditEntry()));
                        response.setRspDesc(rspDesc);
                        responseList.add(response);
                        break;
                    }
                }
                document.close();
                if (flag) {
                    System.out.println("未获取到文件信息"+file.getName());
                }
            } catch (IOException e) {
                log.error("稽核文件：{},失败,", file.getName(), e);
            }
        }


        return responseList;
    }

    public List<AuditResponse> checkPdfAndExcelInfo(String directoryAbsolutePath, String settleMonth, ConfigData data, List<AuditResponse> responseList){
        JSONObject param = JSONObject.parseObject(data.getParameter());
        String fileNames = (String) param.get("notPdfCheckName");
        String[] split = fileNames.split(",");
        List<String> fileNamelist = Arrays.asList(split);
        List<String> fileNameInfolist = fileNamelist.stream().map(o -> o.replace("YYYYMM", settleMonth)).collect(Collectors.toList());
        String directory = directoryAbsolutePath+data.getDirectory();
        log.info("检查Pdf的目录为:"+directory);
        List<File> productDataFileList = new ArrayList<>();
        File directorys = new File(directory);
        searchPdfAndExcelFile(directorys,productDataFileList);
        List<String> fileNameLst = productDataFileList.stream().map(File::getName).collect(Collectors.toList());
        for (File file : productDataFileList) {
            String name = file.getName();
            if (fileNameInfolist.contains(name)){
                continue;
            }
            if (name.endsWith(".pdf")) {
                if (!name.contains("特殊分表")) {
                    String nameReplace = name.replace(".pdf", ".xls");
                    if (!fileNameLst.contains(nameReplace)){
                        AuditResponse response = new AuditResponse();
                        response.setReportName(data.getReportName());
                        response.setSettleMonth(settleMonth);
                        response.setDirectory(file.getAbsolutePath());
                        response.setRspCode(response.FAIL);
                        response.setAuditEntryStr(DictMap.auditEntryMap.get(data.getAuditEntry()));
                        response.setRspDesc("没有找到对应的"+nameReplace+"文件");
                        responseList.add(response);
                    }
                }
            }
            if (name.endsWith(".xls")) {
                if (!name.contains("特殊分表")) {
                    String nameReplace = name.replace(".xls", ".pdf");
                    if (!fileNameLst.contains(nameReplace)){
                        AuditResponse response = new AuditResponse();
                        response.setReportName(data.getReportName());
                        response.setSettleMonth(settleMonth);
                        response.setDirectory(file.getAbsolutePath());
                        response.setRspCode(response.FAIL);
                        response.setAuditEntryStr(DictMap.auditEntryMap.get(data.getAuditEntry()));
                        response.setRspDesc("没有找到对应的"+nameReplace+"文件");
                        responseList.add(response);
                    }
                }
            }
        }

        return responseList;
    }





    private static boolean hasTextContent(PDPage page, PDDocument document) throws IOException {
        PDFTextStripper textStripper = new PDFTextStripper();
        textStripper.setStartPage(document.getPages().indexOf(page) + 1);
        textStripper.setEndPage(document.getPages().indexOf(page) + 1);
        String text = textStripper.getText(document);
        return !text.trim().isEmpty();
    }

    /**
     * 读取该目录下的所有xlsx文件名字
     * D:\工作\日常\工作\上线文档\2024\稽核工具\需求4  202409
     * D:\工作\日常\工作\上线文档\2024\稽核工具\需求4  202409/评估金额\暂估
     */
    public static List<File> listPdfFiles(String directoryPath) {
        List<File> pdfFiles = new ArrayList<>();
        File directory = new File(directoryPath);
        File[] files = directory.listFiles();

        if (files != null) {
            for (File file : files) {
                if (file.isFile() && (file.getName().toLowerCase().endsWith(".pdf"))) {
                    pdfFiles.add(file);
                }
            }
        }


        return pdfFiles;
    }

    public void searchPdfFile(File directory,List<File> pdfFiles) {
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile()) {
                        //多个分表的金额相加综合等于总表金额 就是这个稽核不参与文件校验 因为该文件的fileName是多文件累加的。不能equal判断
                        if (file.isFile() && (file.getName().toLowerCase().endsWith(".pdf"))) {
                            pdfFiles.add(file);
                        }
                    } else if (file.isDirectory()) {
                        searchPdfFile(file,pdfFiles); // 递归进入子目录
                    }
                }
            }
        }
    }

    public void searchPdfAndExcelFile(File directory,List<File> pdfFiles) {
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile()) {
                        if (file.isFile() && (file.getName().toLowerCase().endsWith(".pdf")||file.getName().toLowerCase().endsWith(".xls"))) {
                            pdfFiles.add(file);
                        }
                    } else if (file.isDirectory()) {
                        searchPdfAndExcelFile(file,pdfFiles);
                    }
                }
            }
        }
    }


}
