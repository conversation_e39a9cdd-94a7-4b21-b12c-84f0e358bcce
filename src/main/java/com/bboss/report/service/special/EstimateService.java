package com.bboss.report.service.special;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bboss.report.component.DictMap;
import com.bboss.report.model.AuditResponse;
import com.bboss.report.model.ConfigData;
import com.bboss.report.service.BaseService;
import com.bboss.report.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 暂估 销暂估   两者相加为0 则稽核成功
 */
@Service
@Slf4j
public class EstimateService extends BaseService {

    /**
     * @param directoryAbsolutePath 选定的目录
     * @param settleMonth           账期
     * @param data                  稽核项配置
     * @param responseList          稽核结果
     * @return 1 轮询多个场景
     * 每个
     * // 1 拼装指定目录   分表和总表
     * 2 读取所有分表的数据到内存
     * 1 确认excel的位置
     * 2 有特殊的产品  A|B  对应总表两条数据的稽核
     * <p>
     * 3 读取总表数据
     * 1 读取所有总表数据
     * 2 进行分组
     * 3 总表特殊处理   默认行数  45  和特殊的配置 56
     * 4 D313 省份下有很多的产品
     */

    private static BigDecimal calculateTotalAmount(Map<String, String> subtableMsg) {
        // 初始化总金额为0
        BigDecimal total = BigDecimal.ZERO;

        // 遍历Map中的所有values
        for (String amountStr : subtableMsg.values()) {
            try {
                // 将字符串转换为BigDecimal并累加到总金额
                if (CommonUtil.isNotNull(amountStr)) {
                    BigDecimal amount = new BigDecimal(amountStr);
                    total = total.add(amount);
                } else {
                    log.info("amountStr为空");
                }
            } catch (NumberFormatException e) {
                // 如果转换失败，可以打印错误信息或者采取其他错误处理措施
                System.err.println("Error parsing '" + amountStr + "' to BigDecimal.");
            }
        }
        // 返回总金额
        return total;
    }


    private static boolean isDifferenceGreaterOrEqual(BigDecimal amount1, BigDecimal amount2, BigDecimal threshold) {
        // 计算两个金额的差值
        BigDecimal difference = amount1.add(amount2).abs(); // 使用abs()取绝对值
        // 使用compareTo方法比较差值与阈值
        return difference.compareTo(threshold) >= 0;
    }


    public Map<String, String> tranMap(JSONArray array) {
        // 创建 Map 来存储结果
        Map<String, String> map = new HashMap<>();

        // 遍历 JSON 数组
        for (int i = 0; i < array.size(); i++) {
            JSONObject jsonObject = array.getJSONObject(i);

            // 获取 JSONObject 中的键和值
            String key = new ArrayList<>(jsonObject.keySet()).get(0);
            String value = jsonObject.getString(key);
            // 将键值对添加到 Map 中
            map.put(key, value);
        }
        return map;
    }

    public Integer[] getIntList(String numsStr) {
        // 使用逗号分割字符串
        String[] numStrings = numsStr.split(",");

        // 创建 Integer 数组
        Integer[] numArray = new Integer[numStrings.length];

        // 遍历字符串数组，转换每个元素为 Integer
        for (int i = 0; i < numStrings.length; i++) {
            try {
                numArray[i] = Integer.parseInt(numStrings[i].trim());
            } catch (NumberFormatException e) {
                System.out.println("无法转换的数字: " + numStrings[i]);
                numArray[i] = null; // 或者设置为默认值
            }
        }
        return numArray;
    }

    //入口
    public List<AuditResponse> searchSubtableMsg(String directoryAbsolutePath, String settleMonth, ConfigData data, List<AuditResponse> responseList) {
        String[] pathArr = data.getFileName().split(",");//暂估   销暂估
        directoryAbsolutePath = directoryAbsolutePath + data.getDirectory();
        String estimateUrl = directoryAbsolutePath + "\\" + pathArr[0];
        String noEstimateUrl = directoryAbsolutePath + "\\" + pathArr[1];
        log.info("estimateUrl={}", estimateUrl);
        log.info("noEstimateUrl={}", noEstimateUrl.toString());

        List<String> estimateFileList = listExcelFiles(estimateUrl);
        List<String> noEstimateFileList = listExcelFiles(noEstimateUrl);


        JSONObject param = JSONObject.parseObject(data.getParameter());
        JSONObject estimateMoney = param.getJSONObject("estimateMoney");
        JSONArray fileList = estimateMoney.getJSONArray("fileList");
        Map<String, String> fileMap = tranMap(fileList);

        //校验两边目录下文件的数量是否一致
        if (estimateFileList.size() != noEstimateFileList.size()) {
            AuditResponse response = new AuditResponse();
            response.setReportName(data.getReportName());
            response.setSettleMonth(settleMonth);
            response.setDirectory(data.getDirectory());
            response.setAuditEntryStr(DictMap.auditEntryMap.get(data.getAuditEntry()));
            response.setRspCode(response.FAIL);
            response.setRspDesc("暂估和销暂估文件夹下的文件数量不一致 暂估下文件数量=" + estimateFileList.size() + "销暂估下文件数量=" + noEstimateFileList.size());
            responseList.add(response);
            return responseList;
        }
        //轮询每一个暂估的文件  找对应的销暂估的文件
        for (String fileName : estimateFileList) {
            if (fileName.contains("销暂估")) {
                AuditResponse response = new AuditResponse();
                response.setReportName(data.getReportName());
                response.setSettleMonth(settleMonth);
                response.setDirectory(data.getDirectory());
                response.setAuditEntryStr(DictMap.auditEntryMap.get(data.getAuditEntry()));
                response.setRspCode(response.FAIL);
                response.setRspDesc("暂估文件夹存在销暂估文件-文件名为=" + fileName);
                responseList.add(response);
                return responseList;
            }
            if (fileName.contains("暂估")) {
                String fileNewName = fileName.replace("暂估", "销暂估").trim();
                if (!noEstimateFileList.contains(fileNewName)) {
                    AuditResponse response = new AuditResponse();
                    response.setReportName(data.getReportName());
                    response.setSettleMonth(settleMonth);
                    response.setDirectory(data.getDirectory());
                    response.setAuditEntryStr(DictMap.auditEntryMap.get(data.getAuditEntry()));
                    response.setRspCode(response.FAIL);
                    response.setRspDesc("暂估文件为=" + fileName + "在销暂估文件夹中没有对应的销暂估文件");
                    responseList.add(response);
                    return responseList;
                }
            }
            //文件拼装
            String configCheckfileName = fileName.replace(".xls", "").replace(".xlsx", "");
            String[] nameArr = configCheckfileName.split("_");
            String endName = "";
            // String paramName = nameArr[0];
            // String paramName = nameArr[1];
            if (fileName.contains("公播音乐合作伙伴产品业务提供商结算收入报表")) {//特殊处理
                endName = nameArr[0];
            } else if (fileName.contains("应收收入_调账")) {
                endName = nameArr[0] + "_" + nameArr[1] + "_" + nameArr[3];
            }else if (fileName.contains("合营云产品")||fileName.contains("直营云产品")) {
                endName = nameArr[0] + "_" + nameArr[1] + "_" + nameArr[4]+"_" + nameArr[5];
            }  else {
                endName = nameArr[0] + "_" + nameArr[1];
                //D313_政企条线专业公司标准产品DICT服务包结算单 ||| D313_政企条线专业公司标准产品DICT服务包结算单_分表
                if ("分表".equals(nameArr[nameArr.length - 1])) {
                    endName = nameArr[0] + "_" + nameArr[1] + "_分表";
                }
                if ("特殊分表".equals(nameArr[nameArr.length - 1])) {
                    endName = nameArr[0] + "_" + nameArr[1] + "_特殊分表";
                }
            }
            //将(*)的内容删掉
            endName = endName.replaceAll("\\(.*?\\)", "");
            log.info("当前稽核文件:{},处理后的文件名字:{}", fileName, endName);
            //进行比对  加载文件 判断文件属于哪一类 fileList
            List<String> keyList = new ArrayList<>();
            for (int i = 0; i < fileList.size(); i++) {
                JSONObject fileObject = fileList.getJSONObject(i);
                String key = new ArrayList<>(fileObject.keySet()).get(0);
                keyList.add(key);
            }

            if (keyList.contains(endName)) {
                String filePath = estimateUrl + "\\" + fileName;
                String fileNewName = fileName.replace("暂估", "销暂估").trim();
                String noEstimateFilePath = noEstimateUrl + "\\" + fileNewName;//销暂估的文件路径

                //
                String parameterValue = fileMap.get(endName);
                JSONObject paramObj = JSONObject.parseObject(parameterValue);
                Integer startRow = Integer.parseInt(paramObj.getString("startRow"));
                Integer endRow = Integer.parseInt(paramObj.getString("endRow"));
                Integer startColumn = null;
                if (CommonUtil.isNotNull(paramObj.getString("startColumn"))) {
                    startColumn = Integer.parseInt(paramObj.getString("startColumn"));
                }

                Integer[] columns = null;
                if (CommonUtil.isNotNull(paramObj.getString("columns"))) {
                    columns = getIntList(paramObj.getString("columns"));
                }

                //---------

                //进行稽核
                Integer sheetNums = getsheetNum(new File(filePath));
                //SQL Results&1&1 -> 230   1行 1列  230
                for (int i = 0; i < sheetNums; i++) { //轮询sheet
                    log.info("正在稽核filePath={}", filePath + "sheetNums:" + i);
                    List<Map<String, Object>> estimateFileMsg = getColumnDataList2(filePath, i, startRow, endRow, columns, startColumn);
                    //noEstimateFilePath
                    List<Map<String, Object>> noEstimateFileMsg = getColumnDataList2(noEstimateFilePath, i, startRow, endRow, columns, startColumn);


                    for (int j = 0; j < estimateFileMsg.size(); j++) {//每一行数据
                        Map<String, Object> estimateList = estimateFileMsg.get(j);
                        Map<String, Object> noEstimateList = noEstimateFileMsg.get(j);

                        String productName = "";
                        String productNum = "";
                        String comNum = "";
                        String money = "";
                        for (String key : estimateList.keySet()) {//key = SQL Results&1&1
                            //暂估的value
                            String estimateValue = String.valueOf(estimateList.get(key)).replace(",", "");//230
                            //销暂估的value
                            String noEstimateKey = key.replace("暂估", "销暂估");
                            if (fileName.contains("公播音乐合作伙伴产品业务提供商结算收入报表")) {
                                for (String noKey : noEstimateList.keySet()) {
                                    if (noKey.contains("......")) {
                                        noEstimateKey = noEstimateKey.replace("销暂估_" + settleMonth, "销暂估......");
                                        log.info("noEstimateKey={}", noEstimateKey);
                                        break;
                                    } else {
                                        break;
                                    }
                                }
                            }
                            String noEstimateValue = String.valueOf(noEstimateList.get(noEstimateKey)).replace(",", "");//230

                            //false 则稽核失败
                            if (!areOppositeNumbers(estimateValue, noEstimateValue, fileName, fileNewName, i, key)) {
                                String rspDesc = "以暂估为主的稽核: 暂估文件=" + fileName + ",销暂估文件=" + fileNewName + ",sheetNums=" + i + ",稽核具体行数=" + key + "失败,暂估文件value=" + estimateValue + ",销暂估文件vlaue=" + noEstimateValue;
                                log.info(rspDesc);
                                AuditResponse response = new AuditResponse();
                                response.setReportName(data.getReportName());
                                response.setSettleMonth(settleMonth);
                                response.setDirectory(data.getDirectory());
                                response.setAuditEntryStr(DictMap.auditEntryMap.get(data.getAuditEntry()));
                                response.setRspCode(response.FAIL);
                                response.setRspDesc(rspDesc);
                                responseList.add(response);
                            }
                        }
                    }
                }

            } else {
                log.info("fileName处理后的文件名称=" + endName + ",不在parameter配置内,不参与稽核");
            }
        }
        return responseList;
    }

    public List<AuditResponse> compareProvisionalAndReport(String directoryAbsolutePath, String settleMonth, ConfigData data, List<AuditResponse> responseList) {
        String[] pathArr = data.getFileName().split(",");//暂估   销暂估
        directoryAbsolutePath = directoryAbsolutePath + data.getDirectory();
        String estimateUrl = directoryAbsolutePath + "\\" + pathArr[0];
        String noEstimateUrl = directoryAbsolutePath + "\\" + pathArr[1];
        log.info("estimateUrl={}", estimateUrl);
        log.info("noEstimateUrl={}", noEstimateUrl.toString());

        List<String> estimateFileList = listExcelFiles(estimateUrl);
        List<String> noEstimateFileList = listExcelFiles(noEstimateUrl);

        JSONObject param = JSONObject.parseObject(data.getParameter());
        JSONObject estimateMoney = param.getJSONObject("estimateMoney");
        JSONArray fileList = estimateMoney.getJSONArray("fileList");
        JSONArray provisionalList = estimateMoney.getJSONArray("noProvisionalList");
        Map<String, String> fileMap = tranMap(fileList);
        Map<String, String> provisionalMap = tranMap(provisionalList);

        //校验两边目录下文件的数量是否一致
        if (estimateFileList.size() != noEstimateFileList.size()) {
            AuditResponse response = new AuditResponse();
            response.setReportName(data.getReportName());
            response.setSettleMonth(settleMonth);
            response.setDirectory(data.getDirectory());
            response.setAuditEntryStr(DictMap.auditEntryMap.get(data.getAuditEntry()));
            response.setRspCode(response.FAIL);
            response.setRspDesc("暂估和报表文件夹下的文件数量不一致 暂估下文件数量=" + estimateFileList.size() + "销暂估下文件数量=" + noEstimateFileList.size());
            responseList.add(response);
            return responseList;
        }
        //轮询每一个暂估的文件  找对应的正常的文件
        for (String fileName : estimateFileList) {
            if (fileName.contains("暂估")) {
                DateTime inMonthDate = DateUtil.parse(settleMonth, "yyyyMM");
                DateTime expDate = DateUtil.offsetMonth(inMonthDate, -1);
                String beforeMonth = DateUtil.format(expDate, "yyyyMM");
                String fileNewName = fileName.replace("_暂估", "").replace(settleMonth, beforeMonth).trim();
                //H301_政企条线产品支撑费结算单-总部结算模式_202410-202412.xls H301_政企条线产品支撑费结算单-总部结算模式_202410-202411.xls
                if (!noEstimateFileList.contains(fileNewName)) {
                    AuditResponse response = new AuditResponse();
                    response.setReportName(data.getReportName());
                    response.setSettleMonth(settleMonth);
                    response.setDirectory(data.getDirectory());
                    response.setAuditEntryStr(DictMap.auditEntryMap.get(data.getAuditEntry()));
                    response.setRspCode(response.FAIL);
                    response.setRspDesc("暂估文件为=" + fileName + "在销暂估文件夹中没有对应的销暂估文件："+fileNewName);
                    responseList.add(response);
                    return responseList;
                }
            }
            //文件拼装
            String configCheckfileName = fileName.replace(".xls", "").replace(".xlsx", "");
            String[] nameArr = configCheckfileName.split("_");
            String endName = "";

            if (fileName.contains("公播音乐合作伙伴产品业务提供商结算收入报表")) {//特殊处理
                endName = nameArr[0];
            } else if (fileName.contains("应收收入_调账")) {
                endName = nameArr[0] + "_" + nameArr[1] + "_" + nameArr[3];
            } else {
                endName = nameArr[0] + "_" + nameArr[1];
                //D313_政企条线专业公司标准产品DICT服务包结算单 ||| D313_政企条线专业公司标准产品DICT服务包结算单_分表
                if ("分表".equals(nameArr[nameArr.length - 1])) {
                    endName = nameArr[0] + "_" + nameArr[1] + "_分表";
                }
                if ("特殊分表".equals(nameArr[nameArr.length - 1])) {
                    endName = nameArr[0] + "_" + nameArr[1] + "_特殊分表";
                }
            }
            //将(*)的内容删掉
            endName = endName.replaceAll("\\(.*?\\)", "");
            log.info("当前稽核文件:{},处理后的文件名字:{}", fileName, endName);
            //进行比对  加载文件 判断文件属于哪一类 fileList
            List<String> keyList = new ArrayList<>();
            for (int i = 0; i < fileList.size(); i++) {
                JSONObject fileObject = fileList.getJSONObject(i);
                String key = new ArrayList<>(fileObject.keySet()).get(0);
                keyList.add(key);
            }

            if (keyList.contains(endName)) {
                String filePath = estimateUrl + "\\" + fileName;
                DateTime inMonthDate = DateUtil.parse(settleMonth, "yyyyMM");
                DateTime expDate = DateUtil.offsetMonth(inMonthDate, -1);
                String beforeMonth = DateUtil.format(expDate, "yyyyMM");
                String fileNewName = fileName.replace("_暂估", "").replace(settleMonth, beforeMonth).trim();
                String noEstimateFilePath = noEstimateUrl + "\\" + fileNewName;//正常的文件路径
                //
                String parameterValue = fileMap.get(endName);
                String parameterValue2 = provisionalMap.get(endName);


                JSONObject paramObj = JSONObject.parseObject(parameterValue);
                Integer startRow = null;
                if (CommonUtil.isNotNull(paramObj.getString("startRow"))) {
                    startRow = Integer.parseInt(paramObj.getString("startRow"));
                }
                Integer endRow = null;
                if (CommonUtil.isNotNull(paramObj.getString("endRow"))) {
                    endRow = Integer.parseInt(paramObj.getString("endRow"));
                }
                Integer startColumn = null;
                if (CommonUtil.isNotNull(paramObj.getString("startColumn"))) {
                    startColumn = Integer.parseInt(paramObj.getString("startColumn"));
                }

                Integer[] columns = null;
                if (CommonUtil.isNotNull(paramObj.getString("columns"))) {
                    columns = getIntList(paramObj.getString("columns"));
                }
                //正常报表参数
                JSONObject paramObj2 = JSONObject.parseObject(parameterValue2);
                Integer startRow2 = Integer.parseInt(paramObj2.getString("startRow"));
                Integer endRow2 = Integer.parseInt(paramObj2.getString("endRow"));
                Integer startColumn2 = null;
                if (CommonUtil.isNotNull(paramObj2.getString("startColumn"))) {
                    startColumn2 = Integer.parseInt(paramObj2.getString("startColumn"));
                }
                Integer[] columns2 = null;
                if (CommonUtil.isNotNull(paramObj2.getString("columns"))) {
                    columns2 = getIntList(paramObj2.getString("columns"));
                }
                String items = null;
                if (CommonUtil.isNotNull(paramObj2.getString("items"))) {
                    items = paramObj2.getString("items");
                }
                String itemsFeeName = null;
                if (CommonUtil.isNotNull(paramObj2.getString("itemsFeeName"))) {
                    itemsFeeName = paramObj2.getString("itemsFeeName");
                }
                Integer sheetNums = getsheetNum(new File(filePath));
                //SQL Results&1&1 -> 230   1行 1列  230
                for (int i = 0; i < sheetNums; i++) { //轮询sheet
                    log.info("正在稽核filePath={}", filePath); //暂估
                    List<Map<String, Object>> estimateFileMsg = getColumnDataList2(filePath, i, startRow, endRow, columns, startColumn);
                    //noEstimateFilePath ，267369511.44 正常报表  广东： 26,520,247.00
                    List<Map<String, Object>> noEstimateFileMsg = getColumnDataList2(noEstimateFilePath, i, startRow2, endRow2, columns2, startColumn2);

                    // 过滤并统计,初始化总和
                    BigDecimal noProvisionalValue = BigDecimal.ZERO;
                    if (!StringUtils.isBlank(items)) {
                        boolean flag = false;
                        for (Map<String, Object> stringObjectMap : estimateFileMsg) {
                            flag = stringObjectMap.keySet().stream()
                                    .anyMatch(value -> value.contains("江苏") || value.contains("浙江"));
                        }
                        if (!flag) {
                            String[] split = items.split(",");
                            List<String> excelNames = Arrays.asList(split);
                            List<String> vagueName = excelNames.stream().filter(o -> o.contains("%")).collect(Collectors.toList());
                            List<String> excelFeeNames = new ArrayList<>();
                            if (!StringUtils.isBlank(itemsFeeName)) {
                                String[] splitFee = itemsFeeName.split(",");
                                excelFeeNames = Arrays.asList(splitFee);
                            }
                            for (Map<String, Object> stringObjectMap : noEstimateFileMsg) {
                                boolean flaseFieter = false;
                                boolean isFeeFieter = false;
                                for (String key : stringObjectMap.keySet()) {
                                    String[] endStr = key.split("&");
                                    String endStrColumns = endStr[2];
                                    if (null != columns2) {
                                        if (columns2.length == 2) {
                                            if (endStrColumns.equals(columns2[0].toString())) {
                                                //1。先进行模糊匹配
                                                if (!CollectionUtils.isEmpty(vagueName)) {
                                                    boolean contains = vagueName.stream()
                                                            .anyMatch(prefix -> {
                                                                Object value = stringObjectMap.get(key);
                                                                return value != null &&
                                                                        String.valueOf(value).startsWith(prefix.replace("%", ""));
                                                            });
                                                    if (contains) {
                                                        flaseFieter = true;
                                                        break;
                                                    }
                                                }
                                                boolean contains = excelNames.contains(String.valueOf(stringObjectMap.get(key)));
                                                if (contains) {
                                                    flaseFieter = true;
                                                    break;
                                                }
                                            }

                                        }
                                        if (columns2.length == 3) {
                                            if (endStrColumns.equals(columns2[0].toString())) {
                                                //1。先进行模糊匹配
                                                if (!CollectionUtils.isEmpty(vagueName)) {
                                                    boolean contains = vagueName.stream()
                                                            .anyMatch(prefix -> {
                                                                Object value = stringObjectMap.get(key);
                                                                return value != null &&
                                                                        String.valueOf(value).startsWith(prefix.replace("%", ""));
                                                            });
                                                    if (contains) {
                                                        flaseFieter = true;
                                                        break;
                                                    }
                                                }
                                                boolean contains = excelNames.contains(String.valueOf(stringObjectMap.get(key)));
                                                if (contains) {
                                                    flaseFieter = true;
                                                    break;
                                                } else {
                                                    // 使用 Stream 拆分并分别收集到两个集合
                                                    List<String> productName = excelFeeNames.stream()
                                                            .map(s -> s.split("&")[0]) // 获取产品部分
                                                            .collect(Collectors.toList());

                                                    List<String> feeName = excelFeeNames.stream()
                                                            .map(s -> s.split("&")[1]) // 获取费项部分
                                                            .collect(Collectors.toList());

                                                    String feeKey = key.substring(0, key.length() - 1) + columns2[1];
                                                    if (productName.contains(String.valueOf(stringObjectMap.get(key))) && feeName.stream().anyMatch(name -> name.equals(String.valueOf(stringObjectMap.get(feeKey))))) {
                                                        flaseFieter = true;
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                for (String key : stringObjectMap.keySet()) {
                                    String[] endStr = key.split("&");
                                    String endStrColumns = endStr[2];
                                    if (null != columns2) {
                                        if (columns2.length == 2) {
                                            if (endStrColumns.equals(columns2[1].toString()) && !flaseFieter) {
                                                String strValue = (String) stringObjectMap.get(key);
                                                String numberStr = strValue.replace(",", "");
                                                // 使用 BigDecimal 进行金额计算
                                                BigDecimal number = new BigDecimal(numberStr);
                                                noProvisionalValue = noProvisionalValue.add(number); // 累加
                                            }
                                        }
                                        if (columns2.length == 3) {
                                            if (endStrColumns.equals(columns2[2].toString()) && !flaseFieter) {
                                                String strValue = (String) stringObjectMap.get(key);
                                                String numberStr = strValue.replace(",", "");
                                                // 使用 BigDecimal 进行金额计算
                                                BigDecimal number = new BigDecimal(numberStr);
                                                noProvisionalValue = noProvisionalValue.add(number); // 累加
                                            }
                                        }
                                    }
                                }
                            }
                            //285082844.22
                            //285082819.22，广东：50908057.31 上海：59559910.83 59559910.83
                            String estimateValue = null;
                            for (Map.Entry<String, Object> stringObjectEntry : estimateFileMsg.get(0).entrySet()) {
                                estimateValue = String.valueOf(stringObjectEntry.getValue()).replace(",", "");
                                String key = stringObjectEntry.getKey();
                                //false 则稽核失败
                                if (!compareNumbers(estimateValue, noProvisionalValue.toString(), fileName, fileNewName, i, key)) {
                                    String rspDesc = "以暂估为主的稽核: 暂估文件=" + fileName + ",正常报表文件=" + fileNewName + ",sheetNums=" + i + ",稽核具体行数=" + key + "失败,暂估文件value=" + estimateValue + ",正常文件vlaue=" + noProvisionalValue;
                                    log.info(rspDesc);
                                    AuditResponse response = new AuditResponse();
                                    response.setReportName(data.getReportName());
                                    response.setSettleMonth(settleMonth);
                                    response.setDirectory(data.getDirectory());
                                    response.setAuditEntryStr(DictMap.auditEntryMap.get(data.getAuditEntry()));
                                    response.setRspCode(response.FAIL);
                                    response.setRspDesc(rspDesc);
                                    responseList.add(response);
                                }
                            }
                        }
                    } else {
                        for (int j = 0; j < estimateFileMsg.size(); j++) {//每一行数据
                            Map<String, Object> estimateList = estimateFileMsg.get(j);
                            Map<String, Object> noEstimateList = noEstimateFileMsg.get(j);
                            for (String key : estimateList.keySet()) {//key = SQL Results&1&1
                                //暂估的value
                                String estimateValue = String.valueOf(estimateList.get(key)).replace(",", "");//230
                                //销暂估的value
                                String noEstimateKey = key.replace("_暂估", "");
                                String noEstimateValue = String.valueOf(noEstimateList.get(noEstimateKey)).replace(",", "");//230
                                //false 则稽核失败
                                if (!compareNumbers(estimateValue, noEstimateValue, fileName, fileNewName, i, key)) {
                                    String rspDesc = "以暂估为主的稽核: 暂估文件=" + fileName + ",正常报表文件=" + fileNewName + ",sheetNums=" + i + ",稽核具体行数=" + key + "失败,暂估文件value=" + estimateValue + ",正常报表文件vlaue=" + noEstimateValue;
                                    log.info(rspDesc);
                                    AuditResponse response = new AuditResponse();
                                    response.setReportName(data.getReportName());
                                    response.setSettleMonth(settleMonth);
                                    response.setDirectory(data.getDirectory());
                                    response.setAuditEntryStr(DictMap.auditEntryMap.get(data.getAuditEntry()));
                                    response.setRspCode(response.FAIL);
                                    response.setRspDesc(rspDesc);
                                    responseList.add(response);
                                }
                            }
                        }
                    }
                }

            } else {
                log.info("fileName处理后的文件名称=" + endName + ",不在parameter配置内,不参与稽核");
            }
        }
        return responseList;
    }


    /**
     * 如果两个数都是null 则直接返回
     * 判断两个字符串是否都是数字且是相反数
     *
     * @param str1 第一个字符串
     * @param str2 第二个字符串
     * @return 如果都是数字且是相反数返回true，否则返回false
     */
    public static boolean areOppositeNumbers(String str1, String str2, String fileName, String fileNewName, int sheetNums, String key) {
        if ("null".equals(str1) && "null".equals(str2)) {
            String rspDesc = "以暂估为主的稽核: 暂估文件=" + fileName + ",销暂估文件=" + fileNewName + ",sheetNums=" + sheetNums + ",稽核具体行数=" + key + "失败,暂估文件值=" + str1 + ",销暂估文件值=" + str2;
            log.info(rspDesc);
            return true;
        }
        // 验证两个字符串是否都是数字
        if (!isNumeric(str1) || !isNumeric(str2)) {
            return false;
        }

        // 将字符串转换为BigDecimal
        BigDecimal num1 = new BigDecimal(str1);
        BigDecimal num2 = new BigDecimal(str2);

        // 判断两个BigDecimal是否是相反数
        return num1.compareTo(num2.negate()) == 0;
    }

    public static boolean compareNumbers(String str1, String str2, String fileName, String fileNewName, int sheetNums, String key) {
        if ("null".equals(str1) && "null".equals(str2)) {
            String rspDesc = "以暂估为主的稽核: 暂估文件=" + fileName + ",销暂估文件=" + fileNewName + ",sheetNums=" + sheetNums + ",稽核具体行数=" + key + "失败,暂估文件值=" + str1 + ",销暂估文件值=" + str2;
            log.info(rspDesc);
            return true;
        }
        // 验证两个字符串是否都是数字
        if (!isNumeric(str1) || !isNumeric(str2)) {
            return false;
        }

        // 将字符串转换为BigDecimal
        BigDecimal num1 = new BigDecimal(str1);
        BigDecimal num2 = new BigDecimal(str2);

        // 判断两个BigDecimal是否是相等
        return num1.compareTo(num2) == 0;
    }

    /**
     * 判断字符串是否是数字
     *
     * @param str 字符串
     * @return 如果是数字返回true，否则返回false
     */
    private static boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        try {
            new BigDecimal(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public static boolean contains(String[] array, String element) {
        for (String e : array) {
            if (element.equals(e)) {
                return true;
            }
        }
        return false;
    }


    /**
     * 读取该目录下的所有xlsx文件名字
     * D:\工作\日常\工作\上线文档\2024\稽核工具\需求4  202409
     * D:\工作\日常\工作\上线文档\2024\稽核工具\需求4  202409/评估金额\暂估
     */
    public static List<String> listExcelFiles(String directoryPath) {
        List<String> excelFiles = new ArrayList<>();
        File directory = new File(directoryPath);
        File[] files = directory.listFiles();

        if (files != null) {
            for (File file : files) {
                if (file.isFile() && (file.getName().toLowerCase().endsWith(".xlsx") || file.getName().toLowerCase().endsWith(".xls"))) {
                    excelFiles.add(file.getName());
                }
            }
        }

        return excelFiles;
    }


    public Integer getsheetNum(File file) {
        int numberOfSheets = 1;
        try {
            FileInputStream fis = new FileInputStream(file);
            Workbook workbook = WorkbookFactory.create(fis);
            //sheet的数量
            numberOfSheets = workbook.getNumberOfSheets();
        } catch (Exception e) {
            log.info("sheet数量异常");
        }
        return numberOfSheets;
    }
}
