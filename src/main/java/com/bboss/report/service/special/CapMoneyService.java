package com.bboss.report.service.special;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bboss.report.component.DictMap;
import com.bboss.report.model.AuditResponse;
import com.bboss.report.model.ConfigData;
import com.bboss.report.service.BaseService;
import com.bboss.report.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.util.*;

/**
 * 封顶金额的判断
 * 1 从指定文件中取到产品和每个省的金额
 * 2 从配置文件中取到 产品和每个省的封顶金额
 * 3 进行对比 是否图片封顶金额
 */
@Service
@Slf4j
public class CapMoneyService extends BaseService{
    //入口
    public List<AuditResponse> capMoneyJudget(String directoryAbsolutePath,String settleMonth,ConfigData data,List<AuditResponse> responseList){
        String[] pathArr = data.getFileName().split(",");//产品数据    封顶配置
        directoryAbsolutePath = directoryAbsolutePath + data.getDirectory();
        String productData = directoryAbsolutePath+"\\"+pathArr[0];
        String capConfig = directoryAbsolutePath+"\\"+pathArr[1];


        log.info("productData={}",productData);
        log.info("capConfig={}",capConfig);

        List<String> productDataFileList =  listExcelFiles(productData);
//        List<String> capConfigFileList =  listExcelFiles(capConfig);

        //获取配置
        JSONObject allMsg = JSONObject.parseObject(data.getParameter());
       // JSONObject param = JSONObject.parseObject(data.getParameter());
        JSONObject param = allMsg.getJSONObject("cappingMoney");


        JSONObject capConifg = param.getJSONObject("capConifg");
        String sheetNum = capConifg.getString("sheetNum");
        String capFileName = capConifg.getString("fileName");
        String productString = capConifg.getString("productList");
        List<String> productList = Arrays.asList(productString.split(","));

        //先读取封顶金额   存入内存
        Map<String,String> capMoneyMap = new HashMap<>();
        String filePath = capConfig+"\\"+capFileName;
        //取到sheet的全部数据
        List<Map<String, Object>> capMoneyConfigList = getColumnDataList2(filePath, 0, 0, 0, null ,0);
        //先将产品名称和列对应上   之后在从下面取值
        Map<String, Object> capMoneyConfig = capMoneyConfigList.get(0);
        // key =列   value= 产品名称
        Map<String,String> lineToProductName = new HashMap<>();
        for (String key : capMoneyConfig.keySet()) {//key = SQL Results&1&1
            String[] keyArr = key.split("&");
            String line = keyArr[keyArr.length-1];
            lineToProductName.put(line,(String) capMoneyConfig.get(key));
        }

        for (int j = 2; j < capMoneyConfigList.size(); j++) {//从第三行数据开始读取
            Map<String, Object> capMoneyData = capMoneyConfigList.get(j);
            String province = "";
            for(String key: capMoneyData.keySet()){
                String[] keyArr = key.split("&");
                String line = keyArr[keyArr.length-1];
                if("0".equals(line)){
                    province = (String) capMoneyData.get(key);
                    continue;
                }
                String productName = lineToProductName.get(line);
                String capMoneykey = productName+"-"+province;
                String money = String.valueOf(capMoneyData.get(key)).replace(",", "");//230
                capMoneyMap.put(capMoneykey,money);
            }
        }


        //从各个文件中取到  产品-省份的金额  然后比对
        Map<String,String> productDataMap = new HashMap<>();
        JSONArray productDataConfigArr = param.getJSONArray("productDataConfig");

        //配置的文件数量和 真实的要保持一直
        if(productDataConfigArr.size()!=productDataFileList.size()){
            AuditResponse response = new AuditResponse();
            response.setReportName(data.getReportName());
            response.setSettleMonth(settleMonth);
            response.setDirectory(data.getDirectory());
            response.setAuditEntryStr(DictMap.auditEntryMap.get(data.getAuditEntry()));
            response.setRspCode(response.FAIL);
            response.setRspDesc("配置的文件数量与稽核文件路径下的数量不一致,配置文件下文件数量="+productDataConfigArr.size()+"稽核文件路径下文件数量="+productDataFileList.size());
            responseList.add(response);
            return responseList;
        }

        for(int i=0;i<productDataConfigArr.size();i++){
            JSONObject productDataConfig = productDataConfigArr.getJSONObject(i);
            String fileName = productDataConfig.getString("fileName");
            String productName = productDataConfig.getString("productName");
            List<String> productNameList = Arrays.asList(productName.split(","));

            //获取到文件名称
            for(String proFileName:productDataFileList){
                if(proFileName.contains(fileName)){
                    fileName = proFileName;
                    break;
                }
            }
            //当前文件的整体路径
            String proFilePath = productData+"\\"+fileName;

            //取到sheet的全部数据
            List<Map<String, Object>> productDataConfigList = getColumnDataList2(proFilePath, 0, 3, 8, null ,1);

            //通过第一行 找到产品和对应的列数据
            Map<String, Object> productDataMsg = productDataConfigList.get(0);
            for(String key:productDataMsg.keySet()){

                //可能涉及多个产品 也就是有多个 line  这里直接循环得了
                String productNames = String.valueOf(productDataMsg.get(key)).replace(",", "");
                for(String proName:productNameList){//1个文件可能有多个产品
                    if(proName.equals(productNames)){ //产品名字一样，  确认列的    //直接查询
                        String[] keyArr = key.split("&");
                        String line1 = keyArr[keyArr.length-1];//获取到列
                        String line2 = String.valueOf(Integer.parseInt(line1)+1);

                        //循环取数据
                        for (int j = 3; j < 34; j++) {//从第三行数据开始读取
                            Map<String, Object> productDataConifgMsg = productDataConfigList.get(j);
                            String province ="";
                            String taxMoney = "";
                            String noTaxMoney = "";
                            for(String dataKey:productDataConifgMsg.keySet()){
                                if(dataKey.endsWith("&1")){//第一行
                                    province = String.valueOf(productDataConifgMsg.get(dataKey)).replace(",", "");
                                }
                                if(dataKey.endsWith("&"+line1)){
                                    noTaxMoney = String.valueOf(productDataConifgMsg.get(dataKey)).replace(",", "");
                                }
                                if(dataKey.endsWith("&"+line2)){
                                    taxMoney = String.valueOf(productDataConifgMsg.get(dataKey)).replace(",", "");
                                }
                            }
                            //进行金额相加
                            String sumMoney = new BigDecimal(taxMoney).add(new BigDecimal(noTaxMoney)).toString();
                            String productDataMapKey = proName+"-"+province;
                            productDataMap.put(productDataMapKey,sumMoney);
                        }
                    }
                }
            }
        }

        //进行比对OnePark智慧园区-山西
        for(String key:productDataMap.keySet()) {
            log.info("key={}",key);
            BigDecimal productMoney = new BigDecimal(productDataMap.get(key)).abs();
            BigDecimal capMoney = new BigDecimal(capMoneyMap.get(key));
            //如果产品的金额  大于 封顶   capMoney==0 说明上不封顶
            boolean flag = capMoney.compareTo(new BigDecimal(0))==0;  //为0的时候 不校验   true 的时候不校验       不为0 false 的时候 校验
            if (!flag && productMoney.compareTo(capMoney) == 1) {// productMoney>capMoney
                String rspDesc = "产品-省份=" + key + "稽核失败,省专文件中读取到的金额=" +productMoney.toString() +" 封顶文件的金额=" + capMoney.toString() + ";";
                AuditResponse response = new AuditResponse();
                response.setReportName(data.getReportName());
                response.setSettleMonth(settleMonth);
                response.setDirectory(data.getDirectory());
                response.setAuditEntryStr(DictMap.auditEntryMap.get(data.getAuditEntry()));
                response.setRspCode(response.FAIL);
                response.setRspDesc(rspDesc);
                responseList.add(response);
            }
        }
        return responseList;
    }



    /**
     * 读取该目录下的所有xlsx文件名字
     * D:\工作\日常\工作\上线文档\2024\稽核工具\需求4  202409
     * D:\工作\日常\工作\上线文档\2024\稽核工具\需求4  202409/评估金额\暂估
     */
    public static List<String> listExcelFiles(String directoryPath) {
        List<String> excelFiles = new ArrayList<>();
        File directory = new File(directoryPath);
        File[] files = directory.listFiles();

        if (files != null) {
            for (File file : files) {
                if (file.isFile() && (file.getName().toLowerCase().endsWith(".xlsx")||file.getName().toLowerCase().endsWith(".xls"))) {
                    excelFiles.add(file.getName());
                }
            }
        }

        return excelFiles;
    }


    public Integer getsheetNum(File file){
        int numberOfSheets = 1;
        try{
            FileInputStream fis = new FileInputStream(file);
            Workbook workbook = WorkbookFactory.create(fis);
            //sheet的数量
            numberOfSheets = workbook.getNumberOfSheets();
        }catch (Exception e){
            log.info("sheet数量异常");
        }
        return numberOfSheets;
    }
}
