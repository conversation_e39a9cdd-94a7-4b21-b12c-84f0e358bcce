package com.bboss.report.service;

import cn.hutool.core.util.StrUtil;
import com.bboss.report.component.DictMap;
import com.bboss.report.model.AuditResponse;
import com.bboss.report.model.ExcelParam;
import com.bboss.report.model.ParamData;
import com.bboss.report.service.special.CheckPdfInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * 10009	政企分公司主办集团客户业务结算单_分省(应收)
 * 10010	政企分公司主办集团客户业务结算单_分省(实收)
 * 10019	省公司主办集团客户业务分省明细结算应收
 * 10020	省公司主办集团客户业务分省明细结算实收
 * 10001	应收收入(总表)
 */
@Slf4j
@Service
public class ReportGeneralService extends BaseService implements ReportAuditService {

    @Autowired
    private CheckPdfInfoService checkPdfInfoService;

    @Override
    public List<String> getReportKey() {
        List<String> keyList = new ArrayList<>();
        keyList.add("10009");
        keyList.add("10010");
        keyList.add("10019");
        keyList.add("10020");
        keyList.add("10023");
        keyList.add("10027");
        keyList.add("10028");
        keyList.add("10029");
        keyList.add("10030");
        keyList.add("10031");
        keyList.add("10001");
        keyList.add("10002");
        keyList.add("10007");
        keyList.add("10008");
        keyList.add("10021");
        keyList.add("10161");
        keyList.add("10022");
        keyList.add("91008");
        keyList.add("91009");
        keyList.add("91010");
        keyList.add("91015");
        keyList.add("91016");
        keyList.add("91017");
        keyList.add("91018");
        keyList.add("91019");

        keyList.add("20000");
        keyList.add("20001");
        keyList.add("20002");
        keyList.add("20003");
        keyList.add("20004");
        keyList.add("20005");
        keyList.add("20006");
        keyList.add("20007");
        keyList.add("20008");
        keyList.add("20009");

        keyList.add("20040");
        keyList.add("20041");
        keyList.add("20042");
        keyList.add("20043");
        keyList.add("20044");
        return keyList;
    }

    /**
     * 1.读取excel 的所有sheet ，按sheet遍历
     * 2. 遍历报表对应的稽核项，并调用稽核项的方法
     *
     * @param paramData
     * @return
     */
    @Override
    public List<AuditResponse> execute(ParamData paramData) {
        return super.execute(paramData, "com.bboss.report.service.ReportGeneralService");
    }


    /**
     * 报表金额是否保留两位小数
     *
     * @param paramData
     * @return
     */
    public String keepTwoDecimals(ParamData paramData, ExcelParam excelParam) throws Exception {
        //需要6, 7, 8, 9, 10, 11, 12金额字段
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns);
        List<Map<String, Object>> dataFxList = super.dataFxList;
        return super.keepTwoDecimals(paramData, dataList,dataFxList);
    }

    /**
     * 报表金额是否保留两位小数
     *
     * @param paramData
     * @return
     */
    public String keepTwoDecimalsNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns,param1.getStartColumn());
        ExcelParam.Param param2 = excelParam.getParam2();
        List<Map<String, Object>> dataList2 = new ArrayList<>();
        if (param2 != null) {
            Integer[] columns2 = super.getColumns(param2);
            dataList2 = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        }
        return super.keepTwoDecimalsNotNull(paramData, dataList2,dataList);
    }


    /**
     * 制表人、IT中心部门审核、制表单位
     *
     * @param paramData
     * @return
     */
    public String endSign(ParamData paramData, ExcelParam excelParam) throws Exception {
        // 获取最后一行表尾签字处
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns);
        return super.endSignOneLine(paramData, dataList);
//        return super.endSignPlus(paramData, dataList);
    }


    /**
     * 制表人、IT中心部门审核、制表单位、IT中心领导审核
     *
     * @param paramData
     * @return
     */
    public String endSignPlus(ParamData paramData, ExcelParam excelParam) throws Exception {
        // 获取最后一行表尾签字处
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns, param1.getStartColumn());
        return super.endSignOneLinePlus(paramData, dataList);
//        return super.endSignPlus(paramData, dataList);
    }

    /**
     * “***”为空(江苏&浙江)
     *
     * @param paramData
     * @return
     */
    public String isEmptyJSZJ(ParamData paramData, ExcelParam excelParam) throws Exception {
        // 获取空的那一行
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns, param1.getStartColumn());
        return super.isEmptyJSZJ(paramData, dataList);
    }


    /**
     * 报表结算月账期稽核
     *
     * @param paramData
     * @return
     */
    public String settleMonth(ParamData paramData, ExcelParam excelParam) throws Exception {
        // 读取结算月
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns);
        return super.settleMonth(paramData, dataList);
    }

    /**
     * 应结入：税额 = 含税金额- 不含税金额，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String inTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取税额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        //读取含税金额, 不含税金额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        return super.taxAmount(paramData, taxAmounts, amounts, 2, param1.getItemName());
    }

    /**
     * 应结入：不含税金额= 含税金额/(1+税率)，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String inNoIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取不含税金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> notTaxAmounts = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        //读取含税金额
        if(StrUtil.isNotBlank(paramData.getTaxRate())){
            ExcelParam.Param param2 = excelParam.getParam2();
            Integer[] columns2 = super.getColumns(param2);
            List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
            return super.noIncludeTaxAmount(paramData, notTaxAmounts, amounts, param1.getItemName());
        }else{
            //读取 含税金额和税率
            ExcelParam.Param param2 = excelParam.getParam2();
            Integer[] columns2 = super.getColumns(param2);
            List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
            return super.noIncludeTaxAmountTaxRate(paramData, notTaxAmounts, amounts,  param1.getItemName(), param2.getType());
        }

    }

    /**
     * 应结入：含税金额=税额 +不含税金额，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String inIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取列含税金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        //读取不含税金额和税额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2);
        return super.includeTaxAmount(paramData, taxAmounts, amounts,  param1.getItemName());
    }

    /**
     * 应结出:税额 = 含税金额- 不含税金额，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String outTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取税额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList(paramData.getPathName(),  param1.getStartRow(), param1.getEndRow(), columns1);
        //读取含税金额, 不含税金额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2);
        return super.taxAmount(paramData, taxAmounts, amounts, 2,  param1.getItemName());
    }




    /**
     * 应结出“含税金额”=应结入“含税金额”-结算额“含税金额”
     *
     * @param paramData
     * @return
     */
    public String misMatchOutIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取应结出“含税金额”
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList2(paramData.getPathName(),  param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取应结入“含税金额” , 结算额“含税金额”
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.taxAmount(paramData, taxAmounts, amounts, 1,  param1.getItemName());
    }


    /**
     * 应结出：不含税金额= 含税金额/(1+税率)，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String outNoIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取不含税金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> notTaxAmounts = super.getColumnDataList(paramData.getPathName(),  param1.getStartRow(), param1.getEndRow(), columns1);
        //读取含税金额
        if(StrUtil.isNotBlank(paramData.getTaxRate())){
            ExcelParam.Param param2 = excelParam.getParam2();
            Integer[] columns2 = super.getColumns(param2);
            List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
            return super.noIncludeTaxAmount(paramData, notTaxAmounts, amounts, param1.getItemName());
        }else{
            ExcelParam.Param param2 = excelParam.getParam2();
            Integer[] columns2 = super.getColumns(param2);
            List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
            return super.noIncludeTaxAmountTaxRate(paramData, notTaxAmounts, amounts,  param1.getItemName(), param2.getType());
        }
    }

    /**
     * 应结出：含税金额=税额 +不含税金额，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String outIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取列含税金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        //读取不含税金额和税额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        return super.includeTaxAmount(paramData, taxAmounts, amounts,  param1.getItemName());
    }

    /**
     * “税率”的报表税率稽核
     *
     * @param paramData
     * @return
     */
    public String taxRate(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取税率
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(),param1.getStartRow(), param1.getEndRow(), columns1);
        return super.taxRate(paramData, dataList);
    }

    /**
     * “产品”不为空，不为全数字
     *
     * @param paramData
     * @return
     */
    public String productNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        /**
         * 读取产品列
         */
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.columnIsEmpty(paramData, dataList, "产品"));
        sb.append(super.columnIsNumbers(paramData, dataList, "产品"));
        return sb.toString();
    }


    /**
     * “集团客户”不为空
     *
     * @param paramData
     * @return
     */
    public String groupCustomerNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        return super.columnIsEmpty(paramData, dataList, "集团客户");
    }

    /**
     * “客户编码” 不为空
     *
     * @param paramData
     * @return
     */
    public String customerCodeNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        return super.columnIsEmpty(paramData, dataList, "客户编码");
    }

    /**
     * 厂家代码 不为空
     *
     * @param paramData
     * @return
     */
    public String manufacturerCodeNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        return super.columnIsEmpty(paramData, dataList, "厂家代码");
    }



    /**
     * 各产品名称 不为空，不为全数字
     *
     * @param paramData
     * @return
     */
    public String productNameNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        /**
         * 读取产品列
         */
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.columnIsEmpty(paramData, dataList, "各产品名称"));
        sb.append(super.columnIsNumbers(paramData, dataList, "各产品名称"));
        return sb.toString();
    }


    /**
     * “费项”不为空，不为全数字
     *
     * @param paramData
     * @return
     */
    public String chargesNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        /**
         * 读取费项
         */
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.columnIsEmpty(paramData, dataList, "费项"));
        sb.append(super.columnIsNumbers(paramData, dataList, "费项"));
        return sb.toString();
    }

    /**
     * “集团客户”不为空，不为全数字
     *
     * @param paramData
     * @return
     */
    public String groupCustomersNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        /**
         * 读取产品列
         */
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.columnIsEmpty(paramData, dataList, "产品"));
        sb.append(super.columnIsNumbers(paramData, dataList, "产品"));
        return sb.toString();
    }




    /**
     * 合计=对应列求和
     *
     * @param paramData
     * @return
     */
    public String sumColumn(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        StringBuffer sb = new StringBuffer();
        for (Integer column : columns1) {
            /**
             * 读取合计行
             */
            List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), new Integer[]{column});
            /**
             * 读取行
             */
            List<Map<String, Object>> dataList2 = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), new Integer[]{column});
            sb.append(super.sumColumn(paramData, dataList, dataList2,"合计不等于对应列求和"));
        }
        return sb.toString();
    }


    /**
     * 小计=对应列求和(江苏&浙江)
     *
     * @param paramData
     * @return
     */
    public String subSumColumnJSZJ(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        StringBuffer sb = new StringBuffer();
        for (Integer column : columns1) {
            /**
             * 读取合计行
             */
            List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), new Integer[]{column},param1.getStartColumn());
            /**
             * 读取行
             */
            List<Map<String, Object>> dataList2 = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), new Integer[]{column},param2.getStartColumn());
            sb.append(super.sumColumnJSZJ(paramData, dataList, dataList2,"合计不等于对应列求和"));
        }
        return sb.toString();
    }


    /**
     * 合计=小计(江苏&浙江)
     *
     * @param paramData
     * @return
     */
    public String matchSubSumJSZJ(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        StringBuffer sb = new StringBuffer();
        for (Integer column : columns1) {
            /**
             * 读取合计行
             */
            List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), new Integer[]{column},param1.getStartColumn());
            /**
             * 读取行
             */
            List<Map<String, Object>> dataList2 = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), new Integer[]{column},param2.getStartColumn());
            sb.append(super.sumColumnJSZJ(paramData, dataList, dataList2,"合计不等于对应列求和"));
        }
        return sb.toString();
    }


    /**
     * 应结入“***”“不含税金额”=应结出“不含税金额”的总和
     *
     * @param paramData
     * @return
     */
    public String misMatchInOutcludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        StringBuffer sb = new StringBuffer();
        /**
         * 读取合计行
         */
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        /**
         * 读取行
         */
        List<Map<String, Object>> dataList2 = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        sb.append(super.sumSingleColumn(paramData, dataList, dataList2,"合计不等于对应列求和"));

        return sb.toString();
    }

    /**
     * 应结入“***”“含税金额”=应结出“含税金额”的总和
     *
     * @param paramData
     * @return
     */
    public String misMatchInIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        StringBuffer sb = new StringBuffer();
        /**
         * 读取合计行
         */
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        /**
         * 读取行
         */
        List<Map<String, Object>> dataList2 = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        sb.append(super.sumSingleColumn(paramData, dataList, dataList2,"合计不等于对应列求和"));

        return sb.toString();
    }


    /**
     * 应结入“***”“税额”=应结出“税额”的总和
     *
     * @param paramData
     * @return
     */
    public String misMatchInOutTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        StringBuffer sb = new StringBuffer();
        /**
         * 读取合计行
         */
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        /**
         * 读取行
         */
        List<Map<String, Object>> dataList2 = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        sb.append(super.sumSingleColumn(paramData, dataList, dataList2,"合计不等于对应列求和"));

        return sb.toString();
    }


    /**
     * 结算额“***”=结算额中31省的金额总和的相反数
     *
     * @param paramData
     * @return
     */
    public String minusTaxAmountMarket(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        StringBuffer sb = new StringBuffer();
        for (Integer column : columns1) {
            /**
             * 读取合计行
             */
            List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), new Integer[]{column});
            Map<String, Object> map = dataList.get(0);
            //合计设置 相反数
            for (String key : map.keySet()) {
                String Value = String.valueOf(map.get(key));
                if(Value.startsWith("-")){
                    Value=Value.substring(1,Value.length());
                }else {
                    Value="-"+Value;
                }
                map.put(key,Value);
            }
            /**
             * 读取行
             */
            List<Map<String, Object>> dataList2 = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), new Integer[]{column});
            sb.append(super.sumSingleColumn(paramData, dataList, dataList2,"合计不等于对应列求和"));
        }
        return sb.toString();
    }



    /**
     * “结算额”的“含税金额”=“应结入”的“含税金额”-“应结出”的“含税金额”
     *
     * @param paramData
     * @return
     */
    public String settleIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取“结算额”的“含税金额”
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList(paramData.getPathName(),  param1.getStartRow(), param1.getEndRow(), columns1);
        //读取应结入”的“含税金额”,“应结出”的“含税金额”

        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2);
        return super.taxAmount(paramData, taxAmounts, amounts, param1.getType(), "“结算额”的“含税金额”=“应结入”的“含税金额”-“应结出”的“含税金额”");
    }

    /**
     * 除辽宁30省合计=合计-辽宁省份
     *
     */
    public String settleTotalMinusLiaoningProvince(ParamData paramData, ExcelParam excelParam) throws Exception {

        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList(paramData.getPathName(),  param1.getStartRow(), param1.getEndRow(), columns1);

        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);

        List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2);

        ExcelParam.Param param3 = excelParam.getParam3();
        Integer[] columns3 = super.getColumns(param3);

        List<Map<String, Object>> amounts2 = super.getColumnDataList(paramData.getPathName(),  param3.getStartRow(), param3.getEndRow(), columns3);

        return super.settleTotalMinus(paramData, taxAmounts, amounts,amounts2,param1.getItemName(),param1.getType());
    }

    /**
     * “实收账期”为当前账期
     *
     * @param paramData
     * @return
     * @throws Exception
     */
    public String paidInSettleMonth(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取“实收账期”
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(),  param1.getStartRow(), param1.getEndRow(), columns1);
        return super.settleMonth(paramData, dataList);
    }

    /**
     * “计费账期”不为空
     *
     * @param paramData
     * @return
     * @throws Exception
     */
    public String billingPeriodNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取“实收账期”
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(),  param1.getStartRow(), param1.getEndRow(), columns1);
        return super.columnIsEmpty(paramData, dataList, "计费账期");
    }

    /**
     * "结算对方省"不为空
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String settleOtherProvNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取“实收账期”
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(),  param1.getStartRow(), param1.getEndRow(), columns1);
        StringBuffer sb=new StringBuffer();
        sb.append(super.columnIsEmpty(paramData, dataList, "结算对方省"));
        sb.append(super.columnIsNumbers(paramData, dataList, "结算对方省"));
        return sb.toString() ;
    }

    /**
     * 报表签约主体稽核（帐套）
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String signSubject(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(),  param1.getStartRow(), param1.getEndRow(), columns1);
        StringBuffer sb=new StringBuffer();
        sb.append(super.signSubject(paramData, dataList, "帐套"));
        return sb.toString() ;
    }
    /**
     * “归属省”不为空，不为全数字
     *
     * @param paramData
     * @return
     */
    public String provNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        /**
         * 读取归属省
         */
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.columnIsEmpty(paramData, dataList,  param1.getItemName()));
        sb.append(super.columnIsNumbers(paramData, dataList,  param1.getItemName()));
        return sb.toString();
    }

    /**
     * 匹配报表头
     *
     * @param paramData
     * @return
     */
    public String matchHeader(ParamData paramData, ExcelParam excelParam) throws Exception {
        /**
         * 读取归属省
         */
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), 2, null, columns1);
        Map<String, Object> headerMap = super.getHeaderMap(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.matchHeader(paramData,dataList, headerMap,param1.getColumns()  ,param1.getItemName(),"matchHeader"));
        return  sb.toString();
    }




    public String matchDoubleHeader(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), 2, null, columns1);
        Map<String, Object> headerMap = super.getHeaderMap(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.matchHeader(paramData,dataList, headerMap,param1.getColumns()  ,param1.getItemName(),"matchDoubleHeader"));

        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> secondHeader = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        sb.append(super.matchDoubleHeader(paramData,secondHeader, param2.getItemName()));
        return sb.toString();
    }

    /**
     * 省公司的省份名称”与SHELL页省份名称之间的稽核
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String matchSheet(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        //广东&1&2 -> 省公司：广东
        List<Map<String, Object>> dataList =  super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);

        sb.append(super.matchSheet(paramData, dataList));
        return  sb.toString();
    }

    public String matchHeaderPlus(ParamData paramData, ExcelParam excelParam) throws Exception {
        /**
         * 读取归属省
         */
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), 2, null, columns1);
        Map<String, Object> headerMap = super.getHeaderMap(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        Integer type = param1.getType();
        String headerStr = "matchHeader";
        if (type != null && type == 1) {
            headerStr = "1";
        }
        sb.append(super.matchDynamiSheetHeader(paramData,dataList, headerMap,param1.getColumns()  ,param1.getItemName(),headerStr));
        return  sb.toString();
    }



    /**
     * “结算项目”不为空，不为全数字
     *
     * @param paramData
     * @return
     */
    public String settleItem(ParamData paramData, ExcelParam excelParam) throws Exception {
        /**
         * 读取结算项目
         */
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.columnIsEmpty(paramData, dataList, "结算项目"));
        sb.append(super.columnIsNumbers(paramData, dataList, "结算项目"));
        return sb.toString();
    }
    /**
     * 税额 = 含税金额- 不含税金额，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String taxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取税额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        Long startTime1= System.currentTimeMillis();
        List<Map<String, Object>> taxAmounts = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        Long endTime1= System.currentTimeMillis();
        log.info("读取税额用时：{} MS",endTime1-startTime1);
        //读取含税金额, 不含税金额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        log.info("读取含税金额, 不含税金额用时：{} MS",endTime1-startTime1);
        return super.taxAmount(paramData, taxAmounts, amounts, 2,"税额 = 含税金额- 不含税金额");
    }

    /**
     * 不含税金额= 含税金额/(1+税率)，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String noIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取不含税金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> notTaxAmounts = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        //读取含税金额
        if(StrUtil.isNotBlank(paramData.getTaxRate())){
            ExcelParam.Param param2 = excelParam.getParam2();
            Integer[] columns2 = super.getColumns(param2);
            List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
            return super.noIncludeTaxAmount(paramData, notTaxAmounts, amounts,"不含税金额= 含税金额/(1+税率)");
        }else{
            //读取 含税金额和税率
            ExcelParam.Param param2 = excelParam.getParam2();
            Integer[] columns2 = super.getColumns(param2);
            List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
            return super.noIncludeTaxAmountTaxRate(paramData, notTaxAmounts, amounts,"不含税金额= 含税金额/(1+税率)", param2.getType());
        }

    }

    /**
     * 含税金额=税额 +不含税金额，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String includeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取列含税金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        //读取不含税金额和税额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2);
        return super.includeTaxAmount(paramData, taxAmounts, amounts,"含税金额=税额 +不含税金额");
    }

    /**
     * “业务总收入”的含税金额=“政企结算金额”的含税金额+“省公司结算金额”的含税金额
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String includeTaxAmountAdd(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取列含税金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        //读取不含税金额和税额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2);
        return super.includeTaxAmount(paramData, taxAmounts, amounts, param1.getItemName());
    }

    /**
     * 农政通:总共收入=通信费+功能费
     *
     */
    public String aggregateIncomeCommunicationFunctionFee(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取总共收入金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        //读取通信费和功能费金额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2);
        return super.includeTaxAmount(paramData, taxAmounts, amounts, "农政通:总共收入=通信费+功能费");
    }

    /**
     * “应收合计”的“结算额”=所有结算方负值之和
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String totalCharge(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> totalList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> sumList = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        return super.settlePartySumColumn(paramData, totalList, sumList, param2.getItemName(),param2.getType());
    }
    /**
     * “应付合计”的“结算额”=所有结算方正值之和
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String totalPay(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> totalList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> sumList = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        return super.settlePartySumColumn(paramData, totalList, sumList, param2.getItemName(),param2.getType());
    }


    /**
     * “应收合计”的“结算额”=所有结算方负值之和(销暂估)
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String totalChargeTSE(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> totalList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> sumList = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        return super.settlePartySumColumn(paramData, totalList, sumList, param2.getItemName(),param2.getType());
    }
    /**
     * “应付合计”的“结算额”=所有结算方正值之和(销暂估)
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String totalPayTSE(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> totalList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> sumList = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        return super.settlePartySumColumn(paramData, totalList, sumList, param2.getItemName(),param2.getType());
    }


    /**
     * “应付合计”的“结算额”=所有结算方正值之和
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String  custAndSettleAmount (ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        return super.custAndSettleAmount(paramData, dataList,param1);
    }

    /**
     * “结算额”的“含税金额”=“市场化结算”下的各项“不含税金额”和“税额”的和
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String settleIncludeTaxAmountMarket(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取“结算额”的“含税金额”
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> dataList1 =super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取“市场化结算”下的各项“不含税金额”和“税额”
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> dataList2 =super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.multipleAddition(paramData, dataList1, dataList2, "“结算额”的“含税金额”不等于“市场化结算”下的各项“不含税金额”和“税额”的和");
    }

    /**
     * “XXX”不为空
     *
     * @param paramData
     * @return
     */
    public String notNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        sb.append(super.columnIsEmpty(paramData, dataList,  param1.getItemName()));
        return sb.toString();
    }


    /**
     * 中国移动车务通业务运营支撑费虚拟结算单:本期应结算金额=计费金额*结算比例(%)，保留两位小数，四舍五入
     *
     */
    public String aggregate(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取总共收入金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        //读取通信费和功能费金额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2);
        return super.includeTaxMultiply(paramData, taxAmounts, amounts,  param1.getItemName());
    }

    /**
     *
     * @param paramData
     * @param excelParam
     * @return
     */
    public String worldFileName(ParamData paramData, ExcelParam excelParam) throws Exception{
        return super.worldFileName(paramData.getFileName(),paramData.getPathName());
    }

    /**
     稽核 金额相加
     */
    public String moneyAdd(ParamData paramData, ExcelParam excelParam){
        return super.moneyAdd(paramData,excelParam);
    }


    public String moneyAddAutomatic(ParamData paramData, ExcelParam excelParam){
        return super.moneyAddAutomatic(paramData,excelParam);
    }

    public String moneyAddAutomaticDif(ParamData paramData, ExcelParam excelParam){
        return super.moneyAddAutomaticDif(paramData,excelParam);
    }

    /**
     * 税额=（不含税金额+税额）-round（（不含税金额+税额）/1.06,2）
     * 不含税金额=round（（不含税金额+税额）/1.06,2）
     *
     * @param paramData
     * @return
     */
    public String outTaxSkuAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        String pathName = paramData.getPathName();
        Integer sheetNums =  getsheetNum(new File(pathName));
        //SQL Results&1&1 -> 230   1行 1列  230
        StringBuffer stringBuffer = new StringBuffer();
        for(int i=0;i<sheetNums;i++) {//轮询sheet
            //读取税额
            ExcelParam.Param param1 = excelParam.getParam1();
            Integer[] columns1 = super.getColumns(param1);
            List<Map<String, Object>> taxAmounts = super.getColumnDataList(paramData.getPathName(), i, param1.getStartRow(), param1.getEndRow(), columns1);
            //读取含税金额, 不含税金额
            ExcelParam.Param param2 = excelParam.getParam2();
            Integer[] columns2 = super.getColumns(param2);
            List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(), i, param2.getStartRow(), param2.getEndRow(), columns2);
            String s = super.noIncludeTaxSkuAmount(paramData, taxAmounts, amounts, param1.getItemName());
            stringBuffer.append(s);
        }
        return stringBuffer.toString();
    }



    public String totalCalculatedAmount (ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取合计行
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> dataListA =super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> dataListB =super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        ExcelParam.Param param3 = excelParam.getParam3();
        Integer[] columns3 = super.getColumns(param3);
        List<Map<String, Object>> dataListC =super.getColumnDataList(paramData.getPathName(), param3.getStartRow(), param3.getEndRow(), columns3);
        return super.totalCalculatedAmount(paramData,dataListC,dataListB,dataListA,"总含税不等于“总不含税”+“总税额”");
    }

    /**
     * 特殊分表的sheet页名称稽核，名称需与表头省份一致，同时校验名称需为31省名称
     *
     * @param paramData
     * @return
     */
    public String matchSheetHeader(ParamData paramData, ExcelParam excelParam) throws Exception {
        /**
         * 读取归属省
         */
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), 2, null, columns1);
        Map<String, Object> headerMap = super.getHeaderMap(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.matchSheetHeader(paramData,dataList, headerMap,param1.getColumns()));
        return  sb.toString();
    }

    public Integer getsheetNum(File file){
        int numberOfSheets = 1;
        try{
            FileInputStream fis = new FileInputStream(file);
            Workbook workbook = WorkbookFactory.create(fis);
            //sheet的数量
            numberOfSheets = workbook.getNumberOfSheets();
        }catch (Exception e){
            log.info("sheet数量异常");
        }
        return numberOfSheets;
    }

    public String interSectionNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        sb.append(super.columnIsEmpty(paramData, dataList,  param1.getItemName()));
        return sb.toString();
    }

    public String comparePriceWord(ParamData paramData, ExcelParam excelParam) {
        return super.comparePriceWord(paramData, excelParam);
    }

    public String totalComparePrice(ParamData paramData, ExcelParam excelParam) {
        return  super.totalComparePrice(paramData, excelParam);
    }

    public String totalComparePriceAndTaxRate(ParamData paramData, ExcelParam excelParam) {
        return  super.totalComparePriceAndTaxRate(paramData, excelParam);
    }

    public String totalCompareToMorePrice(ParamData paramData, ExcelParam excelParam)  {
        return  super.totalCompareToMorePrice(paramData, excelParam);
    }

    public String compareTwoExcelProvinceTotalPrice(ParamData paramData, ExcelParam excelParam)  {
        return  super.compareTwoExcelProvinceTotalPrice(paramData, excelParam);
    }
}
