package com.bboss.report.service;

import cn.hutool.core.util.StrUtil;
import com.bboss.report.model.AuditResponse;
import com.bboss.report.model.ExcelParam;
import com.bboss.report.model.ParamData;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 移动云-合作伙伴
 */
@Slf4j
@Service
public class ReportPartnersService extends BaseService implements ReportAuditService{
    @Override
    public List<String> getReportKey() {
        List<String> keyList = new ArrayList<>();
        keyList.add("10103");
        keyList.add("10115");
        keyList.add("10104");
        keyList.add("10024");
        keyList.add("10025");
        keyList.add("10026");
        keyList.add("10117");
        keyList.add("10121");
        keyList.add("10122");
        keyList.add("10133");
        keyList.add("10125");
        keyList.add("10126");
        keyList.add("10127");
        keyList.add("10130");
        keyList.add("10132");
        keyList.add("10134");
        keyList.add("10135");
        keyList.add("10137");
        keyList.add("10118");
        keyList.add("10119");
        keyList.add("10114");
        keyList.add("91011");
        keyList.add("10148");
        keyList.add("91012");
        keyList.add("91013");
        keyList.add("91014");

        keyList.add("10150");

        keyList.add("10144");

        keyList.add("20010");
        keyList.add("20011");
        keyList.add("20012");
        keyList.add("20013");
        keyList.add("20014");
        keyList.add("20015");
        keyList.add("20016");
        keyList.add("20017");
        keyList.add("20018");
        keyList.add("20019");
        return  keyList;
    }

    /**
     * 1.读取excel 的所有sheet ，按sheet遍历
     * 2. 遍历报表对应的稽核项，并调用稽核项的方法
     *
     * @param paramData
     * @return
     */
    @Override
    public List<AuditResponse> execute(ParamData paramData) {

        return super.execute(paramData, "com.bboss.report.service.ReportPartnersService");
    }

    /**
     * “归属省”不为空，不为全数字
     *
     * @param paramData
     * @return
     */
    public String provNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        /**
         * 读取归属省
         */
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.columnIsEmpty(paramData, dataList,  param1.getItemName()));
        sb.append(super.columnIsNumbers(paramData, dataList,  param1.getItemName()));
        return sb.toString();
    }

    /**
     * 报表金额是否保留两位小数
     *
     * @param paramData
     * @return
     */
    public String keepTwoDecimals(ParamData paramData, ExcelParam excelParam) throws Exception {
        //需要6, 7, 8, 9, 10, 11, 12金额字段
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns,param1.getStartColumn());
        List<Map<String, Object>> dataFxList = super.dataFxList;
        return super.keepTwoDecimals(paramData, dataList ,dataFxList);
    }

    /**
     * 报表金额是否保留两位小数
     *
     * @param paramData
     * @return
     */
    public String keepTwoDecimalsNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns,param1.getStartColumn());
        ExcelParam.Param param2 = excelParam.getParam2();
        List<Map<String, Object>> dataList2 = new ArrayList<>();
        if (param2 != null) {
            Integer[] columns2 = super.getColumns(param2);
            dataList2 = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        }
        return super.keepTwoDecimalsNotNull(paramData, dataList2,dataList);
    }
    /**
     * 合计不为0
     * @param paramData
     * @param excelParam
     * @throws Exception
     */
    public String totalNotZero (ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取合计行
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> dataList =super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        return super.notZero(paramData, dataList, "合计为0");
    }






    /**
     * 应结出：含税金额=税额 +不含税金额，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String outIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取列含税金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取不含税金额和税额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.includeTaxAmount(paramData, taxAmounts, amounts,  param1.getItemName());
    }

    /**
     * 应结入：税额 = 含税金额- 不含税金额，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String inTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取税额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        //读取含税金额, 不含税金额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        return super.taxAmount(paramData, taxAmounts, amounts, 2, param1.getItemName());
    }

    /**
     * 应结入：不含税金额= 含税金额/(1+税率)，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String inNoIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取不含税金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> notTaxAmounts = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        //读取含税金额
        if(StrUtil.isNotBlank(paramData.getTaxRate())){
            ExcelParam.Param param2 = excelParam.getParam2();
            Integer[] columns2 = super.getColumns(param2);
            List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
            return super.noIncludeTaxAmount(paramData, notTaxAmounts, amounts, param1.getItemName());
        }else{
            //读取 含税金额和税率
            ExcelParam.Param param2 = excelParam.getParam2();
            Integer[] columns2 = super.getColumns(param2);
            List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
            return super.noIncludeTaxAmountTaxRate(paramData, notTaxAmounts, amounts,  param1.getItemName(), param2.getType());
        }

    }


    /**
     * “结算额”的“含税金额”=“应结入”的“含税金额”-“应结出”的“含税金额”
     *
     * @param paramData
     * @return
     */
    public String settleIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取“结算额”的“含税金额”
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList(paramData.getPathName(),  param1.getStartRow(), param1.getEndRow(), columns1);
        //读取应结入”的“含税金额”,“应结出”的“含税金额”

        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2);
        return super.taxAmount(paramData, taxAmounts, amounts, param1.getType(), "“结算额”的“含税金额”=“应结入”的“含税金额”-“应结出”的“含税金额”");
    }


    /**
     * “结算额”的“含税金额”=“市场化结算”下的各项“不含税金额”和“税额”的和
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String settleIncludeTaxAmountMarket(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取“结算额”的“含税金额”
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> dataList1 =super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取“市场化结算”下的各项“不含税金额”和“税额”
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> dataList2 =super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.multipleAddition(paramData, dataList1, dataList2, "“结算额”的“含税金额”不等于“市场化结算”下的各项“不含税金额”和“税额”的和");
    }


    /**
     * 应结入：含税金额=税额 +不含税金额，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String inIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取列含税金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        //读取不含税金额和税额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2);
        return super.includeTaxAmount(paramData, taxAmounts, amounts,  param1.getItemName());
    }

    /**
     * 应结出：不含税金额= 含税金额/(1+税率)，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String outNoIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取不含税金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> notTaxAmounts = super.getColumnDataList(paramData.getPathName(),  param1.getStartRow(), param1.getEndRow(), columns1);
        //读取含税金额
        if(StrUtil.isNotBlank(paramData.getTaxRate())){
            ExcelParam.Param param2 = excelParam.getParam2();
            Integer[] columns2 = super.getColumns(param2);
            List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
            return super.noIncludeTaxAmount(paramData, notTaxAmounts, amounts, param1.getItemName());
        }else{
            ExcelParam.Param param2 = excelParam.getParam2();
            Integer[] columns2 = super.getColumns(param2);
            List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
            return super.noIncludeTaxAmountTaxRate(paramData, notTaxAmounts, amounts,  param1.getItemName(), param2.getType());
        }
    }
    /**
     * 应结出:税额 = 含税金额- 不含税金额，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String outTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取税额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList2(paramData.getPathName(),  param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取含税金额, 不含税金额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.taxAmount(paramData, taxAmounts, amounts, 2,  param1.getItemName());
    }

    /**
     * 表尾签字处是否有制表人、IT中心部门审核、IT中心领导审核(已更改)
     * 制表人、IT中心部门审核、制表单位
     *
     * @param paramData
     * @return
     */
    public String endSign(ParamData paramData, ExcelParam excelParam) throws Exception {
        // 获取最后一行表尾签字处
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns,param1.getStartColumn());
        return super.endSignOneLine(paramData, dataList);
//        return super.endSignPlus(paramData, dataList);
    }

    /**
     * 制表人、IT中心部门审核、制表单位、IT中心领导审核
     *
     * @param paramData
     * @return
     */
    public String endSignPlus(ParamData paramData, ExcelParam excelParam) throws Exception {
        // 获取最后一行表尾签字处
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns, param1.getStartColumn());
        return super.endSignOneLinePlus(paramData, dataList);
//        return super.endSignPlus(paramData, dataList);
    }

    /**
     * “***”为空(江苏&浙江)
     *
     * @param paramData
     * @return
     */
    public String isEmptyJSZJ(ParamData paramData, ExcelParam excelParam) throws Exception {
        // 获取空的那一行
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns, param1.getStartColumn());
        return super.isEmptyJSZJ(paramData, dataList);
    }

    /**
     * 报表结算月账期稽核
     *
     * @param paramData
     * @return
     */
    public String settleMonth(ParamData paramData, ExcelParam excelParam) throws Exception {
        // 读取结算月

        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns,param1.getStartColumn());
        return super.settleMonth(paramData, dataList);
    }

    /**
     * “产品名称”不为空，不为全数字
     *
     * @param paramData
     * @return
     */
    public String productNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        /**
         * 读取产品列
         */
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        sb.append(super.columnIsEmpty(paramData, dataList, "产品名称"));
        sb.append(super.columnIsNumbers(paramData, dataList,"产品名称"));
        return sb.toString();
    }

    /**
     * “产品编码”不为空
     *
     * @param paramData
     * @return
     */
    public String productNumNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        return super.columnIsEmpty(paramData, dataList, "产品编码");
    }

    /**
     * “集团客户”不为空
     *
     * @param paramData
     * @return
     */
    public String groupCustomerNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        return super.columnIsEmpty(paramData, dataList, "集团客户");
    }

    /**
     * “客户编码” 不为空
     *
     * @param paramData
     * @return
     */
    public String customerCodeNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        return super.columnIsEmpty(paramData, dataList, "客户编码");
    }

    /**
     * 厂家代码 不为空
     *
     * @param paramData
     * @return
     */
    public String manufacturerCodeNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        return super.columnIsEmpty(paramData, dataList, "厂家代码");
    }



    /**
     * 各产品名称 不为空，不为全数字
     *
     * @param paramData
     * @return
     */
    public String productNameNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        /**
         * 读取产品列
         */
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.columnIsEmpty(paramData, dataList, "各产品名称"));
        sb.append(super.columnIsNumbers(paramData, dataList, "各产品名称"));
        return sb.toString();
    }




    /**
     * “厂家名称”不为空，不为全数字
     *
     * @param paramData
     * @return
     */
    public String companyNameNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        /**
         * 读取产品列
         */
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        sb.append(super.columnIsEmpty(paramData, dataList, "厂家名称"));
        sb.append(super.columnIsNumbers(paramData, dataList, "厂家名称"));
        return sb.toString();
    }
    /**
     * “厂家代码”不为空
     *
     * @param paramData
     * @return
     */
    public String companyNumNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        return super.columnIsEmpty(paramData, dataList,"厂家代码");
    }
    /**
     * “产品资费名称”不为空
     *
     * @param paramData
     * @return
     */
    public String tariffNameNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        sb.append(super.columnIsEmpty(paramData, dataList,"产品资费名称"));
        sb.append(super.columnIsNumbers(paramData, dataList, "产品资费名称"));
        return sb.toString();
    }
    /**
     * “产品资费代码”不为空
     *
     * @param paramData
     * @return
     */
    public String tariffNumNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        return super.columnIsEmpty(paramData, dataList,"产品资费代码");
    }

    /**
     * “集团客户”不为空，不为全数字
     *
     */
    public String groupCustomersProvinceNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {

        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.columnIsEmpty(paramData, dataList, "省市"));
        sb.append(super.columnIsNumbers(paramData, dataList, "省市"));
        return sb.toString();
    }

    /**
     * 产品类型不为空，不为全数字
     *
     */
    public String producTypeNumNotNll(ParamData paramData, ExcelParam excelParam) throws Exception {

        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.columnIsEmpty(paramData, dataList, "点播"));
        sb.append(super.columnIsNumbers(paramData, dataList, "点播"));
        return sb.toString();
    }

    /**
     * 合作厂家名称不为空，不为全数字
     *
     */
    public String cooperativeManufacturerNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {

        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.columnIsEmpty(paramData, dataList, "有限公司"));
        sb.append(super.columnIsNumbers(paramData, dataList, "有限公司"));
        return sb.toString();
    }

    /**
     * 税率为6
     *
     */
    public String taxRateIsSix(ParamData paramData, ExcelParam excelParam) throws Exception {

        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.taxRateSix(paramData, dataList, "税率"));
        return sb.toString();
    }

    /**
     * “费项”不为空，不为全数字
     *
     * @param paramData
     * @return
     */
    public String chargesNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        /**
         * 读取费项
         */
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.columnIsEmpty(paramData, dataList, "费项"));
        sb.append(super.columnIsNumbers(paramData, dataList, "费项"));
        return sb.toString();
    }


    /**
     * 本期考核预留金额（含税）=（本期应收可结算金额（含税）之和+本期核减之和）*考核预留比例
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String appraisalReservedAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        ExcelParam.Param param3 = excelParam.getParam3();
        Integer[] columns3 = super.getColumns(param3);
        List<Map<String, Object>> dataList1 = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        List<Map<String, Object>> dataList2 = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        List<Map<String, Object>> dataList3 = super.getColumnDataList2(paramData.getPathName(), param3.getStartRow(), param3.getEndRow(), columns3,param3.getStartColumn());
        return super.sumAndMult(paramData,dataList1,dataList2,dataList3,"本期考核预留金额（含税）不等于（本期应收可结算金额（含税）之和+本期核减之和）*考核预留比例");
    }

    /**
     * 实际支付金额的含税金额=（本期应收可结算金额（含税）之和+本期核减之和）-本期考核预留金额（含税）
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String actualAmountPaid(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        ExcelParam.Param param3 = excelParam.getParam3();
        Integer[] columns3 = super.getColumns(param3);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList1 = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        List<Map<String, Object>> dataList2 = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        List<Map<String, Object>> dataList3 = super.getColumnDataList2(paramData.getPathName(), param3.getStartRow(), param3.getEndRow(), columns3,param3.getStartColumn());
        return super.sumAndMinus(paramData,dataList1,dataList2,dataList3,"实际支付金额的含税金额 不等于（本期应收可结算金额（含税）之和+本期核减之和）-本期考核预留金额（含税）");
    }
    /**
     * 不含税金额= 含税金额/(1+税率)，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String noIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取不含税金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> notTaxAmounts = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取含税金额
        if(StrUtil.isNotBlank(paramData.getTaxRate())){
            ExcelParam.Param param2 = excelParam.getParam2();
            Integer[] columns2 = super.getColumns(param2);
            List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
            return super.noIncludeTaxAmount(paramData, notTaxAmounts, amounts,"不含税金额= 含税金额/(1+税率)");
        }else{
            //读取 含税金额和税率
            ExcelParam.Param param2 = excelParam.getParam2();
            Integer[] columns2 = super.getColumns(param2);
            List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
            return super.noIncludeTaxAmountTaxRate(paramData, notTaxAmounts, amounts,"不含税金额= 含税金额/(1+税率)",param2.getType());
        }

    }


    /**
     * 合计=对应列求和
     *
     * @param paramData
     * @return
     */
    public String sumColumn(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        StringBuffer sb = new StringBuffer();
        for (Integer column : columns1) {
            /**
             * 读取合计行
             */
            List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), new Integer[]{column},param1.getStartColumn());
            /**
             * 读取行
             */
            List<Map<String, Object>> dataList2 = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), new Integer[]{column},param2.getStartColumn());
            sb.append(super.sumColumn(paramData, dataList, dataList2,"合计不等于对应列求和"));
        }
        return sb.toString();
    }


    /**
     * 小计=对应列求和(江苏&浙江)
     *
     * @param paramData
     * @return
     */
    public String subSumColumnJSZJ(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        StringBuffer sb = new StringBuffer();
        for (Integer column : columns1) {
            /**
             * 读取合计行
             */
            List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), new Integer[]{column},param1.getStartColumn());
            /**
             * 读取行
             */
            List<Map<String, Object>> dataList2 = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), new Integer[]{column},param2.getStartColumn());
            sb.append(super.sumColumnJSZJ(paramData, dataList, dataList2,"合计不等于对应列求和"));
        }
        return sb.toString();
    }


    /**
     * 合计=小计(江苏&浙江)
     *
     * @param paramData
     * @return
     */
    public String matchSubSumJSZJ(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        StringBuffer sb = new StringBuffer();
        for (Integer column : columns1) {
            /**
             * 读取合计行
             */
            List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), new Integer[]{column},param1.getStartColumn());
            /**
             * 读取行
             */
            List<Map<String, Object>> dataList2 = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), new Integer[]{column},param2.getStartColumn());
            sb.append(super.sumColumnJSZJ(paramData, dataList, dataList2,"合计不等于对应列求和"));
        }
        return sb.toString();
    }


    /**
     * 应结入“***”“不含税金额”=应结出“不含税金额”的总和
     *
     * @param paramData
     * @return
     */
    public String misMatchInOutcludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        StringBuffer sb = new StringBuffer();
        /**
         * 读取合计行
         */
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        /**
         * 读取行
         */
        List<Map<String, Object>> dataList2 = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        sb.append(super.sumSingleColumn(paramData, dataList, dataList2,"合计不等于对应列求和"));

        return sb.toString();
    }

    /**
     * 应结入“***”“含税金额”=应结出“含税金额”的总和
     *
     * @param paramData
     * @return
     */
    public String misMatchInIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        StringBuffer sb = new StringBuffer();
        /**
         * 读取合计行
         */
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        /**
         * 读取行
         */
        List<Map<String, Object>> dataList2 = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        sb.append(super.sumSingleColumn(paramData, dataList, dataList2,"合计不等于对应列求和"));

        return sb.toString();
    }

    /**
     * 结算额“***”=结算额中31省的金额总和的相反数
     *
     * @param paramData
     * @return
     */
    public String minusTaxAmountMarket(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        StringBuffer sb = new StringBuffer();
        /**
         * 读取合计行
         */
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        /**
         * 读取行
         */
        List<Map<String, Object>> dataList2 = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        sb.append(super.sumSingleColumn(paramData, dataList, dataList2,"合计不等于对应列求和"));

        return sb.toString();
    }

    /**
     * 应结出“含税金额”=应结入“含税金额”-结算额“含税金额”
     *
     * @param paramData
     * @return
     */
    public String misMatchOutIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取应结出“含税金额”
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList2(paramData.getPathName(),  param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取应结入“含税金额” , 结算额“含税金额”
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.taxAmount(paramData, taxAmounts, amounts, 1,  param1.getItemName());
    }


    /**
     * 应结入“***”“税额”=应结出“税额”的总和
     *
     * @param paramData
     * @return
     */
    public String misMatchInOutTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        StringBuffer sb = new StringBuffer();
        for (Integer column : columns1) {
            /**
             * 读取合计行
             */
            List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), new Integer[]{column});
            /**
             * 读取行
             */
            List<Map<String, Object>> dataList2 = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), new Integer[]{column});
            sb.append(super.sumSingleColumn(paramData, dataList, dataList2,"合计不等于对应列求和"));
        }
        return sb.toString();
    }



    /**
     * XXXX=XXXX+XXXX
     * 结算金额合计(含税)=应结算金额(含税)+调整金额(含税)
     *  “业务总收入”的含税金额=“政企结算金额”的含税金额+“省公司结算金额”的含税金额
     *  合计=“应收合计”+“应付合计”
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String includeTaxAmountAdd(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取列含税金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取不含税金额和税额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.includeTaxAmount(paramData, taxAmounts, amounts, param1.getItemName());
    }

    /**
     * XXXX=XXXX+XXXX
     * 含税金额=结算金额合计（含税）+政企分公司应结算给合作伙伴的含税金额(政企直签客户)
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String includeTaxAmountAdd2(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取列含税金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取不含税金额和税额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.includeTaxAmount(paramData, taxAmounts, amounts, param1.getItemName());
    }
    /**
     * 含税金额=税额 +不含税金额，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String includeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取列含税金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取不含税金额和税额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.includeTaxAmount(paramData, taxAmounts, amounts,"含税金额=税额 +不含税金额");
    }

    /**
     * 不含税金额> 税金
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String   noIncludeTaxAmountGreaterTax (ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> dataList1 = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> dataList2 = super.getColumnDataList2(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.greater(paramData, dataList1, dataList2,"不含税金额没有大于税金");
    }


    /**
     * 税额 = 含税金额- 不含税金额，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String taxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取税额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取含税金额, 不含税金额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.taxAmount(paramData, taxAmounts, amounts, param2.getType(),"税金 = 含税金额- 不含税金额");
    }

    /**
     *  xxx=xxx
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String settleAmountEqualIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> dataList1 = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> dataList2 = super.getColumnDataList2(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.equal(paramData, dataList1, dataList2,param1.getItemName());
    }
    /**
     * “XXX”不为空
     *
     * @param paramData
     * @return
     */
    public String notNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        sb.append(super.columnIsEmpty(paramData, dataList,  param1.getItemName()));
        return sb.toString();
    }

    /**
     * 本期考核预留金额=本期结算金额*20%；
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String  amountMultByProportion(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> dataList1 = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> dataList2 = super.getColumnDataList2(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return    super.amountMultByProportion(paramData, dataList1, dataList2,0.2,param2.getItemName());
    }

    /**
     *实际支付金额总计 = 本期结算金额 - 本期考核预留金额+调整金额
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String  amountMinusAndAdd(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> dataList1 = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        List<Map<String, Object>> dataList2 = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.amountMinusAndAdd(paramData,dataList1,dataList2,"实际支付金额总计 = 本期结算金额 - 本期考核预留金额+调整金额") ;
    }
    /**
     * “应收合计”的“结算额”=所有结算方负值之和
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String totalCharge(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> totalList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> sumList = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.settlePartySumColumn(paramData, totalList, sumList, param2.getItemName(),param2.getType());
    }
    /**
     * “应付合计”的“结算额”=所有结算方正值之和
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String totalPay(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> totalList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> sumList = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.settlePartySumColumn(paramData, totalList, sumList, param2.getItemName(),param2.getType());
    }

    /**
     * “应收合计”的“结算额”=所有结算方负值之和(销暂估)
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String totalChargeTSE(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> totalList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> sumList = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        return super.settlePartySumColumn(paramData, totalList, sumList, param2.getItemName(),param2.getType());
    }
    /**
     * “应付合计”的“结算额”=所有结算方正值之和(销暂估)
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String totalPayTSE(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> totalList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> sumList = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        return super.settlePartySumColumn(paramData, totalList, sumList, param2.getItemName(),param2.getType());
    }

    /**
     * 合计=“应收合计”+“应付合计”
     */
    public String  totalChargeAddTotalPay(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        ExcelParam.Param param3 = excelParam.getParam3();
        Integer[] columns3 = super.getColumns(param3);
        List<Map<String, Object>> dataList1 = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        List<Map<String, Object>> dataList2 = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        List<Map<String, Object>> dataList3 = super.getColumnDataList2(paramData.getPathName(), param3.getStartRow(), param3.getEndRow(), columns3,param3.getStartColumn());
        return super.towRowAdd(paramData,dataList1,dataList2,dataList3,"合计不等于“应收合计”+“应付合计”");
    }


    /**
     * 匹配报表头
     *
     * @param paramData
     * @return
     */
    public String matchHeader(ParamData paramData, ExcelParam excelParam) throws Exception {
        /**
         * 读取归属省
         */
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), 2, null, columns1);
        Map<String, Object> headerMap = super.getHeaderMap(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.matchHeader(paramData,dataList, headerMap,param1.getColumns()  ,param1.getItemName(),"matchHeader"));
        return  sb.toString();
    }

    public String matchDoubleHeader(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), 2, null, columns1);
        Map<String, Object> headerMap = super.getHeaderMap(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.matchHeader(paramData,dataList, headerMap,param1.getColumns()  ,param1.getItemName(),"matchDoubleHeader"));

        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> secondHeader = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        sb.append(super.matchDoubleHeader(paramData,secondHeader, param2.getItemName()));
        return sb.toString();
    }

    public String matchHeaderPlus(ParamData paramData, ExcelParam excelParam) throws Exception {
        /**
         * 读取归属省
         */
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), 2, null, columns1);
        Map<String, Object> headerMap = super.getHeaderMap(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        Integer type = param1.getType();
        String headerStr = "matchHeader";
        if (type != null && type == 1) {
            headerStr = "1";
        }
        sb.append(super.matchDynamiSheetHeader(paramData,dataList, headerMap,param1.getColumns()  ,param1.getItemName(),headerStr));
        return  sb.toString();
    }


    public String moneyAddAutomatic(ParamData paramData, ExcelParam excelParam){
        return super.moneyAddAutomatic(paramData,excelParam);
    }

    public String moneyAddAutomaticDif(ParamData paramData, ExcelParam excelParam){
        return super.moneyAddAutomaticDif(paramData,excelParam);
    }


    /**
     * 特殊分表的sheet页名称稽核，名称需与表头省份一致，同时校验名称需为31省名称
     *
     * @param paramData
     * @return
     */
    public String matchSheetHeader(ParamData paramData, ExcelParam excelParam) throws Exception {
        /**
         * 读取归属省
         */
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), 2, null, columns1);
        Map<String, Object> headerMap = super.getHeaderMap(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.matchSheetHeader(paramData,dataList, headerMap,param1.getColumns()));
        return  sb.toString();
    }

    /**
     * 省公司的省份名称”与SHELL页省份名称之间的稽核
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String matchSheet(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        //广东&1&2 -> 省公司：广东
        List<Map<String, Object>> dataList =  super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);

        sb.append(super.matchSheet(paramData, dataList));
        return  sb.toString();
    }


    public String outTaxSkuAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        String pathName = paramData.getPathName();
        Integer sheetNums =  getsheetNum(new File(pathName));
        //SQL Results&1&1 -> 230   1行 1列  230
        StringBuffer stringBuffer = new StringBuffer();
        for(int i=0;i<sheetNums;i++) {//轮询sheet
            //读取税额
            ExcelParam.Param param1 = excelParam.getParam1();
            Integer[] columns1 = super.getColumns(param1);
            List<Map<String, Object>> taxAmounts = super.getColumnDataList(paramData.getPathName(), i, param1.getStartRow(), param1.getEndRow(), columns1);
            //读取含税金额, 不含税金额
            ExcelParam.Param param2 = excelParam.getParam2();
            Integer[] columns2 = super.getColumns(param2);
            List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(), i, param2.getStartRow(), param2.getEndRow(), columns2);
            String s = super.noIncludeTaxSkuAmount(paramData, taxAmounts, amounts, param1.getItemName());
            stringBuffer.append(s);
        }
        return stringBuffer.toString();
    }

    public String totalCalculatedAmount (ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取合计行
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> dataListA =super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> dataListB =super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        ExcelParam.Param param3 = excelParam.getParam3();
        Integer[] columns3 = super.getColumns(param3);
        List<Map<String, Object>> dataListC =super.getColumnDataList(paramData.getPathName(), param3.getStartRow(), param3.getEndRow(), columns3);
        return super.totalCalculatedAmount(paramData,dataListC,dataListB,dataListA,"总含税不等于“总不含税”+“总税额”");
    }

    public Integer getsheetNum(File file){
        int numberOfSheets = 1;
        try{
            FileInputStream fis = new FileInputStream(file);
            Workbook workbook = WorkbookFactory.create(fis);
            //sheet的数量
            numberOfSheets = workbook.getNumberOfSheets();
        }catch (Exception e){
            log.info("sheet数量异常");
        }
        return numberOfSheets;
    }

    public String interSectionNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        sb.append(super.columnIsEmpty(paramData, dataList,  param1.getItemName()));
        return sb.toString();
    }

    public String comparePriceWord(ParamData paramData, ExcelParam excelParam) {
        return super.comparePriceWord(paramData, excelParam);
    }

    public String totalComparePrice(ParamData paramData, ExcelParam excelParam) {
        return  super.totalComparePrice(paramData, excelParam);
    }

    public String totalComparePriceAndTaxRate(ParamData paramData, ExcelParam excelParam) {
        return  super.totalComparePriceAndTaxRate(paramData, excelParam);
    }
    public String totalCompareToMorePrice(ParamData paramData, ExcelParam excelParam)  {
        return  super.totalCompareToMorePrice(paramData, excelParam);
    }
    public String compareTwoExcelProvinceTotalPrice(ParamData paramData, ExcelParam excelParam)  {
        return  super.compareTwoExcelProvinceTotalPrice(paramData, excelParam);
    }
}
