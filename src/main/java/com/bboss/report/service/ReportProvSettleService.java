package com.bboss.report.service;

import cn.hutool.core.util.StrUtil;
import com.bboss.report.model.AuditResponse;
import com.bboss.report.model.ExcelParam;
import com.bboss.report.model.ParamData;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *  省专结算
 */
@Slf4j
@Service
public class ReportProvSettleService extends BaseService implements ReportAuditService {
    @Override
    public List<String> getReportKey() {
        List<String> keyList = new ArrayList<>();
        keyList.add("10110");
        keyList.add("10113");
        keyList.add("10124");
        keyList.add("10107");
        keyList.add("10106");
        keyList.add("10109");
        keyList.add("10129");
        keyList.add("10111");
        keyList.add("10112");
        keyList.add("24080501");
        keyList.add("10128");
        keyList.add("10145");
        keyList.add("10147");
        keyList.add("10116");
        keyList.add("10123");
        keyList.add("10136");
        keyList.add("10120");
        keyList.add("10108");
        keyList.add("10149");
        keyList.add("10101");
        keyList.add("10102");
        keyList.add("10131");
        keyList.add("10156");
        keyList.add("10187");

        keyList.add("20020");
        keyList.add("20021");
        keyList.add("20022");
        keyList.add("20023");
        keyList.add("20024");
        keyList.add("20025");
        keyList.add("20026");
        keyList.add("20027");
        keyList.add("20028");
        keyList.add("20029");
        return keyList;
    }

    /**
     * 1.读取excel 的所有sheet ，按sheet遍历
     * 2. 遍历报表对应的稽核项，并调用稽核项的方法
     *
     * @param paramData
     * @return
     */
    @Override
    public List<AuditResponse> execute(ParamData paramData) {
        return super.execute(paramData, "com.bboss.report.service.ReportProvSettleService");
    }

    public String provNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        /**
         * 读取归属省
         */
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.columnIsEmpty(paramData, dataList,  param1.getItemName()));
        sb.append(super.columnIsNumbers(paramData, dataList,  param1.getItemName()));
        return sb.toString();
    }

    /**
     * 报表金额是否保留两位小数
     *
     * @param paramData
     * @return
     */
    public String keepTwoDecimals(ParamData paramData, ExcelParam excelParam) throws Exception {
        //需要6, 7, 8, 9, 10, 11, 12金额字段
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns,param1.getStartColumn());
        List<Map<String, Object>> dataFxList = super.dataFxList;
        return super.keepTwoDecimals(paramData, dataList ,dataFxList);
    }

    /**
     * 报表金额是否保留两位小数
     *
     * @param paramData
     * @return
     */
    public String keepTwoDecimalsNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns,param1.getStartColumn());
        ExcelParam.Param param2 = excelParam.getParam2();
        List<Map<String, Object>> dataList2 = new ArrayList<>();
        if (param2 != null) {
            Integer[] columns2 = super.getColumns(param2);
            dataList2 = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        }
        return super.keepTwoDecimalsNotNull(paramData, dataList2,dataList);
    }

    /**
     * 表尾签字处是否有制表人、IT中心部门审核、IT中心领导审核(已更改)
     * 制表人、IT中心部门审核、制表单位
     *
     * @param paramData
     * @return
     */
    public String endSign(ParamData paramData, ExcelParam excelParam) throws Exception {
        // 获取最后一行表尾签字处
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns,param1.getStartColumn());
        return super.endSignOneLine(paramData, dataList);
//        return super.endSignPlus(paramData, dataList);
    }

    /**
     * 制表人、IT中心部门审核、制表单位、IT中心领导审核
     *
     * @param paramData
     * @return
     */
    public String endSignPlus(ParamData paramData, ExcelParam excelParam) throws Exception {
        // 获取最后一行表尾签字处
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns, param1.getStartColumn());
        return super.endSignOneLinePlus(paramData, dataList);
//        return super.endSignPlus(paramData, dataList);
    }

    /**
     * “***”为空(江苏&浙江)
     *
     * @param paramData
     * @return
     */
    public String isEmptyJSZJ(ParamData paramData, ExcelParam excelParam) throws Exception {
        // 获取空的那一行
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns, param1.getStartColumn());
        return super.isEmptyJSZJ(paramData, dataList);
    }

    /**
     * 报表结算月账期稽核
     *
     * @param paramData
     * @return
     */
    public String settleMonth(ParamData paramData, ExcelParam excelParam) throws Exception {
        // 读取结算月
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns,param1.getStartColumn());
        return super.settleMonth(paramData, dataList);
    }

    /**
     * 应结入：税额 = 含税金额- 不含税金额，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String inTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取税额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取含税金额, 不含税金额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.taxAmount(paramData, taxAmounts, amounts, 2, param1.getItemName());
    }

    /**
     * 应结入：不含税金额= 含税金额/(1+税率)，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String inNoIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取不含税金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> notTaxAmounts = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取含税金额
        if(StrUtil.isNotBlank(paramData.getTaxRate())){
            ExcelParam.Param param2 = excelParam.getParam2();
            Integer[] columns2 = super.getColumns(param2);
            List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
            return super.noIncludeTaxAmount(paramData, notTaxAmounts, amounts, param1.getItemName());
        }else{
            //读取 含税金额和税率
            ExcelParam.Param param2 = excelParam.getParam2();
            Integer[] columns2 = super.getColumns(param2);
            List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
            return super.noIncludeTaxAmountTaxRate(paramData, notTaxAmounts, amounts,  param1.getItemName(), param2.getType());
        }

    }

    /**
     * 应结入：含税金额=税额 +不含税金额，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String inIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取列含税金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取不含税金额和税额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.includeTaxAmount(paramData, taxAmounts, amounts,  param1.getItemName());
    }

    /**
     * 应结出:税额 = 含税金额- 不含税金额，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String outTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取税额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList2(paramData.getPathName(),  param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取含税金额, 不含税金额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.taxAmount(paramData, taxAmounts, amounts, 2,  param1.getItemName());
    }


    /**
     * 应结出“含税金额”=应结入“含税金额”-结算额“含税金额”
     *
     * @param paramData
     * @return
     */
    public String misMatchOutIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取应结出“含税金额”
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList2(paramData.getPathName(),  param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取应结入“含税金额” , 结算额“含税金额”
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.taxAmount(paramData, taxAmounts, amounts, 1,  param1.getItemName());
    }


    /**
     * 应结出：不含税金额= 含税金额/(1+税率)，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String outNoIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取不含税金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> notTaxAmounts = super.getColumnDataList2(paramData.getPathName(),  param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取含税金额
        if(StrUtil.isNotBlank(paramData.getTaxRate())){
            ExcelParam.Param param2 = excelParam.getParam2();
            Integer[] columns2 = super.getColumns(param2);
            List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
            return super.noIncludeTaxAmount(paramData, notTaxAmounts, amounts, param1.getItemName());
        }else{
            ExcelParam.Param param2 = excelParam.getParam2();
            Integer[] columns2 = super.getColumns(param2);
            List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
            return super.noIncludeTaxAmountTaxRate(paramData, notTaxAmounts, amounts,  param1.getItemName(), param2.getType());
        }
    }

    /**
     * 应结出：含税金额=税额 +不含税金额，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String outIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取列含税金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取不含税金额和税额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.includeTaxAmount(paramData, taxAmounts, amounts,  param1.getItemName());
    }


    /**
     * “产品”不为空，不为全数字
     *
     * @param paramData
     * @return
     */
    public String productNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        /**
         * 读取产品列
         */
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        sb.append(super.columnIsEmpty(paramData, dataList, param1.getItemName()));
        sb.append(super.columnIsNumbers(paramData, dataList, param1.getItemName()));
        return sb.toString();
    }


    /**
     * “集团客户”不为空
     *
     * @param paramData
     * @return
     */
    public String groupCustomerNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        return super.columnIsEmpty(paramData, dataList, "集团客户");
    }

    /**
     * “客户编码” 不为空
     *
     * @param paramData
     * @return
     */
    public String customerCodeNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        return super.columnIsEmpty(paramData, dataList, "客户编码");
    }

    /**
     * 厂家代码 不为空
     *
     * @param paramData
     * @return
     */
    public String manufacturerCodeNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        return super.columnIsEmpty(paramData, dataList, "厂家代码");
    }



    /**
     * 各产品名称 不为空，不为全数字
     *
     * @param paramData
     * @return
     */
    public String productNameNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        /**
         * 读取产品列
         */
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.columnIsEmpty(paramData, dataList, "各产品名称"));
        sb.append(super.columnIsNumbers(paramData, dataList, "各产品名称"));
        return sb.toString();
    }




    /**
     * 合计=对应列求和
     *
     * @param paramData
     * @return
     */
    public String sumColumn(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        StringBuffer sb = new StringBuffer();
        for (Integer column : columns1) {
            /**
             * 读取合计行
             */
            List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), new Integer[]{column},param1.getStartColumn());
            /**
             * 读取行
             */
            List<Map<String, Object>> dataList2 = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), new Integer[]{column},param2.getStartColumn());
            sb.append(super.sumColumn(paramData, dataList, dataList2,"合计不等于对应列求和"));
        }
        return sb.toString();
    }


    /**
     * 小计=对应列求和(江苏&浙江)
     *
     * @param paramData
     * @return
     */
    public String subSumColumnJSZJ(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        StringBuffer sb = new StringBuffer();
        for (Integer column : columns1) {
            /**
             * 读取合计行
             */
            List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), new Integer[]{column},param1.getStartColumn());
            /**
             * 读取行
             */
            List<Map<String, Object>> dataList2 = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), new Integer[]{column},param2.getStartColumn());
            sb.append(super.sumColumnJSZJ(paramData, dataList, dataList2,"合计不等于对应列求和"));
        }
        return sb.toString();
    }


    /**
     * 合计=小计(江苏&浙江)
     *
     * @param paramData
     * @return
     */
    public String matchSubSumJSZJ(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        StringBuffer sb = new StringBuffer();
        for (Integer column : columns1) {
            /**
             * 读取合计行
             */
            List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), new Integer[]{column},param1.getStartColumn());
            /**
             * 读取行
             */
            List<Map<String, Object>> dataList2 = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), new Integer[]{column},param2.getStartColumn());
            sb.append(super.sumColumnJSZJ(paramData, dataList, dataList2,"合计不等于对应列求和"));
        }
        return sb.toString();
    }


    /**
     * 应结入“***”“不含税金额”=应结出“不含税金额”的总和
     *
     * @param paramData
     * @return
     */
    public String misMatchInOutcludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        StringBuffer sb = new StringBuffer();
        /**
         * 读取合计行
         */
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        /**
         * 读取行
         */
        List<Map<String, Object>> dataList2 = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        sb.append(super.sumSingleColumn(paramData, dataList, dataList2,"合计不等于对应列求和"));

        return sb.toString();
    }

    /**
     * 应结入“***”“含税金额”=应结出“含税金额”的总和
     *
     * @param paramData
     * @return
     */
    public String misMatchInIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        StringBuffer sb = new StringBuffer();
        /**
         * 读取合计行
         */
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        /**
         * 读取行
         */
        List<Map<String, Object>> dataList2 = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        sb.append(super.sumSingleColumn(paramData, dataList, dataList2,"合计不等于对应列求和"));

        return sb.toString();
    }


    /**
     * 应结入“***”“税额”=应结出“税额”的总和
     *
     * @param paramData
     * @return
     */
    public String misMatchInOutTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        StringBuffer sb = new StringBuffer();
        /**
         * 读取合计行
         */
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        /**
         * 读取行
         */
        List<Map<String, Object>> dataList2 = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        sb.append(super.sumSingleColumn(paramData, dataList, dataList2,"合计不等于对应列求和"));

        return sb.toString();
    }

    /**
     * 结算额“***”=结算额中31省的金额总和的相反数
     *
     * @param paramData
     * @return
     */
    public String minusTaxAmountMarket(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        StringBuffer sb = new StringBuffer();
        for (Integer column : columns1) {
            /**
             * 读取合计行
             */
            List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), new Integer[]{column});
            Map<String, Object> map = dataList.get(0);
            //合计设置 相反数
            for (String key : map.keySet()) {
                String Value = String.valueOf(map.get(key));
                if(Value.startsWith("-")){
                    Value=Value.substring(1,Value.length());
                }else {
                    Value="-"+Value;
                }
                map.put(key,Value);
            }
            /**
             * 读取行
             */
            List<Map<String, Object>> dataList2 = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), new Integer[]{column});
            sb.append(super.sumSingleColumn(paramData, dataList, dataList2,"合计不等于对应列求和"));
        }
        return sb.toString();
    }



    /**
     * “结算额”的“含税金额”=“应结入”的“含税金额”-“应结出”的“含税金额”
     *
     * @param paramData
     * @return
     */
    public String settleIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取“结算额”的“含税金额”
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList2(paramData.getPathName(),  param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取应结入”的“含税金额”,“应结出”的“含税金额”

        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.taxAmount(paramData, taxAmounts, amounts, param1.getType(), "“结算额”的“含税金额”=“应结入”的“含税金额”-“应结出”的“含税金额”");
    }



    /**
     * 税额 = 含税金额- 不含税金额，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String taxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取税额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        Long startTime1= System.currentTimeMillis();
        List<Map<String, Object>> taxAmounts = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        Long endTime1= System.currentTimeMillis();
        log.info("读取税额用时：{} MS",endTime1-startTime1);
        //读取含税金额, 不含税金额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        log.info("读取含税金额, 不含税金额用时：{} MS",endTime1-startTime1);
        return super.taxAmount(paramData, taxAmounts, amounts, param2.getType(),"税额 = 含税金额- 不含税金额");
    }

    /**
     * 不含税金额= 含税金额/(1+税率)，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String noIncludeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取不含税金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> notTaxAmounts = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取含税金额
        if(StrUtil.isNotBlank(paramData.getTaxRate())){
            ExcelParam.Param param2 = excelParam.getParam2();
            Integer[] columns2 = super.getColumns(param2);
            List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
            return super.noIncludeTaxAmount(paramData, notTaxAmounts, amounts,"不含税金额= 含税金额/(1+税率)");
        }else{
            //读取 含税金额和税率
            ExcelParam.Param param2 = excelParam.getParam2();
            Integer[] columns2 = super.getColumns(param2);
            List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
            return super.noIncludeTaxAmountTaxRate(paramData, notTaxAmounts, amounts,"不含税金额= 含税金额/(1+税率)", param2.getType());
        }

    }

    /**
     * 含税金额=税额 +不含税金额，保留两位小数，四舍五入
     *
     * @param paramData
     * @return
     */
    public String includeTaxAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取列含税金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取不含税金额和税额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.includeTaxAmount(paramData, taxAmounts, amounts,"含税金额=税额 +不含税金额");
    }

    /**
     * “业务总收入”的含税金额=“政企结算金额”的含税金额+“省公司结算金额”的含税金额
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String includeTaxAmountAdd(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取列含税金额
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> taxAmounts = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取不含税金额和税额
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> amounts = super.getColumnDataList2(paramData.getPathName(),  param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.includeTaxAmount(paramData, taxAmounts, amounts, "“业务总收入”的含税金额=“政企结算金额”的含税金额+“省公司结算金额”的含税金额");
    }

    /**
     * “应收合计”的“结算额”=所有结算方负值之和
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String totalCharge(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> totalList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> sumList = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.settlePartySumColumn(paramData, totalList, sumList, param2.getItemName(),param2.getType());
    }
    /**
     * “应付合计”的“结算额”=所有结算方正值之和
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String totalPay(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> totalList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> sumList = super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.settlePartySumColumn(paramData, totalList, sumList, param2.getItemName(),param2.getType());
    }

    /**
     * “应收合计”的“结算额”=所有结算方负值之和(销暂估)
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String totalChargeTSE(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> totalList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> sumList = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        return super.settlePartySumColumn(paramData, totalList, sumList, param2.getItemName(),param2.getType());
    }
    /**
     * “应付合计”的“结算额”=所有结算方正值之和(销暂估)
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String totalPayTSE(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> totalList = super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> sumList = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        return super.settlePartySumColumn(paramData, totalList, sumList, param2.getItemName(),param2.getType());
    }

    /**
     * “应付合计”的“结算额”=所有结算方正值之和
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String  custAndSettleAmount (ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        return super.custAndSettleAmount(paramData, dataList,param1);
    }

    /**
     * “结算额”的“含税金额”=“市场化结算”下的各项“不含税金额”和“税额”的和
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String settleIncludeTaxAmountMarket(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取“结算额”的“含税金额”
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> dataList1 =super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取“市场化结算”下的各项“不含税金额”和“税额”
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> dataList2 =super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        return super.multipleAddition(paramData, dataList1, dataList2, "“结算额”的“含税金额”不等于“市场化结算”下的各项“不含税金额”和“税额”的和");
    }


    /**
     * 省政企公司(成研院等)结算额等于对应31省中的（不含税+税额）金额总和的相反数
     * 结算额“***”=市场化结算31省中‘‘***’’的金额总和的相反数
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String provFinalAmnountEqualTo31ProSumAmountsOppositeNum(ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取省政企公司对应的公司名称
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> dataList1 =super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        //读取省政企公司对应的“结算额”的“含税金额”
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> dataList2 =super.getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        //读取省政企公司对应31省的“不含税额”和“税额”
        ExcelParam.Param param3 = excelParam.getParam3();
        Integer[] columns3 = super.getColumns(param3);
        List<Map<String, Object>> dataList3 =super.getColumnDataList2(paramData.getPathName(), param3.getStartRow(), param3.getEndRow(), columns3,param3.getStartColumn());

        List<Map<String, Object>> dataList4 =super.getColumnDataList2(paramData.getPathName(), 5, null, null,10);


        return super.auditTheSettleAmount(paramData, dataList1, dataList2,dataList3,dataList4, "结算额“***”不等于市场化结算31省中‘‘***’’的金额总和的相反数");
    }


    /**
     * 合计不为0
     * @param paramData
     * @param excelParam
     * @throws Exception
     */
    public String totalNotZero (ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取合计行
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> dataList =super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        return super.notZero(paramData, dataList, "合计为0");
    }

    /**
     * “XXX”不为空
     *
     * @param paramData
     * @return
     */
    public String notNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        sb.append(super.columnIsEmpty(paramData, dataList,  param1.getItemName()));
        return sb.toString();
    }

    /**
     * “XXX”不为空 ，不为全数字
     *
     * @param paramData
     * @return
     */
    public String notNullAndNumber(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        sb.append(super.columnIsEmpty(paramData, dataList,  param1.getItemName()));
        sb.append(super.columnIsNumbers(paramData, dataList,  param1.getItemName()));
        return sb.toString();
    }
    /**
     *
     * @param paramData
     * @param excelParam
     * @return
     */
    public String worldFileName(ParamData paramData, ExcelParam excelParam) throws Exception{
        return super.worldFileName(paramData.getFileName(),paramData.getPathName());
    }


    /**
     * 匹配报表头
     *
     * @param paramData
     * @return
     */
    public String matchHeader(ParamData paramData, ExcelParam excelParam) throws Exception {
        /**
         * 读取归属省
         */
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), 2, null, columns1);
        Map<String, Object> headerMap = super.getHeaderMap(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.matchHeader(paramData,dataList, headerMap,param1.getColumns()  ,param1.getItemName(),"matchHeader"));
        return  sb.toString();
    }

    public String matchHeaderPlus(ParamData paramData, ExcelParam excelParam) throws Exception {
        /**
         * 读取归属省
         */
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), 2, null, columns1);
        Map<String, Object> headerMap = super.getHeaderMap(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        Integer type = param1.getType();
        String headerStr = "matchHeader";
        if (type != null && type == 1) {
            headerStr = "1";
        }
        sb.append(super.matchDynamiSheetHeader(paramData,dataList, headerMap,param1.getColumns()  ,param1.getItemName(),headerStr));
        return  sb.toString();
    }

    public String matchDoubleHeader(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), 2, null, columns1);
        Map<String, Object> headerMap = super.getHeaderMap(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.matchHeader(paramData,dataList, headerMap,param1.getColumns()  ,param1.getItemName(),"matchDoubleHeader"));

        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> secondHeader = super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        sb.append(super.matchDoubleHeader(paramData,secondHeader, param2.getItemName()));
        return sb.toString();
    }

    public String moneyAddAutomatic(ParamData paramData, ExcelParam excelParam){
        return super.moneyAddAutomatic(paramData,excelParam);
    }

    public String moneyAddAutomaticDif(ParamData paramData, ExcelParam excelParam){
        return super.moneyAddAutomaticDif(paramData,excelParam);
    }

    /**
     * 特殊分表的sheet页名称稽核，名称需与表头省份一致，同时校验名称需为31省名称
     *
     * @param paramData
     * @return
     */
    public String matchSheetHeader(ParamData paramData, ExcelParam excelParam) throws Exception {
        /**
         * 读取归属省
         */
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList(paramData.getPathName(), 2, null, columns1);
        Map<String, Object> headerMap = super.getHeaderMap(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        sb.append(super.matchSheetHeader(paramData,dataList, headerMap,param1.getColumns()));
        return  sb.toString();
    }

    /**
     * 省公司的省份名称”与SHELL页省份名称之间的稽核
     * @param paramData
     * @param excelParam
     * @return
     * @throws Exception
     */
    public String matchSheet(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        //广东&1&2 -> 省公司：广东
        List<Map<String, Object>> dataList =  super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);

        sb.append(super.matchSheet(paramData, dataList));
        return  sb.toString();
    }

    public String outTaxSkuAmount(ParamData paramData, ExcelParam excelParam) throws Exception {
        String pathName = paramData.getPathName();
        Integer sheetNums =  getsheetNum(new File(pathName));
        //SQL Results&1&1 -> 230   1行 1列  230
        StringBuffer stringBuffer = new StringBuffer();
        for(int i=0;i<sheetNums;i++) {//轮询sheet
            //读取税额
            ExcelParam.Param param1 = excelParam.getParam1();
            Integer[] columns1 = super.getColumns(param1);
            List<Map<String, Object>> taxAmounts = super.getColumnDataList(paramData.getPathName(), i, param1.getStartRow(), param1.getEndRow(), columns1);
            //读取含税金额, 不含税金额
            ExcelParam.Param param2 = excelParam.getParam2();
            Integer[] columns2 = super.getColumns(param2);
            List<Map<String, Object>> amounts = super.getColumnDataList(paramData.getPathName(), i, param2.getStartRow(), param2.getEndRow(), columns2);
            String s = super.noIncludeTaxSkuAmount(paramData, taxAmounts, amounts, param1.getItemName());
            stringBuffer.append(s);
        }
        return stringBuffer.toString();
    }

    public String totalCalculatedAmount (ParamData paramData, ExcelParam excelParam) throws Exception {
        //读取合计行
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        List<Map<String, Object>> dataListA =super.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Integer[] columns2 = super.getColumns(param2);
        List<Map<String, Object>> dataListB =super.getColumnDataList(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), columns2);
        ExcelParam.Param param3 = excelParam.getParam3();
        Integer[] columns3 = super.getColumns(param3);
        List<Map<String, Object>> dataListC =super.getColumnDataList(paramData.getPathName(), param3.getStartRow(), param3.getEndRow(), columns3);
        return super.totalCalculatedAmount(paramData,dataListC,dataListB,dataListA,"总含税不等于“总不含税”+“总税额”");
    }


    public Integer getsheetNum(File file){
        int numberOfSheets = 1;
        try{
            FileInputStream fis = new FileInputStream(file);
            Workbook workbook = WorkbookFactory.create(fis);
            //sheet的数量
            numberOfSheets = workbook.getNumberOfSheets();
        }catch (Exception e){
            log.info("sheet数量异常");
        }
        return numberOfSheets;
    }

    public String interSectionNotNull(ParamData paramData, ExcelParam excelParam) throws Exception {
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = super.getColumns(param1);
        StringBuffer sb=new StringBuffer();
        List<Map<String, Object>> dataList = super.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        sb.append(super.columnIsEmpty(paramData, dataList,  param1.getItemName()));
        return sb.toString();
    }

    public String comparePriceWord(ParamData paramData, ExcelParam excelParam) {
        return super.comparePriceWord(paramData, excelParam);
    }

    public String totalComparePrice(ParamData paramData, ExcelParam excelParam) {
        return  super.totalComparePrice(paramData, excelParam);
    }

    public String totalComparePriceAndTaxRate(ParamData paramData, ExcelParam excelParam) {
        return  super.totalComparePriceAndTaxRate(paramData, excelParam);
    }
    public String totalCompareToMorePrice(ParamData paramData, ExcelParam excelParam)  {
        return  super.totalCompareToMorePrice(paramData, excelParam);
    }

    public String compareTwoExcelColumnPrice(ParamData paramData, ExcelParam excelParam)  {
        return  super.compareTwoExcelColumnPrice(paramData, excelParam);
    }

    public String compareTwoExcelProvinceTotalPrice(ParamData paramData, ExcelParam excelParam)  {
        return  super.compareTwoExcelProvinceTotalPrice(paramData, excelParam);
    }

}
