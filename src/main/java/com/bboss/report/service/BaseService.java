package com.bboss.report.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bboss.report.component.DictMap;
import com.bboss.report.listener.ExcelListenerWithColumn;
import com.bboss.report.listener.ExcelListenerWithStartColumn;
import com.bboss.report.model.AuditEntryParam;
import com.bboss.report.model.AuditResponse;
import com.bboss.report.model.ExcelParam;
import com.bboss.report.model.ParamData;
import com.bboss.report.service.special.CheckPdfInfoService;
import com.bboss.report.util.ApplicationContextUtil;
import com.bboss.report.util.CommonUtil;
import com.bboss.report.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
public abstract class BaseService {

    @Autowired
    private CheckPdfInfoService checkPdfInfoService;
    /**
     * @param paramData
     * @param className
     * @return
     */
    public List<AuditResponse> execute(ParamData paramData, String className) {

        List<AuditResponse> responseList = new ArrayList<>();
        if (ObjectUtil.isNotNull(paramData.getAuditEntry())) {//稽核项
            String[] auditEntryArr = paramData.getAuditEntry().split(",");
            for (String auditEntryKey : auditEntryArr) {//遍历每个稽核项
                auditEntryKey = auditEntryKey.trim();
                if (DictMap.auditEntryMethodMap.get(auditEntryKey) != null) {//稽核项的alias
                    List<AuditResponse> retList = process(auditEntryKey, paramData, className);
                    responseList.addAll(retList);
                }
            }
        } else {
            AuditResponse response = new AuditResponse();
            BeanUtil.copyProperties(paramData, response);
            response.setRspCode(response.EXCEPTION);
            response.setRspDesc("配置文件里,稽核项为空");
            responseList.add(response);
        }
        return responseList;
    }

    /**
     * @param auditEntryKey
     * @param paramData
     * @param className
     * @return
     */
    public List<AuditResponse> process(String auditEntryKey, ParamData paramData, String className) {
        String auditEntry = null;
        AuditResponse response = new AuditResponse();
        BeanUtil.copyProperties(paramData, response);
        List<AuditResponse> responseList = new ArrayList<>();
        try {
            //根据配置文件里的稽核项key获取稽核项方法
            String methodName = DictMap.auditEntryMethodMap.get(auditEntryKey);
            auditEntry = DictMap.auditEntryMap.get(auditEntryKey);//稽核项说明
            log.info("稽核名称：{}",auditEntry);
            //根据稽核项方法获取读excel的参数  根据英文名称找到配置
            ExcelParam excelParam = getExcelParam(paramData, methodName);
            log.info("表格读取参数：{}", JSON.toJSONString(excelParam));
            // spring bean 获取service类，并通过反射调用方法
            Object childClass = ApplicationContextUtil.createInstanceByClassName(className);
            //从类中获取到具体的方法   都是通过稽核项的英文找到的
            Method method = childClass.getClass().getMethod(methodName, ParamData.class, ExcelParam.class);
            if (method != null) {
                String ret = (String) method.invoke(childClass, paramData, excelParam);
                if (ret.length() == 0) {
                    response.setRspCode(AuditResponse.SUCCESS);
                    response.setRspDesc(AuditResponse.SUCCESS_DESC);
                    ret=AuditResponse.SUCCESS_DESC;
                } else {
                    response.setRspCode(AuditResponse.FAIL);
                    if (ret.length() > 32767) {
                        ret = ret.substring(0, 32760);
                    }
                    response.setRspDesc(ret);
                }
                response.setAuditEntryStr(auditEntry);
                responseList.add(response);
                log.info("稽核文件:{}, 稽核项：{},稽核完成!,稽核结果:[{}]", paramData.getFileName(), auditEntry,ret);
            } else {
                log.error("稽核文件：{}, 没有找到稽核项：{},对应的方法", paramData.getFileName(), auditEntryKey);
                response.setRspCode(response.EXCEPTION);
                response.setRspDesc("报表：" + paramData.getReportName() + ",文件：" + paramData.getFileName() + "没有找到稽核项：" + auditEntry + ",对应的方法");
                responseList.add(response);
            }

        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException | ClassNotFoundException |
                 InstantiationException | NoSuchFieldException e) {
            log.error("稽核文件：{},调用稽核方法：{}失败,", paramData.getFileName(), DictMap.auditEntryMethodMap.get(auditEntryKey), e);
            response.setRspCode(response.EXCEPTION);
            response.setRspDesc("报表：" + paramData.getReportName() + ",文件：" + paramData.getFileName() + "调用稽核项方法：" + auditEntry + "失败");
            responseList.add(response);
        }
        return responseList;
    }

    private ExcelParam getExcelParam(ParamData paramData, String methodName) throws NoSuchFieldException, IllegalAccessException {
        AuditEntryParam auditEntryParam = JSON.parseObject(paramData.getParameter(), AuditEntryParam.class);//所有稽核项的方法
        Field nameField = auditEntryParam.getClass().getDeclaredField(methodName);
        nameField.setAccessible(true); // 如果成员变量是私有的，需要设置为可访问
        ExcelParam excelParam = (ExcelParam) nameField.get(auditEntryParam);
        return excelParam;
    }

    /**
     * 报表金额是否保留两位小数
     *
     * @param paramData
     * @param dataList
     * @return
     * @throws Exception
     */
    public String keepTwoDecimals(ParamData paramData, List<Map<String, Object>> dataList,List<Map<String, Object>> dataFxList) throws Exception {
        StringBuffer sb = new StringBuffer();
        for (Map<String, Object> dataMap : dataFxList) {
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                String[] keyArr = entry.getKey().split("&");
                if(entry.getValue()!=null) {
                    String value = String.valueOf(entry.getValue());
                    String replaceValue = value.replace(",", "");
                    replaceValue = replaceValue.replaceAll("-?\\.0{2}(?!\\d)|(?<=\\.\\d{2})0+$", "");
                    replaceValue = replaceValue.replaceAll("-?\\d+\\.\\d{1}(?![0-9])", replaceValue+"0");
                    if (!replaceValue.contains(".")) {
                        replaceValue += ".00";
                    }
                    if (!replaceValue.matches("-?\\d+\\.\\d{2}")) {
                        sb.append("报表文件：").append(paramData.getFileName()).append(",");
                        sb.append("sheet名称").append(keyArr[0]).append(",");
                        sb.append("第").append(keyArr[1]).append("行");
                        sb.append("第").append(keyArr[2]).append("列");
                        sb.append("报表金额没有保留两位小数").append(System.lineSeparator());
                    }
                }
            }
        }
        return sb.toString();
    }


    /**
     * 报表金额是否保留两位小数,不能为空
     *
     * @param paramData
     * @param dataList
     * @return
     * @throws Exception
     */
    public String keepTwoDecimalsNotNull(ParamData paramData, List<Map<String, Object>> dataList,List<Map<String, Object>> dataFxList) throws Exception {
        StringBuffer sb = new StringBuffer();
        for (Map<String, Object> dataMap : dataFxList) {
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                String[] keyArr = entry.getKey().split("&");
                String value = String.valueOf(entry.getValue());
                if (entry.getValue()==null||com.alibaba.excel.util.StringUtils.isBlank(value)) {
                    sb.append("报表文件：").append(paramData.getFileName()).append(",");
                    sb.append("sheet名称").append(keyArr[0]).append(",");
                    sb.append("第").append(keyArr[1]).append("行");
                    sb.append("第").append(keyArr[2]).append("列");
                    sb.append("报表金额没有保留两位小数").append(System.lineSeparator());
                }else {
                    String replaceValue = value.replace(",", "");
                    replaceValue = replaceValue.replaceAll("-?\\.0{2}(?!\\d)|(?<=\\.\\d{2})0+$", "");
                    replaceValue = replaceValue.replaceAll("-?\\d+\\.\\d{1}(?![0-9])", replaceValue + "0");
                    if (!replaceValue.contains(".")) {
                        replaceValue += ".00";
                    }
                    if (!replaceValue.matches("-?\\d+\\.\\d{2}")) {
                        sb.append("报表文件：").append(paramData.getFileName()).append(",");
                        sb.append("sheet名称").append(keyArr[0]).append(",");
                        sb.append("第").append(keyArr[1]).append("行");
                        sb.append("第").append(keyArr[2]).append("列");
                        sb.append("报表金额没有保留两位小数").append(System.lineSeparator());
                    }
                }
            }
        }
        for (Map<String, Object> dataMap : dataList) {
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                String[] keyArr = entry.getKey().split("&");
                String value = String.valueOf(entry.getValue());
                if (entry.getValue()==null||com.alibaba.excel.util.StringUtils.isBlank(value)) {
                    sb.append("报表文件：").append(paramData.getFileName()).append(",");
                    sb.append("sheet名称").append(keyArr[0]).append(",");
                    sb.append("第").append(keyArr[1]).append("行");
                    sb.append("第").append(keyArr[2]).append("列");
                    sb.append("报表金额没有保留两位小数").append(System.lineSeparator()).toString();
                }else {
                    String replaceValue = value.replace(",", "");
                    replaceValue = replaceValue.replaceAll("-?\\.0{2}(?!\\d)|(?<=\\.\\d{2})0+$", "");
                    replaceValue = replaceValue.replaceAll("-?\\d+\\.\\d{1}(?![0-9])", replaceValue + "0");
                    if (!replaceValue.contains(".")) {
                        replaceValue += ".00";
                    }
                    if (!replaceValue.matches("-?\\d+\\.\\d{2}")) {
                        sb.append("报表文件：").append(paramData.getFileName()).append(",");
                        sb.append("sheet名称").append(keyArr[0]).append(",");
                        sb.append("第").append(keyArr[1]).append("行");
                        sb.append("第").append(keyArr[2]).append("列");
                        sb.append("报表金额没有保留两位小数").append(System.lineSeparator());
                    }
                }
            }
        }
        return sb.toString();
    }


    /**
     * 制表人、IT中心部门审核、制表单位
     *
     * @param paramData
     * @param dataList
     * @return
     * @throws Exception
     */
    public String endSign(ParamData paramData, List<Map<String, Object>> dataList) throws Exception {
        StringBuffer sb = new StringBuffer();
        for (Map<String, Object> dataMap : dataList) {
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                String[] keyArr = entry.getKey().split("&");
                String value = String.valueOf(entry.getValue());
                if (!(StringUtil.StringContains(value, "制表人") &&
                        StringUtil.StringContains(value, "IT中心部门审核") &&
                        StringUtil.StringContains(value, "制表单位"))) {
                    sb.append("报表文件：").append(paramData.getFileName()).append(",");
                    sb.append("sheet名称").append(keyArr[0]).append(",");
                    sb.append("第").append(keyArr[1]).append("行");
                    sb.append("第").append(keyArr[2]).append("列");
                    sb.append("表尾签字处没有有制表人、IT中心部门审核、制表单位").append(System.lineSeparator());
                }
            }

        }
        return sb.toString();
    }

    /**
     * 制表人、IT中心部门审核、制表单位、IT中心领导审核
     *
     * @param paramData
     * @param dataList
     * @return
     * @throws Exception
     */
    public String endSignPlus(ParamData paramData, List<Map<String, Object>> dataList) throws Exception {
        StringBuffer sb = new StringBuffer();
        for (Map<String, Object> dataMap : dataList) {
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                String[] keyArr = entry.getKey().split("&");
                String value = String.valueOf(entry.getValue());
                if (!(StringUtil.StringContains(value, "制表人") &&
                        StringUtil.StringContains(value, "IT中心部门审核") &&
                        StringUtil.StringContains(value, "IT中心领导审核") &&
                        StringUtil.StringContains(value, "制表单位"))) {
                    sb.append("报表文件：").append(paramData.getFileName()).append(",");
                    sb.append("sheet名称").append(keyArr[0]).append(",");
                    sb.append("第").append(keyArr[1]).append("行");
                    sb.append("第").append(keyArr[2]).append("列");
                    sb.append("表尾签字处没有有制表人、IT中心部门审核、制表单位、IT中心领导审核").append(System.lineSeparator());
                }
            }

        }
        return sb.toString();
    }


    protected String endSignOneLine(ParamData paramData, List<Map<String, Object>> dataList) {
        StringBuffer sb = new StringBuffer();

        if (CollectionUtils.isEmpty(dataList)) {
            sb.append("报表文件：").append(paramData.getFileName()).append(",");
            sb.append("表尾签字处没有有制表人、IT中心部门审核、制表单位").append(System.lineSeparator());
            return sb.toString();
        }

        for (Map<String, Object> dataMap : dataList) {
            String sheetName="";
            String line = "";
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                String[] keyArr = entry.getKey().split("&");
                String value = String.valueOf(entry.getValue());
                sheetName=keyArr[0];
                line=line+value;
            }
            if (!(StringUtil.StringContains(line, "制表人") &&
                    StringUtil.StringContains(line, "IT中心部门审核") &&
                    StringUtil.StringContains(line, "制表单位"))) {
                sb.append("报表文件：").append(paramData.getFileName()).append(",");
                sb.append("sheet名称").append(sheetName).append(",");
                sb.append("表尾签字处没有有制表人、IT中心部门审核、制表单位").append(System.lineSeparator());
            }
        }
        return sb.toString();
    }


    protected String endSignOneLinePlus(ParamData paramData, List<Map<String, Object>> dataList) {
        StringBuffer sb = new StringBuffer();
        for (Map<String, Object> dataMap : dataList) {
            String sheetName="";
            String line = "";
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                String[] keyArr = entry.getKey().split("&");
                String value = String.valueOf(entry.getValue());
                sheetName=keyArr[0];
                line=line+value;
            }
            if (!(StringUtil.StringContains(line, "制表人") &&
                    StringUtil.StringContains(line, "IT中心部门审核") &&
                    StringUtil.StringContains(line, "IT中心领导审核") &&
                    StringUtil.StringContains(line, "制表单位"))) {
                sb.append("报表文件：").append(paramData.getFileName()).append(",");
                sb.append("sheet名称").append(sheetName).append(",");
                sb.append("表尾签字处没有有制表人、IT中心部门审核、制表单位、IT中心领导审核").append(System.lineSeparator());
            }
        }
        return sb.toString();
    }

    /**
     * “***”为空(江苏&浙江)
     *
     * @param paramData
     * @return
     */
    public String isEmptyJSZJ(ParamData paramData, List<Map<String, Object>> dataList) {
        StringBuffer sb = new StringBuffer();
        for (Map<String, Object> dataMap : dataList) {
            String sheetName="";
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {

                String[] keyArr = entry.getKey().split("&");
                sheetName=keyArr[0];
                if("江苏".equals(sheetName)||"浙江".equals(sheetName)){

                    if (entry.getValue()!=null) {
                        sb.append("报表文件：").append(paramData.getFileName()).append(",");
                        sb.append("sheet名称").append(sheetName).append(",");
                        sb.append("31省公司客户 所在行不为空").append(System.lineSeparator());
                    }
                }
            }
        }
        return sb.toString();
    }

    /**
     * 报表结算月账期稽核
     *
     * @param paramData
     * @param dataList
     * @return
     * @throws Exception
     */
    public String settleMonth(ParamData paramData, List<Map<String, Object>> dataList) throws Exception {
        StringBuffer sb = new StringBuffer();
        for (Map<String, Object> dataMap : dataList) {
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                String[] keyArr = entry.getKey().split("&");
                String value = String.valueOf(entry.getValue());
                if (!value.contains(paramData.getSettleMonth())&&!("江苏".equals(keyArr[0])||"浙江".equals(keyArr[0]))) {
                    sb.append("报表文件：").append(paramData.getFileName()).append(",");
                    sb.append("sheet名称").append(keyArr[0]).append(",");
                    sb.append("第").append(keyArr[1]).append("行");
                    sb.append("第").append(keyArr[2]).append("列");
                    sb.append("报表结算月不是当前稽核账期").append(System.lineSeparator());
                }
            }

        }

        return sb.toString();
    }

    /**
     * 税额 = 含税金额- 不含税金额，保留两位小数，四舍五入
     *
     * @param paramData
     * @param dataList1
     * @param dataList2
     * @param type
     * @param itemName
     * @return
     * @throws Exception
     */
    public String taxAmount(ParamData paramData, List<Map<String, Object>> dataList1, List<Map<String, Object>> dataList2, Integer type, String itemName) throws Exception {
        List<Map<String, Object>> resultList = dataList2.stream()
                .map(map -> {
                    Object[] values = map.values().toArray();
                    Object[] keys = map.keySet().toArray();
                    String key = String.valueOf(keys[0]);
                    key = key.substring(0, key.lastIndexOf("&"));
                    Map<String, Object> result = new LinkedHashMap<>();
                    if (values[0] != null && values[1] != null) {
                        BigDecimal value1 = new BigDecimal(String.valueOf(values[0]).replace(",", ""));
                        BigDecimal value2 = new BigDecimal(String.valueOf(values[1]).replace(",", ""));
                        if (type != null && type == 1) {
                            BigDecimal value=value1.subtract(value2).setScale(2, RoundingMode.HALF_UP);
                            result.put(key, value.toString());
                        } else {
                            BigDecimal value=value2.subtract(value1).setScale(2, RoundingMode.HALF_UP);
                            result.put(key, value.toString());
                        }

                    } else {
                        result.put(key, null);
                    }
                    return result;
                }).collect(Collectors.toList());
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < dataList1.size(); i++) {
            Map<String, Object> mapFromList1 = dataList1.get(i);
            Map<String, Object> mapFromList2 = resultList.get(i);
            for (String key : mapFromList1.keySet()) {
                String key2 = key.substring(0, key.lastIndexOf("&"));
                String[] keyArr = key.split("&");
                String value1 = String.valueOf(mapFromList1.get(key)).replace(",", "");
                String value2 = String.valueOf(mapFromList2.get(key2)).replace(",", "");
                if (!value1.equals(value2)) {
                    sb.append("报表文件：").append(paramData.getFileName()).append(",");
                    sb.append("sheet名称").append(keyArr[0]).append(",");
                    sb.append("第").append(keyArr[1]).append("行");
                    sb.append("第").append(keyArr[2]).append("列");
                    sb.append(itemName).append(System.lineSeparator());
                }
            }
        }
        return sb.toString();
    }


    /**
     * 合计=合计-辽宁省份
     */
    public String settleTotalMinus(ParamData paramData, List<Map<String, Object>> taxAmounts, List<Map<String, Object>> amounts, List<Map<String, Object>> amounts2, String itemName, int type) throws Exception {

        List<Map<String, Object>> result = new ArrayList<>();

        HashMap<String, Object> stringMap = new HashMap<>();
        BigDecimal vOne = BigDecimal.valueOf(0.00);
        BigDecimal vTwo = BigDecimal.valueOf(0.00);
        String keys = "";
        for (int i = 0; i < amounts.size(); i++) {
            Map<String, Object> mOne = amounts.get(i);
            Map<String, Object> mTwo = amounts2.get(i);
            for (String key : mOne.keySet()) {
                keys = key;
                vOne = new BigDecimal(String.valueOf(mOne.get(key)).replace(",", ""));
            }
            for (String key : mTwo.keySet()) {
                vTwo = new BigDecimal(String.valueOf(mTwo.get(key)).replace(",", ""));
            }
            BigDecimal value=vOne.subtract(vTwo).setScale(2, RoundingMode.HALF_UP);
            stringMap.put(keys,value.toString());
            result.add(stringMap);
        }

        StringBuffer sb = new StringBuffer();
        String value1 = "";
        String value2 = "";
        String[] keyArr = {};
        for (int i = 0; i < taxAmounts.size(); i++) {
            Map<String, Object> mapFromList1 = taxAmounts.get(i);
            Map<String, Object> mapFromList2 = result.get(i);
            for (String key : mapFromList1.keySet()) {
                keyArr = key.split("&");
                value1 = String.valueOf(mapFromList1.get(key)).replace(",", "");
            }
            for (String key : mapFromList2.keySet()) {
                value2 = String.valueOf(mapFromList2.get(key)).replace(",", "");
            }
            if (!value1.equals(value2)) {
                sb.append("报表文件：").append(paramData.getFileName()).append(",");
                sb.append("sheet名称").append(keyArr[0]).append(",");
                sb.append("第").append(keyArr[1]).append("行");
                sb.append("第").append(keyArr[2]).append("列");
                sb.append(itemName).append(System.lineSeparator());
            }
        }
        return sb.toString();
    }

    /**
     * 不含税金额= 含税金额/(1+税率)，保留两位小数，四舍五入
     *
     * @param paramData
     * @param dataList1
     * @param dataList2
     * @return
     * @throws Exception
     */
    public String noIncludeTaxAmount(ParamData paramData, List<Map<String, Object>> dataList1, List<Map<String, Object>> dataList2, String itemName) throws Exception {
        BigDecimal taxRate =new BigDecimal(paramData.getTaxRate().replace("%", "")).divide(new BigDecimal(100),2,RoundingMode.HALF_UP).add(new BigDecimal(1)) ;
        //含税金额/（1+税率）
        List<Map<String, Object>> resultList = dataList2.stream()
                .map(map -> {
                    Map<String, Object> result = new LinkedHashMap<>();
                    for (Map.Entry<String, Object> entry : map.entrySet()) {
                        String key = entry.getKey(); // 获取键
                        key = key.substring(0, key.lastIndexOf("&"));
                        Object value = entry.getValue(); // 获取值
                        if (value != null) {
                            BigDecimal value1 = new BigDecimal(String.valueOf(value).replace(",", ""));
                            BigDecimal value2= value1.divide(taxRate,2,RoundingMode.HALF_UP);
                            result.put(key,  value2.toString() );
                        } else {
                            result.put(key, null);
                        }
                    }
                    return result;
                })
                .collect(Collectors.toList());
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < dataList1.size(); i++) {
            Map<String, Object> mapFromList1 = dataList1.get(i);
            Map<String, Object> mapFromList2 = resultList.get(i);
            for (String key : mapFromList1.keySet()) {
                String key2 = key.substring(0, key.lastIndexOf("&"));
                String[] keyArr = key.split("&");
                Double value1 =0.00;
                Double value2=0.00;
                if(mapFromList1.get(key)!=null){
                    value1 = Double.parseDouble(String.valueOf(mapFromList1.get(key)).replace(",", ""));
                }
                if (mapFromList2.get(key2)!=null){
                    value2 = Double.parseDouble(String.valueOf(mapFromList2.get(key2)).replace(",", ""));
                }
                if (Math.abs(value1 - value2) >1.00 &&!("江苏".equals(keyArr[0])||"浙江".equals(keyArr[0]))) {
                    sb.append("报表文件：").append(paramData.getFileName()).append(",");
                    sb.append("sheet名称").append(keyArr[0]).append(",");
                    sb.append("第").append(keyArr[1]).append("行");
                    sb.append("第").append(keyArr[2]).append("列");
                    sb.append(itemName).append(System.lineSeparator());
                }
            }
        }
//        for (Map<String, Object> mapFromList1 : dataList1) {
//            String key = mapFromList1.keySet().iterator().next();
//            String[] keyArr = key.split("&");
//            String key1 = key.substring(0, key.lastIndexOf("&"));
//            BigDecimal value1 = new BigDecimal(0.00);
//            if (mapFromList1.get(key) != null) {
//                value1 =new BigDecimal(String.valueOf(mapFromList1.get(key)).replace(",", ""))  ;
//            }
//            for (Map<String, Object> mapFromList2 : resultList) {
//                String key2 = mapFromList2.keySet().iterator().next();
//                BigDecimal value2 =new  BigDecimal(0.00);
//                if (mapFromList2.get(key2) != null) {
//                    value2 =new BigDecimal(String.valueOf(mapFromList2.get(key2)).replace(",", ""));
//                }
//                if (key1.equals(key2)) {
////                    if ( !value1.equals(value2)) {
//                    if (value1.compareTo(value2)!=0) {
//                        sb.append("报表文件：").append(paramData.getFileName()).append(",");
//                        sb.append("sheet名称").append(keyArr[0]).append(",");
//                        sb.append("第").append(keyArr[1]).append("行");
//                        sb.append("第").append(keyArr[2]).append("列");
//                        sb.append(itemName).append(System.lineSeparator());
//                    }
//                    break;
//                }
//            }
//        }
        return sb.toString();
    }

    /**
     *
     * @param paramData
     * @param dataList1 税额
     * @param dataList2 不含税额
     * @param itemName
     * @return
     * @throws Exception
     */
    public String noIncludeTaxSkuAmount(ParamData paramData, List<Map<String, Object>> dataList1, List<Map<String, Object>> dataList2, String itemName) throws Exception {
        // dataList1  report&4&3 -> 33,877,868.19  dataList2:report&4&4 -> 2,032,672.09
        //税额=（不含税金额+税额）-round（（不含税金额+税额）/1.06,2）
        BigDecimal taxRate;
        if (!StringUtils.isEmpty(paramData.getTaxRate())) {
            taxRate =new BigDecimal(paramData.getTaxRate().replace("%", "")).divide(new BigDecimal(100),2,RoundingMode.HALF_UP).add(new BigDecimal(1)) ;
        } else {
            taxRate = new BigDecimal("1.06");
        }

        //遍历不含税金额来计算
        List<Map<String, Object>> resultList = dataList2.stream()
                .map(map -> {
                    Map<String, Object> result = new LinkedHashMap<>();
                    for (Map.Entry<String, Object> entry : map.entrySet()) {
                        String key = entry.getKey(); // 获取键
                        Integer column = Integer.parseInt(key.substring(key.lastIndexOf("&")+1))+1;
                        //取出税额所在的列
                        String keyAdd = key.substring(0, key.lastIndexOf("&")+1)+column;
                        Object value = entry.getValue(); // 获取值
                        if (value != null) {
                            BigDecimal value1 = new BigDecimal(String.valueOf(value).replace(",", ""));
                            //取出税额
                            Double v = 0.00;
                            for (Map<String, Object> stringObjectMap : dataList1) {
                                for (String key2 : stringObjectMap.keySet()) {
                                    if(key2.equals(keyAdd)){
                                         v = Double.parseDouble(String.valueOf(stringObjectMap.get(keyAdd)).replace(",", ""));
                                    }
                                }
                            }
                            BigDecimal value2 = new BigDecimal(v);
                            BigDecimal add = value1.add(value2);
                            //税额=（不含税金额+税额）-round（（不含税金额+税额）/1.06,2）
                            BigDecimal subtract = add.subtract(value1.add(value2).divide(taxRate, 2, RoundingMode.HALF_UP));
                            result.put(keyAdd,  subtract.toString() );
                        } else {
                            result.put(keyAdd, null);
                        }
                    }
                    return result;
                })
                .collect(Collectors.toList());
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < dataList1.size(); i++) {
            Map<String, Object> mapFromList1 = dataList1.get(i);
            Map<String, Object> mapFromList2 = resultList.get(i);
            for (String key : mapFromList1.keySet()) {
                String[] keyArr = key.split("&");
                Double value1 =0.00;
                Double value2=0.00;
                if(mapFromList1.get(key)!=null){
                    value1 = Double.parseDouble(String.valueOf(mapFromList1.get(key)).replace(",", ""));
                }
                if (mapFromList2.get(key)!=null){
                    value2 = Double.parseDouble(String.valueOf(mapFromList2.get(key)).replace(",", ""));
                }
                BigDecimal bigDecimal = new BigDecimal(value2);
                bigDecimal = bigDecimal.setScale(2, RoundingMode.HALF_UP);
                value2 = bigDecimal.doubleValue();
                BigDecimal sub = NumberUtil.sub(value1 - value2).setScale(2, RoundingMode.HALF_UP);
                if (sub.compareTo(new BigDecimal("0.01"))==1 ||sub.compareTo(new BigDecimal("-0.01"))==-1){
                    sb.append("报表文件：").append(paramData.getFileName()).append(",");
                    sb.append("sheet名称").append(keyArr[0]).append(",");
                    sb.append("第").append(keyArr[1]).append("行");
                    sb.append("第").append(keyArr[2]).append("列");
                    sb.append("税额不等于（不含税金额+税额）-round（（不含税金额+税额）/"+taxRate.toString()+",2）").append(System.lineSeparator());
                }
            }
        }
        return sb.toString();
    }




    /**
     * 不含税金额= 含税金额/(1+税率)，保留两位小数，四舍五入
     *
     * @param paramData
     * @param dataList1
     * @param dataList2
     * @param itemName
     * @param type
     * @return
     * @throws Exception
     */
    public String noIncludeTaxAmountTaxRate(ParamData paramData, List<Map<String, Object>> dataList1, List<Map<String, Object>> dataList2, String itemName, Integer type) {
        List<Map<String, Object>> resultList = dataList2.stream()
                .map(map -> {
                    Object[] values = map.values().toArray();
                    Object[] keys = map.keySet().toArray();
                    String key = String.valueOf(keys[0]);
                    key = key.substring(0, key.lastIndexOf("&"));
                    Map<String, Object> result = new LinkedHashMap<>();
                    BigDecimal value1 = new BigDecimal(0.00);
                    BigDecimal value2 = new BigDecimal(0.00);
                    if (values[0] != null && values[1] != null) {
                        if (type != null && type == 1) {
                            value1 = new BigDecimal(String.valueOf(values[1]).replace("%", "")).divide(new BigDecimal(100),2,RoundingMode.HALF_UP) ;
                            value2 = new BigDecimal(String.valueOf(values[0]).replace(",", ""));

                        } else {
                            value1 = new BigDecimal(String.valueOf(values[0]).replace("%", "")).divide(new BigDecimal(100),2,RoundingMode.HALF_UP) ;
                            value2 = new BigDecimal(String.valueOf(values[1]).replace(",", ""));
                        }
                        //(value2 / (1 + value1))
                        BigDecimal vaueadd=value1.add(new BigDecimal(1));
                        BigDecimal vaue=value2.divide(vaueadd,2,RoundingMode.HALF_UP);
                        result.put(key,vaue.toString() );
                    } else {
                        result.put(key, null);
                    }
                    return result;
                })
                .collect(Collectors.toList());
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < dataList1.size(); i++) {
            Map<String, Object> mapFromList1 = dataList1.get(i);
            Map<String, Object> mapFromList2 = resultList.get(i);
            for (String key : mapFromList1.keySet()) {
                String key2 = key.substring(0, key.lastIndexOf("&"));
                String[] keyArr = key.split("&");
                Double value1 =0.00;
                Double value2=0.00;
                if(mapFromList1.get(key)!=null){
                    value1 = Double.parseDouble(String.valueOf(mapFromList1.get(key)).replace(",", ""));
                }
                if (mapFromList2.get(key2)!=null){
                    value2 = Double.parseDouble(String.valueOf(mapFromList2.get(key2)).replace(",", ""));
                }
                if (Math.abs(value1 - value2) >1.00&&!("江苏".equals(keyArr[0])||"浙江".equals(keyArr[0]))) {
                    sb.append("报表文件：").append(paramData.getFileName()).append(",");
                    sb.append("sheet名称").append(keyArr[0]).append(",");
                    sb.append("第").append(keyArr[1]).append("行");
                    sb.append("第").append(keyArr[2]).append("列");
                    sb.append(itemName).append(System.lineSeparator());
                }
            }
        }
//        for (Map<String, Object> mapFromList1 : dataList1) {
//            String key = mapFromList1.keySet().iterator().next();
//            String[] keyArr = key.split("&");
//            String key1 = key.substring(0, key.lastIndexOf("&"));
//            BigDecimal value1 = new BigDecimal(0.00);
//            if (mapFromList1.get(key) != null) {
//                value1 =new BigDecimal(String.valueOf(mapFromList1.get(key)).replace(",", ""))  ;
//            }
//            for (Map<String, Object> mapFromList2 : resultList) {
//                String key2 = mapFromList2.keySet().iterator().next();
//                BigDecimal value2 =new  BigDecimal(0.00);
//                if (mapFromList2.get(key2) != null) {
//                    value2 =new BigDecimal(String.valueOf(mapFromList2.get(key2)).replace(",", ""));
//                }
//                if (key1.equals(key2)) {
////                    if (Math.abs(value1 - value2) < 1.0) {
//                    if ( value1.compareTo(value2)!=0) {
//                        sb.append("报表文件：").append(paramData.getFileName()).append(",");
//                        sb.append("sheet名称").append(keyArr[0]).append(",");
//                        sb.append("第").append(keyArr[1]).append("行");
//                        sb.append("第").append(keyArr[2]).append("列");
//                        sb.append(itemName).append(System.lineSeparator());
//                    }
//                    break;
//                }
//            }
//        }
        return sb.toString();
    }

    /**
     * 含税金额=税额 +不含税金额，保留两位小数，四舍五入
     * “业务总收入”的含税金额=“政企结算金额”的含税金额+“省公司结算金额”的含税金额
     *
     * @param paramData
     * @param taxAmounts
     * @param amounts
     * @param itemName
     * @return
     * @throws Exception
     */
    public String includeTaxAmount(ParamData paramData, List<Map<String, Object>> taxAmounts, List<Map<String, Object>> amounts, String itemName) throws Exception {
        //含税金额+税额
        List<Map<String, Object>> resultList = amounts.stream()
                .map(map -> {
                    Object[] values = map.values().toArray();
                    Object[] keys = map.keySet().toArray();
                    String key = String.valueOf(keys[0]);
                    key = key.substring(0, key.lastIndexOf("&"));
                    Map<String, Object> result = new LinkedHashMap<>();
                    if (values[0] != null && values[1] != null) {
                        BigDecimal value1 = new BigDecimal(String.valueOf(values[0]).replace(",", ""));
                        BigDecimal value2 = new BigDecimal(String.valueOf(values[1]).replace(",", ""));
                        BigDecimal value=value1.add(value2).setScale(2, RoundingMode.HALF_UP);
                        result.put(key, value.toString());
                    } else {
                        result.put(key, null);
                    }
                    return result;
                })
                .collect(Collectors.toList());
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < taxAmounts.size(); i++) {
            Map<String, Object> mapFromList1 = taxAmounts.get(i);
            Map<String, Object> mapFromList2 = resultList.get(i);
            for (String key : mapFromList1.keySet()) {
                String key2 = key.substring(0, key.lastIndexOf("&"));
                String[] keyArr = key.split("&");
                String value1 = String.valueOf(mapFromList1.get(key)).replace(",", "");
                String value2 = String.valueOf(mapFromList2.get(key2)).replace(",", "");
                if (value1 == null || !value1.equals(value2)) {
                    sb.append("报表文件：").append(paramData.getFileName()).append(",");
                    sb.append("sheet名称").append(keyArr[0]).append(",");
                    sb.append("第").append(keyArr[1]).append("行");
                    sb.append("第").append(keyArr[2]).append("列");
                    sb.append(itemName).append(System.lineSeparator());
                }
            }
        }
        return sb.toString();
    }

    /**
     * 本期应结算金额=计费金额*结算比例(%)，保留两位小数，四舍五入
     */
    public String includeTaxMultiply(ParamData paramData, List<Map<String, Object>> taxAmounts, List<Map<String, Object>> amounts, String itemName) throws Exception {

        List<Map<String, Object>> resultList = amounts.stream()
                .map(map -> {
                    Object[] values = map.values().toArray();
                    Object[] keys = map.keySet().toArray();
                    String key = String.valueOf(keys[0]);
                    key = key.substring(0, key.lastIndexOf("&"));
                    Map<String, Object> result = new LinkedHashMap<>();
                    if (values[0] != null && values[1] != null) {
                        BigDecimal value1 = new BigDecimal(String.valueOf(values[0]).replace(",", ""));
                        BigDecimal value2 = new BigDecimal(String.valueOf(values[1]).replace(",", "")).divide(new BigDecimal(100),2, RoundingMode.HALF_UP);
                        //value1 * (value2 / 100)

                        BigDecimal value = value1.multiply(value2).setScale(2, RoundingMode.HALF_UP);
                        result.put(key, value);
                    } else {
                        result.put(key, null);
                    }
                    return result;
                })
                .collect(Collectors.toList());
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < taxAmounts.size(); i++) {
            Map<String, Object> mapFromList1 = taxAmounts.get(i);
            Map<String, Object> mapFromList2 = resultList.get(i);
            for (String key : mapFromList1.keySet()) {
                String key2 = key.substring(0, key.lastIndexOf("&"));
                String[] keyArr = key.split("&");
                String value1 = String.valueOf(mapFromList1.get(key)).replace(",", "");
                String value2 = String.valueOf(mapFromList2.get(key2)).replace(",", "");
                if (value1 == null || !value1.equals(value2)) {
                    sb.append("报表文件：").append(paramData.getFileName()).append(",");
                    sb.append("sheet名称").append(keyArr[0]).append(",");
                    sb.append("第").append(keyArr[1]).append("行");
                    sb.append("第").append(keyArr[2]).append("列");
                    sb.append(itemName).append(System.lineSeparator());
                }
            }
        }
        return sb.toString();
    }


    /**
     * “税率”的报表税率稽核
     *
     * @param paramData
     * @param dataList
     * @return
     * @throws Exception
     */
    public String taxRate(ParamData paramData, List<Map<String, Object>> dataList) throws Exception {
        StringBuffer sb = new StringBuffer();
        for (Map<String, Object> dataMap : dataList) {
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                String[] keyArr = entry.getKey().split("&");
                String value = String.valueOf(entry.getValue());
                if (!paramData.getTaxRate().equals(value)&&!("江苏".equals(keyArr[0])||"浙江".equals(keyArr[0]))) {
                    sb.append("报表文件：").append(paramData.getFileName()).append(",");
                    sb.append("sheet名称").append(keyArr[0]).append(",");
                    sb.append("第").append(keyArr[1]).append("行");
                    sb.append("第").append(keyArr[2]).append("列");
                    sb.append("税率不是").append(paramData.getTaxRate()).append(System.lineSeparator());
                }
            }

        }
        return sb.toString();
    }

    /**
     * 列不为空，不为全数字
     *
     * @param paramData
     * @param dataList
     * @param columnName
     * @return
     * @throws Exception
     */
    public String columnIsEmpty(ParamData paramData, List<Map<String, Object>> dataList, String columnName) throws Exception {
        StringBuffer sb = new StringBuffer();
        for (Map<String, Object> dataMap : dataList) {
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                String[] keyArr = entry.getKey().split("&");
                String value = String.valueOf(entry.getValue());
                if ((value == null || StrUtil.isBlank(value) || "null".equals(value))&&!("江苏".equals(keyArr[0])||"浙江".equals(keyArr[0]))) {
                    sb.append("报表文件：").append(paramData.getFileName()).append(",");
                    sb.append("sheet名称").append(keyArr[0]).append(",");
                    sb.append("第").append(keyArr[1]).append("行");
                    sb.append("第").append(keyArr[2]).append("列");
                    sb.append("“").append(columnName).append("”为空").append(System.lineSeparator());
                }
            }

        }
        return sb.toString();
    }





    public String matchHeader(ParamData paramData,List<Map<String, Object>> dataList, Map<String, Object> headerMaps, String columns, String itemName,String type) throws Exception {
        StringBuffer sb = new StringBuffer();

        for (Map<String, Object> dataMap : dataList) {

            String sheetName = dataMap.keySet().iterator().next().split("&")[0];
            Map<String, Object> headerMap = (Map<String, Object>) headerMaps.get(sheetName);
            String header = String.valueOf(headerMap.get(Integer.valueOf(columns)));
            String fileName = paramData.getFileName();
            String newItemName=itemName;

            //替换税率
            if(itemName.contains("%%%")){
                int percentIndex =fileName.indexOf("%");
                String percentString = paramData.getFileName().substring(percentIndex - 1, percentIndex + 1);
                newItemName = newItemName.replace("%%%", percentString);
            }


            //和sheetName取省份
            if(newItemName.contains("***")){
                for (String key : DictMap.provinceMap) {
                    if(sheetName.equals(key)){
                        newItemName=newItemName.replace("***", "（"+ key +"）");
                    }
                }
            }

            //从文件名里取省份
            for (String key : DictMap.provinceMap) {
                if(fileName.contains(key)){
                    newItemName=newItemName.replace("$$$", "（"+ key +"）");
                }
            }

            //从文件名里取日期
            if(newItemName.contains("YYYY") && newItemName.contains("MM")){
                String date = fileName.substring(fileName.length() - 10 , fileName.length() - 4);
                newItemName=newItemName.replace("YYYY",date.substring(0,4));
                newItemName=newItemName.replace("MM",date.substring(4,6));
            }



            //替换暂估
            if(fileName.contains("_暂估_")&&"matchHeader".equals(type)){
                newItemName=newItemName+"_暂估";
            }

            //替换销暂估
            if(fileName.contains("_销暂估_")&&"matchHeader".equals(type)){
                newItemName=newItemName+"_销暂估";
            }


            //替换调账
            boolean flag=true;
            if(fileName.contains("_调账_")){
                //因为计费的程序会在"调账"两个字前后随机出现不固定数量空格,必须使用split实现
                String[] s = newItemName.split("###");
                if(header.contains(s[0]) && (s.length>1 ? header.contains(s[1]):true) && header.contains("(调账)")){
                    flag=true;
                }else{
                    flag=false;
                }
            }else{
                String[] s = newItemName.split("###");
                if(header.contains(s[0]) && (s.length>1 ? header.contains(s[1]):true) ){
                    flag=true;
                }else{
                    flag=false;
                }
            }


            if (!flag) {
                sb.append("报表文件：").append(paramData.getFileName()).append(",");
                sb.append("sheet名称").append(sheetName).append(",");
                sb.append("第一行的表头不匹配");
                sb.append("“").append(newItemName).append("”").append(System.lineSeparator());
            }
        }

        return sb.toString();
    }

    public String matchSheetHeader(ParamData paramData,List<Map<String, Object>> dataList, Map<String, Object> headerMaps, String columns) throws Exception {
        StringBuffer sb = new StringBuffer();
        Set<String> sheetProvince = new HashSet<>();
        for (Map<String, Object> dataMap : dataList) {
            boolean isConTainsflag=false;
            String sheetName = dataMap.keySet().iterator().next().split("&")[0];
            if (!DictMap.provinceMap.contains(sheetName)) {
                isConTainsflag = true;
            }
            sheetProvince.add(sheetName);
            Map<String, Object> headerMap = (Map<String, Object>) headerMaps.get(sheetName);
            String header = String.valueOf(headerMap.get(Integer.valueOf(columns)));
            if (isConTainsflag) {
                sb.append("报表文件：").append(paramData.getFileName()).append(",");
                sb.append("sheet名称").append(sheetName).append(",");
                sb.append("sheet名称不在31省中");
                sb.append("“").append("”").append(System.lineSeparator());
            }else {
                if (!header.contains(sheetName)){
                    sb.append("报表文件：").append(paramData.getFileName()).append(",");
                    sb.append("sheet名称").append(sheetName).append(",");
                    sb.append("报表表头省份和sheet不一致");
                    sb.append("“").append(header).append("”").append(System.lineSeparator());
                }
            }
        }
        if (sheetProvince.size() != dataList.size()) {
            if (StringUtils.isEmpty(sb)) {
                sb.append("报表文件：").append(paramData.getFileName()).append(",");
                sb.append("sheet名称在31省中重复");
                sb.append(System.lineSeparator());
            }
        }
        return sb.toString();
    }

    public String matchSheet(ParamData paramData,List<Map<String, Object>> dataList) throws Exception {
        StringBuffer sb = new StringBuffer();
        Set<String> sheetProvince = new HashSet<>();
        for (Map<String, Object> dataMap : dataList) {
            boolean isConTainsflag=false;
            String sheetName = dataMap.keySet().iterator().next().split("&")[0];
            if (!DictMap.provinceMapPlus.contains(sheetName)) {
                isConTainsflag = true;
            }
            sheetProvince.add(sheetName);
            String province = (String) dataMap.get(dataMap.keySet().iterator().next());
            if (isConTainsflag) {
                sb.append("报表文件：").append(paramData.getFileName()).append(",");
                sb.append("sheet名称").append(sheetName).append(",");
                sb.append("sheet名称不在31省和互联网公司中");
                sb.append("“").append("”").append(System.lineSeparator());
            }else {
                if (!province.contains(sheetName)){
                    sb.append("报表文件：").append(paramData.getFileName()).append(",");
                    sb.append("sheet名称").append(sheetName).append(",");
                    sb.append("报表中省公司和sheet不一致");
                    sb.append("“").append(province).append("”").append(System.lineSeparator());
                }
            }
        }
        if (sheetProvince.size() != dataList.size()) {
            if (StringUtils.isEmpty(sb)) {
                sb.append("报表文件：").append(paramData.getFileName()).append(",");
                sb.append("sheet名称在31省和互联网公司中重复");
                sb.append(System.lineSeparator());
            }
        }
        return sb.toString();
    }

    public String matchDynamiSheetHeader(ParamData paramData,List<Map<String, Object>> dataList, Map<String, Object> headerMaps, String columns, String itemName,String type) throws Exception {
        StringBuffer sb = new StringBuffer();
        for (Map<String, Object> dataMap : dataList) {
            String sheetName = dataMap.keySet().iterator().next().split("&")[0];
            Map<String, Object> headerMap = (Map<String, Object>) headerMaps.get(sheetName);
            String header = String.valueOf(headerMap.get(Integer.valueOf(columns)));
            String fileName = paramData.getFileName();
            String newItemName=itemName;
            //替换税率
            if(itemName.contains("%%%")){
                int percentIndex =fileName.indexOf("%");
                String percentString = paramData.getFileName().substring(percentIndex - 1, percentIndex + 1);
                newItemName = newItemName.replace("%%%", percentString);
            }
            //和sheetName取省份
            if (type.equals("1")) {
                if(newItemName.contains("***")){
                    for (String key : DictMap.provinceMap) {
                        if(sheetName.equals(key)){
                            newItemName=newItemName.replace("***",  key);
                        }
                    }
                }
            }else {
                if(newItemName.contains("***")){
                    for (String key : DictMap.provinceMap) {
                        if(sheetName.equals(key)){
                            newItemName=newItemName.replace("***", "（"+ key +"）");
                        }
                    }
                }
            }

            //从文件名里取省份
            for (String key : DictMap.provinceMap) {
                if(fileName.contains(key)){
                    newItemName=newItemName.replace("$$$", "（"+ key +"）");
                }
            }
            //从文件名里取日期
            if(newItemName.contains("YYYY") && newItemName.contains("MM")){
                String date = paramData.getSettleMonth();
                newItemName=newItemName.replace("YYYY",date.substring(0,4));
                newItemName=newItemName.replace("MM",date.substring(4,6));
            }
            //替换暂估
            if(fileName.contains("_暂估_")&&"matchHeader".equals(type)){
                newItemName=newItemName+"_暂估";
            }
            //替换销暂估
            if(fileName.contains("_销暂估_")&&"matchHeader".equals(type)){
                newItemName=newItemName+"_销暂估";
            }
            //替换调账
            boolean flag=true;
            if(fileName.contains("_调账_")){
                //因为计费的程序会在"调账"两个字前后随机出现不固定数量空格,必须使用split实现
                String[] s = newItemName.split("###");
                if(header.contains(s[0]) && (s.length>1 ? header.contains(s[1]):true) && header.contains("(调账)")){
                    flag=true;
                }else{
                    flag=false;
                }
            }else{
                String[] s = newItemName.split("###");
                if(header.contains(s[0]) && (s.length>1 ? header.contains(s[1]):true) ){
                    flag=true;
                }else{
                    flag=false;
                }
            }
            if (!flag) {
                sb.append("报表文件：").append(paramData.getFileName()).append(",");
                sb.append("sheet名称").append(sheetName).append(",");
                sb.append("第一行的表头不匹配");
                sb.append("“").append(newItemName).append("”").append(System.lineSeparator());
            }
        }
        return sb.toString();
    }



    public String matchDoubleHeader(ParamData paramData,List<Map<String, Object>> secondHeader, String itemName) throws Exception {
        StringBuffer sb = new StringBuffer();
        String newItemName=itemName;
        String fileName = paramData.getFileName();
        String secondLineHeader="";
        //从文件名里取日期
        if(newItemName.contains("YYYY") && newItemName.contains("MM")){
            String date = fileName.substring(fileName.length() - 10 , fileName.length() - 4);
            newItemName=newItemName.replace("YYYY",date.substring(0,4));
            newItemName=newItemName.replace("MM",date.substring(4,6));
        }

        //替换暂估
        if(fileName.contains("_暂估_")){
            newItemName=newItemName+"_暂估";
        }

        //替换销暂估
        if(fileName.contains("_销暂估_")){
            newItemName=newItemName+"_销暂估";
        }

        Set<String> sheetNames = secondHeader.get(0).keySet();
        for(String sheetName : sheetNames){
            secondLineHeader = String.valueOf(secondHeader.get(0).get(sheetName));
        }

        if (!secondLineHeader.equals(newItemName)) {
            sb.append("报表文件：").append(paramData.getFileName()).append(",");
            sb.append("第二行的表头不匹配");
            sb.append("“").append(newItemName).append("”").append(System.lineSeparator());
        }

        return sb.toString();
    }
        /**
         * 不为全数字
         *
         * @param paramData
         * @param dataList
         * @param columnName
         * @return
         * @throws Exception
         */
    public String columnIsNumbers(ParamData paramData, List<Map<String, Object>> dataList, String columnName) throws Exception {
        StringBuffer sb = new StringBuffer();
        for (Map<String, Object> dataMap : dataList) {
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                String[] keyArr = entry.getKey().split("&");
                String value = String.valueOf(entry.getValue());
                if (value.matches("^[0-9]+$")) {
                    sb.append("报表文件：").append(paramData.getFileName()).append(",");
                    sb.append("sheet名称").append(keyArr[0]).append(",");
                    sb.append("第").append(keyArr[1]).append("行");
                    sb.append("第").append(keyArr[2]).append("列");
                    sb.append("“").append(columnName).append("”是全数字").append(System.lineSeparator());
                }
            }

        }
        return sb.toString();
    }

    /**
     * 税率为6
     *
     */
    public String taxRateSix(ParamData paramData, List<Map<String, Object>> dataList, String columnName) throws Exception {
        StringBuffer sb = new StringBuffer();
        for (Map<String, Object> dataMap : dataList) {
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                String[] keyArr = entry.getKey().split("&");
                Integer value = Integer.valueOf(String.valueOf(entry.getValue()).replace("%",""));

                if (!value.equals(6)) {
                    sb.append("报表文件：").append(paramData.getFileName()).append(",");
                    sb.append("sheet名称").append(keyArr[0]).append(",");
                    sb.append("第").append(keyArr[1]).append("行");
                    sb.append("第").append(keyArr[2]).append("列");
                    sb.append("“").append(columnName).append("”税率不为6").append(System.lineSeparator());
                }
            }

        }
        return sb.toString();
    }

    /**
     * 合计=对应列求和
     *
     * @param paramData
     * @param dataList
     * @param dataList2
     * @return
     * @throws Exception
     */
    public String sumColumn(ParamData paramData, List<Map<String, Object>> dataList, List<Map<String, Object>> dataList2, String itemName) throws Exception {
        StringBuffer sb = new StringBuffer();
        // 使用流操作分组并求和
        List<Map<String, Object>> resultList2 = dataList2.stream()
                .filter(map -> map.get(map.keySet().iterator().next()) != null)
                .collect(Collectors.groupingBy(
                        map -> {
                            String[] keyArr = map.keySet().iterator().next().split("&");
                            return keyArr[0] + "&" + keyArr[2];
                        }, // 截取第一个字符作为分组的键
                        Collectors.summingDouble(map -> Double.parseDouble(String.valueOf(map.get(map.keySet().iterator().next())).replace(",", "")))
                ))
                .entrySet()
                .stream()
                .map(entry -> {
                    Map<String, Object> resultEntry = new LinkedHashMap<>();
                    BigDecimal value=new BigDecimal(entry.getValue()).setScale(2, RoundingMode.HALF_UP);
                    resultEntry.put(entry.getKey(), value.toString());
                    return resultEntry;
                })
                .collect(Collectors.toList());

        for (Map<String, Object> mapFromList1 : dataList) {
            String key = mapFromList1.keySet().iterator().next();
            String[] keyArr = key.split("&");
            String key1 = keyArr[0] + "&" + keyArr[2];
            String value1 = String.valueOf(mapFromList1.get(key)).replace(",", "");
            for (Map<String, Object> mapFromList2 : resultList2) {
                String key2 = mapFromList2.keySet().iterator().next();
                if (key1.equals(key2)) {
                    String value2 = String.valueOf(mapFromList2.get(key2));
                    if (!value1.equals(value2)&&!("江苏".equals(keyArr[0])||"浙江".equals(keyArr[0]))) {
                        sb.append("报表文件：").append(paramData.getFileName()).append(",");
                        sb.append("sheet名称").append(keyArr[0]).append(",");
                        sb.append("第").append(keyArr[1]).append("行");
                        sb.append("第").append(keyArr[2]).append("列");
                        sb.append(itemName).append(System.lineSeparator());
                    }
                    break;
                }
            }
        }

        return sb.toString();
    }

    /**
     * 合计=对应列求和(江苏&浙江 专供)
     *
     * @param paramData
     * @param dataList
     * @param dataList2
     * @return
     * @throws Exception
     */
    public String sumColumnJSZJ(ParamData paramData, List<Map<String, Object>> dataList, List<Map<String, Object>> dataList2, String itemName) throws Exception {
        StringBuffer sb = new StringBuffer();
        // 使用流操作分组并求和
        List<Map<String, Object>> resultList2 = dataList2.stream()
                .filter(map -> map.get(map.keySet().iterator().next()) != null)
                .collect(Collectors.groupingBy(
                        map -> {
                            String[] keyArr = map.keySet().iterator().next().split("&");
                            return keyArr[0] + "&" + keyArr[2];
                        }, // 截取第一个字符作为分组的键
                        Collectors.summingDouble(map -> Double.parseDouble(String.valueOf(map.get(map.keySet().iterator().next())).replace(",", "")))
                ))
                .entrySet()
                .stream()
                .map(entry -> {
                    Map<String, Object> resultEntry = new LinkedHashMap<>();
                    BigDecimal value=new BigDecimal(entry.getValue()).setScale(2, RoundingMode.HALF_UP);
                    resultEntry.put(entry.getKey(), value.toString());
                    return resultEntry;
                })
                .collect(Collectors.toList());

        for (Map<String, Object> mapFromList1 : dataList) {
            String key = mapFromList1.keySet().iterator().next();
            String[] keyArr = key.split("&");
            String key1 = keyArr[0] + "&" + keyArr[2];
            String value1 = String.valueOf(mapFromList1.get(key)).replace(",", "");
            for (Map<String, Object> mapFromList2 : resultList2) {
                String key2 = mapFromList2.keySet().iterator().next();
                if (key1.equals(key2)) {
                    String value2 = String.valueOf(mapFromList2.get(key2));
                    if("江苏".equals(keyArr[0])||"浙江".equals(keyArr[0])){
                        if (!value1.equals(value2)) {
                            sb.append("报表文件：").append(paramData.getFileName()).append(",");
                            sb.append("sheet名称").append(keyArr[0]).append(",");
                            sb.append("第").append(keyArr[1]).append("行");
                            sb.append("第").append(keyArr[2]).append("列");
                            sb.append(itemName).append(System.lineSeparator());
                        }
                        break;
                    }
                }
            }
        }

        return sb.toString();
    }

    /**
     *
     * 单列 合计对比
     *
     */
    public String sumSingleColumn(ParamData paramData, List<Map<String, Object>> dataList, List<Map<String, Object>> dataList2, String itemName) throws Exception {
        StringBuffer sb = new StringBuffer();
        // 单列求和
        List<Map<String, Object>> resultList2 = dataList2.stream()
                .filter(map -> map.get(map.keySet().iterator().next()) != null)
                .collect(Collectors.groupingBy(
                        map -> {
                            String[] keyArr = map.keySet().iterator().next().split("&");
                            return keyArr[0] ;
                        }, // 截取第一个字符作为分组的键
                        Collectors.summingDouble(map -> Double.parseDouble(String.valueOf(map.get(map.keySet().iterator().next())).replace(",", "")))
                ))
                .entrySet()
                .stream()
                .map(entry -> {
                    Map<String, Object> resultEntry = new LinkedHashMap<>();
                    BigDecimal value=new BigDecimal(entry.getValue()).setScale(2, RoundingMode.HALF_UP);
                    resultEntry.put(entry.getKey(), value.toString());
                    return resultEntry;
                })
                .collect(Collectors.toList());

        for (Map<String, Object> mapFromList1 : dataList) {
            String key = mapFromList1.keySet().iterator().next();
            String[] keyArr = key.split("&");
            String key1 = keyArr[0] ;
            String value1 = String.valueOf(mapFromList1.get(key)).replace(",", "");
            for (Map<String, Object> mapFromList2 : resultList2) {
                String key2 = mapFromList2.keySet().iterator().next();
                if (key1.equals(key2)) {
                    String value2 = String.valueOf(mapFromList2.get(key2));
                    if (!value1.equals(value2)) {
                        sb.append("报表文件：").append(paramData.getFileName()).append(",");
                        sb.append("sheet名称").append(keyArr[0]).append(",");
                        sb.append("第").append(keyArr[1]).append("行");
                        sb.append("第").append(keyArr[2]).append("列");
                        sb.append(itemName).append(System.lineSeparator());
                    }
                    break;
                }
            }
        }

        return sb.toString();
    }

    /**
     * 获取列数据
     *
     * @param pathName
     * @param startRow
     * @param endRow
     * @param columns
     * @return
     */
    Map<String, Object> headerMap;
    List<Map<String, Object>> dataFxList;
    public List<Map<String, Object>> getColumnDataList(String pathName, Integer startRow, Integer endRow, Integer[] columns) {
        ExcelListenerWithColumn dataListener = new ExcelListenerWithColumn(startRow, endRow, columns);
        EasyExcel.read(pathName, dataListener).ignoreEmptyRow(true).doReadAll();
        List<Map<String, Object>> dataList = dataListener.getDataList();
        headerMap=dataListener.getHeaderMap();
        dataFxList = dataListener.getDataFxList();
        return dataList;
    }

    //如果多个sheet 读取指定sheet的内容
    public List<Map<String, Object>> getColumnDataList(String pathName,Integer sheetNum, Integer startRow, Integer endRow, Integer[] columns) {
        ExcelListenerWithColumn dataListener = new ExcelListenerWithColumn(startRow, endRow, columns);
        EasyExcel.read(pathName, dataListener).sheet(sheetNum).doRead();
        List<Map<String, Object>> dataList = dataListener.getDataList();
        headerMap=dataListener.getHeaderMap();
        dataFxList = dataListener.getDataFxList();
        return dataList;
    }
    public List<Map<String, Object>> getColumnDataList2(String pathName,Integer sheetNum, Integer startRow, Integer endRow, Integer[] columns, Integer startColumn) {
        ExcelListenerWithStartColumn dataListener = new ExcelListenerWithStartColumn(startRow, endRow, columns, startColumn);
        EasyExcel.read(pathName, dataListener).sheet(sheetNum).doRead();
        List<Map<String, Object>> dataList = dataListener.getDataList();
        dataFxList = dataListener.getDataFxList();
        return dataList;
    }



    public Map<String, Object> getHeaderMap(String pathName, Integer startRow, Integer endRow, Integer[] columns) {
        return headerMap;
    }

    public Integer[] getColumns(ExcelParam.Param param) {
        Integer[] columns = null;
        if (StrUtil.isNotBlank(param.getColumns())) {
            String column = param.getColumns().replaceAll(" ", "");
            columns = Arrays.stream(column.split(","))
                    .map(Integer::parseInt) // 将字符串映射为整数
                    .toArray(Integer[]::new);
        }
        return columns;
    }


    public String signSubject(ParamData paramData, List<Map<String, Object>> dataList, String columnName) {
        StringBuffer sb = new StringBuffer();
        for (Map<String, Object> dataMap : dataList) {
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                String[] keyArr = entry.getKey().split("&");
                String value = String.valueOf(entry.getValue());
                if (!value.contains(paramData.getSignSubject())) {
                    sb.append("报表文件：").append(paramData.getFileName()).append(",");
                    sb.append("sheet名称").append(keyArr[0]).append(",");
                    sb.append("第").append(keyArr[1]).append("行");
                    sb.append("第").append(keyArr[2]).append("列");
                    sb.append("税率不是").append(paramData.getTaxRate()).append(System.lineSeparator());
                }
            }

        }
        return sb.toString();
    }

    public String settlePartySumColumn(ParamData paramData, List<Map<String, Object>> dataList, List<Map<String, Object>> dataList2, String itemName, Integer type) {
        StringBuffer sb = new StringBuffer();
        log.info("dataList2={}",JSONObject.toJSONString(dataList2));
        // 使用流操作分组并求和
//        List<Map<String, Object>> resultList2 = dataList2.stream()
//                .filter(map -> {
//                    String value = String.valueOf(map.get(map.keySet().iterator().next())).replace(",", "");
//                    if (type == 1 && value.matches("[0-9]+.?[0-9]*")) {
//                        return true;
//                    } else if (type == 2 && value.matches("-[0-9]+.?[0-9]*")) {
//                        return true;
//                    } else {
//                        return false;
//                    }
//                })
//                .collect(Collectors.groupingBy(
//                        map -> {
//                            String[] keyArr = map.keySet().iterator().next().split("&");
//                            return keyArr[0] + "&" + keyArr[2];
//                        }, // 截取第一个字符作为分组的键
//                        Collectors.summingDouble(map -> Double.parseDouble(String.valueOf(map.get(map.keySet().iterator().next())).replace(",", "")))
//                ))
//                .entrySet()
//                .stream()
//                .map(entry -> {
//                    Map<String, Object> resultEntry = new LinkedHashMap<>();
//                    BigDecimal value=new BigDecimal(entry.getValue()).setScale(2, RoundingMode.HALF_UP);
//                    resultEntry.put(entry.getKey(),value);
//                    return resultEntry;
//                })
//                .collect(Collectors.toList());
        Map<String,BigDecimal> resultListMap = new LinkedHashMap<>();
        dataList2 = dataList2.stream()
                .map(map -> map.entrySet().stream().filter(entry -> {
                        String value = String.valueOf(entry.getValue()).replace(",", "");
                        if (type == 1 && value.matches("[0-9]+.?[0-9]*")) {
                            return true;
                        } else if (type == 2 && value.matches("-[0-9]+.?[0-9]*")) {
                            return true;
                        } else {
                            return false;
                        }
                    }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue))).collect(Collectors.toList());

        dataList2.stream().forEach(map -> {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String[] split = entry.getKey().split("&");
                BigDecimal value1 =new BigDecimal(String.valueOf(entry.getValue()).replace(",", ""));
                String titleName = split[0]+"&"+split[2];
                BigDecimal bigDecimal = ObjectUtil.isNull(resultListMap.get(titleName))?new BigDecimal(0.00):resultListMap.get(titleName);
                BigDecimal addAmount = bigDecimal.add(value1);
                resultListMap.put(titleName,addAmount);
            }

        });



        for (Map.Entry<String, Object> mapFromList1 : dataList.get(0).entrySet()) {
            String key = mapFromList1.getKey();
            String[] keyArr = key.split("&");
            String key1 = keyArr[0] + "&" + keyArr[2];
            String value1 = String.valueOf(mapFromList1.getValue()).replace(",", "");
            for (Map.Entry<String, BigDecimal> mapFromList2 : resultListMap.entrySet()) {
                String key2 = mapFromList2.getKey();
                if (key1.equals(key2)) {
                    String value2 = String.valueOf(mapFromList2.getValue());
                    if (!value1.equals(value2)) {
                        sb.append("报表文件：").append(paramData.getFileName()).append(",");
                        sb.append("sheet名称").append(keyArr[0]).append(",");
                        sb.append("第").append(keyArr[1]).append("行");
                        sb.append("第").append(keyArr[2]).append("列");
                        sb.append(itemName).append(System.lineSeparator());
                    }
                    break;
                }
            }
        }

        return sb.toString();
    }

    public String custAndSettleAmount(ParamData paramData, List<Map<String, Object>> dataList, ExcelParam.Param param1) {
        StringBuffer sb = new StringBuffer();
        for (Map<String, Object> dataMap : dataList) {
            Object[] values = dataMap.values().toArray();
            Object[] keys = dataMap.keySet().toArray();
            String key = String.valueOf(keys[1]);
            String[] keyArr = key.split("&");
            Integer value1 = Integer.valueOf(String.valueOf(values[0]));
            Double value2 = Double.parseDouble(String.valueOf(values[1]).replace(",", ""));
            if (value1 == 0 && !Double.valueOf(value2).equals(0.00)) {
                sb.append("报表文件：").append(paramData.getFileName()).append(",");
                sb.append("sheet名称").append(keyArr[0]).append(",");
                sb.append("第").append(keyArr[1]).append("行");
                sb.append("第").append(keyArr[2]).append("列");
                sb.append("当客户数为0,“结算额”不为0").append(System.lineSeparator());
            }
        }
        return sb.toString();
    }

    public List<Map<String, Object>> getColumnDataList2(String pathName, Integer startRow, Integer endRow, Integer[] columns, Integer startColumn) {
        ExcelListenerWithStartColumn dataListener = new ExcelListenerWithStartColumn(startRow, endRow, columns, startColumn);
        EasyExcel.read(pathName, dataListener).ignoreEmptyRow(true).doReadAll();
        List<Map<String, Object>> dataList = dataListener.getDataList();
        dataFxList = dataListener.getDataFxList();
        return dataList;
    }


    /**
     * @Author: mateng(<EMAIL>)
     * @Description: 根据入参稽核报表数据，对省公司对应的结算额和对应31省的金额的相反数进行稽核
     * @Param:
     * @return:
     * @Create  on：2024/3/5 16:33
     */
    public String auditTheSettleAmount(ParamData paramData, List<Map<String, Object>> dataList1, List<Map<String, Object>> dataList2,List<Map<String, Object>> dataList3,List<Map<String, Object>> dataList4, String itemName) {
        StringBuffer sb = new StringBuffer();
        //转化list4的key
        List<Map<String, String>> columnNameList = dataList4.stream().map(map -> {
            Map<String, String> newMap = new LinkedHashMap();
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String column = entry.getKey().substring(entry.getKey().lastIndexOf("&")+1);
                String value = ObjectUtil.isNull(entry.getValue()) ? null : (String) entry.getValue();
                if (StringUtils.isEmpty(value)) {
                    String name = newMap.get(String.valueOf(Integer.valueOf(column) - 1));
                    newMap.put(column, name);
                } else
                    newMap.put(column, value);
            }
            return newMap;
        }).collect(Collectors.toList());
        Map<String, String> columnNameMap = columnNameList.get(0);
        log.info("转换后的列对应的列名信息={}", JSONObject.toJSONString(columnNameMap));
        //获取对应公司的31省的不含税金额和税额的和
        Map<String,BigDecimal> nameAllAmountMap = new LinkedHashMap<>();
        dataList3.stream().forEach(map -> {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String column = entry.getKey().substring(entry.getKey().lastIndexOf("&")+1);
                BigDecimal value1 =new BigDecimal(String.valueOf(entry.getValue()).replace(",", ""));
                String titleName = columnNameMap.get(column);
                BigDecimal bigDecimal = ObjectUtil.isNull(nameAllAmountMap.get(titleName))?new BigDecimal(0.00):nameAllAmountMap.get(titleName);
                BigDecimal addAmount = bigDecimal.add(value1);
                nameAllAmountMap.put(titleName,addAmount);
            }

        });
        //转化下list1中省政企公司对应的行数和名称
        Map<String, String> lineNameMap = new LinkedHashMap();
        dataList1.stream().forEach(map -> {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String[] split = entry.getKey().split("&");
                String lineNum = split[split.length-2];
                String value = (String) entry.getValue();
                lineNameMap.put(lineNum,value);
            }
        });
        log.info("转换后的列对应的行号和省政企名信息={}", JSONObject.toJSONString(lineNameMap));
        //对各个省政企公司中31省的不含税金额和税额总和的相反数和省政企公司的结算额进行对比是否相等
        dataList2.stream().forEach(map -> {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String[] split = entry.getKey().split("&");
                BigDecimal proFinalAmount =new BigDecimal(String.valueOf(entry.getValue()).replace(",", ""));
                String name = lineNameMap.get(split[split.length - 2]);
                BigDecimal amountBy31Pro = ObjectUtil.isNull(nameAllAmountMap.get(name))?new BigDecimal(0.00):nameAllAmountMap.get(name);
                log.info("比较["+name+"]的结算额：【"+proFinalAmount+"】和31省的不含税额和税额的总和【"+amountBy31Pro+"】的相反数["+amountBy31Pro.negate()+"]是否相等！");
                if (proFinalAmount.compareTo(amountBy31Pro.negate()) != 0) {
                    String newItemName = itemName.replaceAll("\\*\\*\\*",name);
                    sb.append("报表文件：").append(paramData.getFileName()).append(",");
                    sb.append("sheet名称").append(split[0]).append(",");
                    sb.append("第").append(split[1]).append("行");
                    sb.append("第").append(split[2]).append("列");
                    sb.append(newItemName).append(System.lineSeparator());
                }
            }
        });

        return sb.toString();
    }

    public String multipleAddition(ParamData paramData, List<Map<String, Object>> dataList1, List<Map<String, Object>> dataList2, String itemName) {
        StringBuffer sb = new StringBuffer();
        // 使用流操作分组并求和
        List<Map<String, Object>> resultList2 = dataList2.stream().map(map -> {
            Map<String, Object> newMap = new LinkedHashMap();
            String key = "";
            BigDecimal value = new BigDecimal(0.00);
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                key = entry.getKey().substring(0, entry.getKey().lastIndexOf("&"));
                if (entry.getValue() != null) {
                    BigDecimal value1 =new BigDecimal(String.valueOf(entry.getValue()).replace(",", ""));
                    value = value.add(value1);
                }
            }
            newMap.put(key, value.setScale(2, RoundingMode.HALF_UP).toString());
            return newMap;
        }).collect(Collectors.toList());
        for (Map<String, Object> mapFromList1 : dataList1) {
            String key = mapFromList1.keySet().iterator().next();
            String[] keyArr = key.split("&");
            String key1 = key.substring(0, key.lastIndexOf("&"));
            String value1 = String.valueOf(mapFromList1.get(key)).replace(",", "");
            for (Map<String, Object> mapFromList2 : resultList2) {
                String key2 = mapFromList2.keySet().iterator().next();
                if (key1.equals(key2)) {
                    String value2 = String.valueOf(mapFromList2.get(key2));
                    if (!value1.equals(value2)) {
                        sb.append("报表文件：").append(paramData.getFileName()).append(",");
                        sb.append("sheet名称").append(keyArr[0]).append(",");
                        sb.append("第").append(keyArr[1]).append("行");
                        sb.append("第").append(keyArr[2]).append("列");
                        sb.append(itemName).append(System.lineSeparator());
                    }
                    break;
                }
            }
        }

        return sb.toString();
    }

    public String notZero(ParamData paramData, List<Map<String, Object>> dataList, String itemName) {
        StringBuffer sb = new StringBuffer();
        for (Map<String, Object> dataMap : dataList) {
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                String[] keyArr = entry.getKey().split("&");
                String value = String.valueOf(entry.getValue()).replace(",", "");
                if (!Double.valueOf(value).equals(0.00)) {
                    sb.append("报表文件：").append(paramData.getFileName()).append(",");
                    sb.append("sheet名称").append(keyArr[0]).append(",");
                    sb.append("第").append(keyArr[1]).append("行");
                    sb.append("第").append(keyArr[2]).append("列");
                    sb.append(itemName).append("为空").append(System.lineSeparator());
                }
            }

        }
        return sb.toString();
    }

    public String sumAndMult(ParamData paramData, List<Map<String, Object>> dataList1, List<Map<String, Object>> dataList2, List<Map<String, Object>> dataList3, String itemName) {
        StringBuffer sb = new StringBuffer();
        List<Map<String, Object>> resultList1 = dataList2.stream().map(map -> {
            Map<String, Object> newMap = new LinkedHashMap();
            String key = "";
            BigDecimal value = new BigDecimal(0.00);
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                key = entry.getKey().substring(0, entry.getKey().lastIndexOf("&"));
                if (entry.getValue() != null) {
                    BigDecimal value1 =new BigDecimal(String.valueOf(entry.getValue()).replace(",", ""));
                    value = value.add(value1);
                }
            }
            newMap.put(key, value.setScale(2, RoundingMode.HALF_UP).toString());
            return newMap;
        }).collect(Collectors.toList());

        List<Map<String, Object>> resultList2 = IntStream.range(0, resultList1.size())
                .mapToObj(i -> multiplyMaps(resultList1.get(i), dataList3.get(i)))
                .collect(Collectors.toList());
        for (Map<String, Object> mapFromList1 : dataList1) {
            String key = mapFromList1.keySet().iterator().next();
            String[] keyArr = key.split("&");
            String key1 = key.substring(0, key.lastIndexOf("&"));
            String value1 = String.valueOf(mapFromList1.get(key)).replace(",", "");
            for (Map<String, Object> mapFromList2 : resultList2) {
                String key2 = mapFromList2.keySet().iterator().next();
                if (key1.equals(key2)) {
                    String value2 = String.valueOf(mapFromList2.get(key2));
                    if (!value1.equals(value2)) {
                        sb.append("报表文件：").append(paramData.getFileName()).append(",");
                        sb.append("sheet名称").append(keyArr[0]).append(",");
                        sb.append("第").append(keyArr[1]).append("行");
                        sb.append("第").append(keyArr[2]).append("列");
                        sb.append(itemName).append(System.lineSeparator());
                    }
                    break;
                }
            }
        }
        return sb.toString();
    }

    public static Map<String, Object> multiplyMaps(Map<String, Object> map1, Map<String, Object> map2) {
        Map<String, Object> retMap = new HashMap<>();
        // 遍历map1的键，对应的值相乘并放入retMap中
        map2.forEach((key, value) -> {
            String key1 = key.substring(0, key.lastIndexOf("&"));
            if (map1.containsKey(key1) && value != null && map1.get(key1) != null) {
                BigDecimal value1 = new BigDecimal(String.valueOf(map1.get(key1)).replace(",", ""));
                BigDecimal value2 = new BigDecimal(String.valueOf(value).replace(",", "").replace("%", "")).divide(new BigDecimal(100),2, RoundingMode.HALF_UP) ;
                BigDecimal newValue= value1.multiply(value2).setScale(2, RoundingMode.HALF_UP);
                retMap.put(key1,newValue );
            } else {
                retMap.put(key1, "0.00");
            }
        });
        return retMap;
    }

    public String sumAndMinus(ParamData paramData, List<Map<String, Object>> dataList1, List<Map<String, Object>> dataList2, List<Map<String, Object>> dataList3, String itemName) {
        StringBuffer sb = new StringBuffer();
        List<Map<String, Object>> resultList1 = dataList2.stream().map(map -> {
            Map<String, Object> newMap = new LinkedHashMap();
            String key = "";
            BigDecimal value = new BigDecimal(0.00);
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                key = entry.getKey().substring(0, entry.getKey().lastIndexOf("&"));
                if (entry.getValue() != null) {
                    BigDecimal value1 =new BigDecimal(String.valueOf(entry.getValue()).replace(",", ""));
                    value = value.add(value1);
                }
            }
            newMap.put(key, value.setScale(2, RoundingMode.HALF_UP).toString());
            return newMap;
        }).collect(Collectors.toList());
        List<Map<String, Object>> resultList2 = IntStream.range(0, resultList1.size())
                .mapToObj(i -> minusMaps(resultList1.get(i), dataList3.get(i)))
                .collect(Collectors.toList());
        for (Map<String, Object> mapFromList1 : dataList1) {
            String key = mapFromList1.keySet().iterator().next();
            String[] keyArr = key.split("&");
            String key1 = key.substring(0, key.lastIndexOf("&"));
            String value1 = String.valueOf(mapFromList1.get(key)).replace(",", "");
            for (Map<String, Object> mapFromList2 : resultList2) {
                String key2 = mapFromList2.keySet().iterator().next();
                if (key1.equals(key2)) {
                    String value2 = String.valueOf(mapFromList2.get(key2));
                    if (!value1.equals(value2)) {
                        sb.append("报表文件：").append(paramData.getFileName()).append(",");
                        sb.append("sheet名称").append(keyArr[0]).append(",");
                        sb.append("第").append(keyArr[1]).append("行");
                        sb.append("第").append(keyArr[2]).append("列");
                        sb.append(itemName).append(System.lineSeparator());
                    }
                    break;
                }
            }
        }
        return sb.toString();
    }

    private Map<String, Object> minusMaps(Map<String, Object> map1, Map<String, Object> map2) {
        Map<String, Object> retMap = new HashMap<>();
        // 遍历map1的键，对应的值相减并放入retMap中
        map2.forEach((key, value) -> {
            String key1 = key.substring(0, key.lastIndexOf("&"));
            if (map1.containsKey(key1) && value != null && map1.get(key1) != null) {
                BigDecimal value1 = new BigDecimal(String.valueOf(map1.get(key1)).replace(",", ""));
                BigDecimal value2 = new BigDecimal(String.valueOf(value).replace(",", ""));
                BigDecimal newValue= value1.subtract(value2).setScale(2, RoundingMode.HALF_UP);
                retMap.put(key1, newValue.toString());
            } else {
                retMap.put(key1, "0.00");
            }
        });
        return retMap;
    }

    public String greater(ParamData paramData, List<Map<String, Object>> dataList1, List<Map<String, Object>> dataList2, String itemName) {
        StringBuffer sb = new StringBuffer();
        for (Map<String, Object> mapFromList1 : dataList1) {
            String key1 = mapFromList1.keySet().iterator().next();
            String[] keyArr = key1.split("&");
            String newKey1 = key1.substring(0, key1.lastIndexOf("&"));
            Double value1 = Double.parseDouble(String.valueOf(mapFromList1.get(key1)).replace(",", ""));
            for (Map<String, Object> mapFromList2 : dataList2) {
                String key2 = mapFromList2.keySet().iterator().next();
                String newkey2 = key1.substring(0, key1.lastIndexOf("&"));
                if (newKey1.equals(newkey2)) {
                    Double value2 = Double.parseDouble(String.valueOf(mapFromList2.get(key2)).replace(",", ""));
                    if (value1 <= value2) {
                        sb.append("报表文件：").append(paramData.getFileName()).append(",");
                        sb.append("sheet名称").append(keyArr[0]).append(",");
                        sb.append("第").append(keyArr[1]).append("行");
                        sb.append("第").append(keyArr[2]).append("列");
                        sb.append(itemName).append(System.lineSeparator());
                    }
                    break;
                }
            }
        }
        return sb.toString();

    }




    public String equal(ParamData paramData, List<Map<String, Object>> dataList1, List<Map<String, Object>> dataList2, String itemName) {
        StringBuffer sb = new StringBuffer();
        for (Map<String, Object> mapFromList1 : dataList1) {
            String key1 = mapFromList1.keySet().iterator().next();
            String[] keyArr = key1.split("&");
            String newKey1 = key1.substring(0, key1.lastIndexOf("&"));
            Double value1 = Double.parseDouble(String.valueOf(mapFromList1.get(key1)).replace(",", ""));
            for (Map<String, Object> mapFromList2 : dataList2) {
                String key2 = mapFromList2.keySet().iterator().next();
                String newkey2 = key1.substring(0, key1.lastIndexOf("&"));
                if (newKey1.equals(newkey2)) {
                    Double value2 = Double.parseDouble(String.valueOf(mapFromList2.get(key2)).replace(",", ""));
                    if (value1 == value2) {
                        sb.append("报表文件：").append(paramData.getFileName()).append(",");
                        sb.append("sheet名称").append(keyArr[0]).append(",");
                        sb.append("第").append(keyArr[1]).append("行");
                        sb.append("第").append(keyArr[2]).append("列");
                        sb.append(itemName).append(System.lineSeparator());
                    }
                    break;
                }
            }
        }
        return sb.toString();
    }

    public String amountMultByProportion(ParamData paramData, List<Map<String, Object>> dataList1, List<Map<String, Object>> dataList2, double proportion, String itemName) {
        List<Map<String, Object>> resultList2 = IntStream.range(0, dataList2.size())
                .mapToObj(i -> multiplyMaps(dataList2.get(i), proportion))
                .collect(Collectors.toList());
        StringBuffer sb = new StringBuffer();
        for (Map<String, Object> mapFromList1 : dataList1) {
            String key = mapFromList1.keySet().iterator().next();
            String[] keyArr = key.split("&");
            String key1 = key.substring(0, key.lastIndexOf("&"));
            String value1 = String.valueOf(mapFromList1.get(key)).replace(",", "");
            for (Map<String, Object> mapFromList2 : resultList2) {
                String key2 = mapFromList2.keySet().iterator().next();
                if (key1.equals(key2)) {
                    String value2 = String.valueOf(mapFromList2.get(key2));
                    if (!value1.equals(value2)) {
                        sb.append("报表文件：").append(paramData.getFileName()).append(",");
                        sb.append("sheet名称").append(keyArr[0]).append(",");
                        sb.append("第").append(keyArr[1]).append("行");
                        sb.append("第").append(keyArr[2]).append("列");
                        sb.append(itemName).append(System.lineSeparator());
                    }
                    break;
                }
            }
        }
        return sb.toString();
    }

    public static Map<String, Object> multiplyMaps(Map<String, Object> map1, double proportion) {
        Map<String, Object> retMap = new HashMap<>();
        // 遍历map1的键，对应的值相乘并放入retMap中
        map1.forEach((key, value) -> {
            if (value != null) {
                BigDecimal value1 = new BigDecimal(String.valueOf(value).replace(",", ""));
                BigDecimal newValue =new BigDecimal(proportion).multiply(value1).setScale(2, RoundingMode.HALF_UP);
                retMap.put(key, newValue.toString());
            } else {
                retMap.put(key, "0.00");
            }
        });
        return retMap;
    }

    public String amountMinusAndAdd(ParamData paramData, List<Map<String, Object>> dataList1, List<Map<String, Object>> dataList2, String itemName) {
        List<Map<String, Object>> resultList = dataList2.stream()
                .map(map -> {
                    Object[] values = map.values().toArray();
                    Object[] keys = map.keySet().toArray();
                    String key = String.valueOf(keys[0]);
                    key = key.substring(0, key.lastIndexOf("&"));
                    Map<String, Object> result = new LinkedHashMap<>();
                    BigDecimal value1 =new BigDecimal(0.00) ;
                    if (values[0] != null) {
                      value1 = new BigDecimal(String.valueOf(values[0]).replace(",", ""));
                    }
                    BigDecimal value2 =new BigDecimal(0.00) ;
                    if (values[1] != null) {
                         value2 = new BigDecimal(String.valueOf(values[1]).replace(",", ""));
                    }
                    BigDecimal value3 = new BigDecimal(0.00) ;
                    if (values[2] != null) {
                        value3 = new BigDecimal(String.valueOf(values[2]).replace(",", ""));
                    }
                    BigDecimal newValue = value1.subtract(value2).add(value3).setScale(2, RoundingMode.HALF_UP);
                    result.put(key, newValue.toString());
                    return result;
                })
                .collect(Collectors.toList());
        StringBuffer sb = new StringBuffer();
        for (Map<String, Object> mapFromList1 : dataList1) {
            String key = mapFromList1.keySet().iterator().next();
            String[] keyArr = key.split("&");
            String key1 = key.substring(0, key.lastIndexOf("&"));
            String value1 = String.valueOf(mapFromList1.get(key)).replace(",", "");
            for (Map<String, Object> mapFromList2 : resultList) {
                String key2 = mapFromList2.keySet().iterator().next();
                if (key1.equals(key2)) {
                    String value2 = String.valueOf(mapFromList2.get(key2));
                    if (!value1.equals(value2)) {
                        sb.append("报表文件：").append(paramData.getFileName()).append(",");
                        sb.append("sheet名称").append(keyArr[0]).append(",");
                        sb.append("第").append(keyArr[1]).append("行");
                        sb.append("第").append(keyArr[2]).append("列");
                        sb.append(itemName).append(System.lineSeparator());
                    }
                    break;
                }
            }
        }
        return sb.toString();
    }


    public String towRowAdd(ParamData paramData, List<Map<String, Object>> dataList1, List<Map<String, Object>> dataList2, List<Map<String, Object>> dataList3, String itemName) {
        List<Map<String, Object>> resultList2 = IntStream.range(0, dataList2.size())
                .mapToObj(i -> AdditionMaps(dataList2.get(i), dataList3.get(i)))
                .collect(Collectors.toList());
        StringBuffer sb = new StringBuffer();
        for (Map<String, Object> mapFromList1 : dataList1) {
            String key = mapFromList1.keySet().iterator().next();
            String[] keyArr = key.split("&");
            String key1 = keyArr[0] + "&" + keyArr[2];
            String value1 = String.valueOf(mapFromList1.get(key)).replace(",", "");
            for (Map<String, Object> mapFromList2 : resultList2) {
                String key2 = mapFromList2.keySet().iterator().next();
                if (key1.equals(key2)) {
                    String value2 = String.valueOf(mapFromList2.get(key2));
                    if (!value1.equals(value2)) {
                        sb.append("报表文件：").append(paramData.getFileName()).append(",");
                        sb.append("sheet名称").append(keyArr[0]).append(",");
                        sb.append("第").append(keyArr[1]).append("行");
                        sb.append("第").append(keyArr[2]).append("列");
                        sb.append(itemName).append(System.lineSeparator());
                    }
                    break;
                }
            }
        }
        return sb.toString();
    }

    /**
     * dataList1=dataList2+dataList3
     * @param paramData
     * @param dataList1  总含税
     * @param dataList2  总不含税
     * @param dataList3  总税额
     * @param itemName
     * @return
     */
    public String totalCalculatedAmount(ParamData paramData, List<Map<String, Object>> dataList1, List<Map<String, Object>> dataList2, List<Map<String, Object>> dataList3, String itemName) {
        List<Map<String, Object>> resultList2 = IntStream.range(0, dataList2.size())
                .mapToObj(i -> AdditionMaps(dataList2.get(i), dataList3.get(i)))
                .collect(Collectors.toList());
        StringBuffer sb = new StringBuffer();
        for (Map<String, Object> mapFromList1 : dataList1) {
            String key = mapFromList1.keySet().iterator().next();
            String[] keyArr = key.split("&");
            String key1 = keyArr[0] + "&" + keyArr[2];
            String value1 = String.valueOf(mapFromList1.get(key)).replace(",", "");
            for (Map<String, Object> mapFromList2 : resultList2) {
                String key2 = mapFromList2.keySet().iterator().next();
                if (key1.equals(key2)) {
                    String value2 = String.valueOf(mapFromList2.get(key2));
                    if (!value1.equals(value2)) {
                        sb.append("报表文件：").append(paramData.getFileName()).append(",");
                        sb.append("sheet名称").append(keyArr[0]).append(",");
                        sb.append("第").append(keyArr[1]).append("行");
                        sb.append("第").append(keyArr[2]).append("列");
                        sb.append("合计：含税金额不等于不含税金额+税额").append(System.lineSeparator());
                    }
                    break;
                }
            }
        }


        BigDecimal taxRate;
        if (!StringUtils.isEmpty(paramData.getTaxRate())) {
            taxRate =new BigDecimal(paramData.getTaxRate().replace("%", "")).divide(new BigDecimal(100),2,RoundingMode.HALF_UP).add(new BigDecimal(1)) ;
        } else {
            taxRate = new BigDecimal("1.06");
        }
        //总不含税=round(总含税/1.06,2)
        List<Map<String, Object>> resultList3 = IntStream.range(0, dataList1.size())
                .mapToObj(i -> calculationMaps(dataList1.get(i), taxRate))
                .collect(Collectors.toList());
        for (Map<String, Object> mapFromList2 : dataList2) {
            String key = mapFromList2.keySet().iterator().next();
            String[] keyArr = key.split("&");
            String key1 = keyArr[0] + "&" + keyArr[2];
            String value1 = String.valueOf(mapFromList2.get(key)).replace(",", "");
            for (Map<String, Object> mapFromList3 : resultList3) {
                String key2 = mapFromList3.keySet().iterator().next();
                if (key1.equals(key2)) {
                    String value2 = String.valueOf(mapFromList3.get(key2));
                    if (!value1.equals(value2)) {
                        sb.append("报表文件：").append(paramData.getFileName()).append(",");
                        sb.append("sheet名称").append(keyArr[0]).append(",");
                        sb.append("第").append(keyArr[1]).append("行");
                        sb.append("第").append(keyArr[2]).append("列");
                        sb.append("总不含税不等于round(总含税/1.06,2)").append(System.lineSeparator());
                    }
                    break;
                }
            }
        }

        List<Map<String, Object>> subtractMaps = IntStream.range(0, dataList2.size())
                .mapToObj(i -> subtractMaps(dataList1.get(i), dataList2.get(i)))
                .collect(Collectors.toList());
        for (Map<String, Object> mapFromList1 : dataList3) {
            String key = mapFromList1.keySet().iterator().next();
            String[] keyArr = key.split("&");
            String key1 = keyArr[0] + "&" + keyArr[2];
            String value1 = String.valueOf(mapFromList1.get(key)).replace(",", "");
            for (Map<String, Object> mapFromList3 : subtractMaps) {
                String key2 = mapFromList3.keySet().iterator().next();
                if (key1.equals(key2)) {
                    String value2 = String.valueOf(mapFromList3.get(key2));
                    if (!value1.equals(value2)) {
                        sb.append("报表文件：").append(paramData.getFileName()).append(",");
                        sb.append("sheet名称").append(keyArr[0]).append(",");
                        sb.append("第").append(keyArr[1]).append("行");
                        sb.append("第").append(keyArr[2]).append("列");
                        sb.append("合计：税额不等于含税金额-不含税金额").append(System.lineSeparator());
                    }
                    break;
                }
            }
        }
        return sb.toString();
    }


    public Map<String, Object> AdditionMaps(Map<String, Object> map1, Map<String, Object> map2) {
        Map<String, Object> retMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : map1.entrySet()) {
            String[] keyArr = entry.getKey().split("&");
            String newKey1 = keyArr[0] + "&" + keyArr[2];
            BigDecimal value1  =new BigDecimal(0.00) ;
            if (entry.getValue() != null) {
                 value1 = new BigDecimal(String.valueOf(entry.getValue()).replace(",", ""));
            }
            for (Map.Entry<String, Object> entry2 : map2.entrySet()) {
                String[] keyArr2 = entry2.getKey().split("&");
                String newKey2 = keyArr2[0] + "&" + keyArr2[2];
                BigDecimal value2 =new BigDecimal(0.00) ;
                if (entry2.getValue() != null) {
                    value2 = new BigDecimal(String.valueOf(entry2.getValue()).replace(",", ""));
                }
                if(newKey1.equals(newKey2)){
                    BigDecimal newValue= value1.add(value2).setScale(2, RoundingMode.HALF_UP);
                    retMap.put(newKey1, newValue.toString());
                    break;
                }
            }
        }
        return retMap;
    }

    public Map<String, Object> calculationMaps(Map<String, Object> map1,BigDecimal taxRate) {
        Map<String, Object> retMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : map1.entrySet()) {
            String[] keyArr = entry.getKey().split("&");
            String newKey1 = keyArr[0] + "&" + keyArr[2];
            BigDecimal value1  =new BigDecimal(0.00) ;
            if (entry.getValue() != null) {
                //总不含税=round(总含税/1.06,2)
                value1 = new BigDecimal(String.valueOf(entry.getValue()).replace(",", ""));
                BigDecimal divide = value1.divide(taxRate, 2, RoundingMode.HALF_UP);
                retMap.put(newKey1, divide.toString());
            }
        }
        return retMap;
    }

    /**
     * @param map1
     * @param map2
     * @return map1-map2
     */
    public Map<String, Object> subtractMaps(Map<String, Object> map1, Map<String, Object> map2) {
        Map<String, Object> retMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : map1.entrySet()) {
            String[] keyArr = entry.getKey().split("&");
            String newKey1 = keyArr[0] + "&" + keyArr[2];
            BigDecimal value1  =new BigDecimal(0.00) ;
            if (entry.getValue() != null) {
                value1 = new BigDecimal(String.valueOf(entry.getValue()).replace(",", ""));
            }
            for (Map.Entry<String, Object> entry2 : map2.entrySet()) {
                String[] keyArr2 = entry2.getKey().split("&");
                String newKey2 = keyArr2[0] + "&" + keyArr2[2];
                BigDecimal value2 =new BigDecimal(0.00) ;
                if (entry2.getValue() != null) {
                    value2 = new BigDecimal(String.valueOf(entry2.getValue()).replace(",", ""));
                }
                if(newKey1.equals(newKey2)){
                    BigDecimal newValue= value1.subtract(value2).setScale(2, RoundingMode.HALF_UP);
                    retMap.put(newKey1, newValue.toString());
                    break;
                }
            }
        }
        return retMap;
    }

    public String worldFileName(String fileName, String pathName) {
        String worldPathName=pathName.replace("xls","doc");
        fileName=pathName.replace("xls","doc");
        File file=new File(worldPathName);
        if (!file.exists()) {
            return fileName+"文件不存在或者文件名不正确";
        }
        return "";
    }

    /**
     稽核 金额相加
     */
    public String moneyAdd(ParamData paramData, ExcelParam excelParam){
        log.info("开始稽核moneyAdd");
        //这里写逻辑   从
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer startCol = Integer.parseInt(param1.getColumns());
        List<Map<String, Object>> excelMsgs = getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), null, startCol);

        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < excelMsgs.size(); i++) {//每一行数据
            //进行计算
            String sumTaxMoney = "0";
            String sumNoTaxMoney = "0";
            String taxMoney = "0";
            String noTaxMoney = "0";

            Map<String, Object> mapFromList1 = excelMsgs.get(i);
            for (String key : mapFromList1.keySet()) {//key = SQL Results&1&1
                String value = String.valueOf(mapFromList1.get(key)).replace(",", "");//230
                String[] keyArr = key.split("&");
                Integer line = Integer.parseInt(keyArr[2]);//
                if (line == (mapFromList1.size() + startCol - 1)) {//总计 税额 倒数第一行
                    sumTaxMoney = value;
                } else if (line == (mapFromList1.size()  + startCol - 2)) {//总计 不含税金额 倒数第二行
                    sumNoTaxMoney = value;
                } else {
                    if (line % 2 == 0) {
                        noTaxMoney = new BigDecimal(noTaxMoney).add(new BigDecimal(value)).toString();
                    } else {
                        taxMoney = new BigDecimal(taxMoney).add(new BigDecimal(value)).toString();
                    }
                }
            }
            //对比金额
            if (!taxMoney.equals(sumTaxMoney)) {
                sb.append("报表文件：").append(paramData.getFileName()).append(",");
                sb.append("第" + (i+6) + "行,税额不匹配 产品税额相加=" + taxMoney + ",总计税额=" + sumTaxMoney);
            }
            if (!noTaxMoney.equals(sumNoTaxMoney)) {
                sb.append("报表文件：").append(paramData.getFileName()).append(",");
                sb.append("第" + (i+6)+ "行,不含税金额不匹配 产品不含税金额相加=" + noTaxMoney + ",总计不含税金额=" + sumNoTaxMoney);
            }
        }
        return sb.toString();
    }

    /**
     * 自动读取配置进行金额相加稽核
     * 稽核 a+b+c+d+..=sum
     * @param paramData
     * @param excelParam
     * @return
     */
    public String moneyAddAutomatic(ParamData paramData, ExcelParam excelParam){
        //param1定义  a,b,c
        StringBuffer sb = new StringBuffer();

        ExcelParam.Param param1 = excelParam.getParam1();
        if(!CommonUtil.isNotNull(param1.getColumns())){
            return "param1的columns为空";
        }
        ExcelParam.Param param2 = excelParam.getParam2();
        if(!CommonUtil.isNotNull(param2.getColumns())){
            return "param2的columns为空";
        }
        String[] addLeft = param1.getColumns().split("&");
        String[] addRight = param2.getColumns().split("&");
        if(addLeft.length!=addRight.length){
            return "param1和param2的数量不一致";
        }
        boolean flagLog = true;
        for(int i=0;i<addLeft.length;i++){
            String leftCols = addLeft[i];
            String rightCols = addRight[i];

            Integer[] leftColumns = Arrays.stream(leftCols.split(","))
                    .map(Integer::parseInt) // 将字符串映射为整数
                    .toArray(Integer[]::new);

            Integer[] rightColumns = Arrays.stream(rightCols.split(","))
                    .map(Integer::parseInt) // 将字符串映射为整数
                    .toArray(Integer[]::new);

            List<Map<String, Object>> excelMsgLeft = getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), leftColumns, null);
            List<Map<String, Object>> excelMsgRight = getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), rightColumns, null);

            for (int j = 0; j < excelMsgLeft.size(); j++) { //每一行数据 计算出两个金额
                Map<String, Object> leftRowMsg = excelMsgLeft.get(j);
                BigDecimal leftMoney = new BigDecimal(0);
                for (String key : leftRowMsg.keySet()) { //一行多个金额
                    String value = String.valueOf(leftRowMsg.get(key)).replace(",", "");//230
                    leftMoney = leftMoney.add(new BigDecimal(value));
                }

                Map<String, Object> rightRowMsg = excelMsgRight.get(j);
                BigDecimal rightMoney = new BigDecimal(0);
                for (String key : rightRowMsg.keySet()) {  //一行多个金额
                    String value = String.valueOf(rightRowMsg.get(key)).replace(",", "");//230
                    rightMoney = rightMoney.add(new BigDecimal(value));
                }
                if(leftMoney.compareTo(rightMoney)!=0){
                    if(flagLog){
                        sb.append("报表文件：").append(paramData.getFileName()).append(":");
                        flagLog =false;
                    }
                    sb.append("第"+String.valueOf(param1.getStartRow().intValue()+j+1)+"行的"+Arrays.toString(leftColumns)+"列数相加="+leftMoney.toString()+"不等于"+Arrays.toString(rightColumns)+"列数相加"+rightMoney.toString()+";");
                }
            }
        }
       return sb.toString();
    }
    public String moneyAddAutomaticDif(ParamData paramData, ExcelParam excelParam){
        //param1定义  a,b,c
        StringBuffer sb = new StringBuffer();

        ExcelParam.Param param1 = excelParam.getParam1();
        if(!CommonUtil.isNotNull(param1.getColumns())){
            return "param1的columns为空";
        }
        ExcelParam.Param param2 = excelParam.getParam2();
        if(!CommonUtil.isNotNull(param2.getColumns())){
            return "param2的columns为空";
        }
        String[] addLeft = param1.getColumns().split("&");
        String[] addRight = param2.getColumns().split("&");
        if(addLeft.length!=addRight.length){
            return "param1和param2的数量不一致";
        }
        boolean flagLog = true;
        for(int i=0;i<addLeft.length;i++){
            String leftCols = addLeft[i];
            String rightCols = addRight[i];

            Integer[] leftColumns = Arrays.stream(leftCols.split(","))
                    .map(Integer::parseInt) // 将字符串映射为整数
                    .toArray(Integer[]::new);

            Integer[] rightColumns = Arrays.stream(rightCols.split(","))
                    .map(Integer::parseInt) // 将字符串映射为整数
                    .toArray(Integer[]::new);

            List<Map<String, Object>> excelMsgLeft = getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), leftColumns, null);
            List<Map<String, Object>> excelMsgRight = getColumnDataList2(paramData.getPathName(), param2.getStartRow(), param2.getEndRow(), rightColumns, null);

            for (int j = 0; j < excelMsgLeft.size(); j++) { //每一行数据 计算出两个金额
                Map<String, Object> leftRowMsg = excelMsgLeft.get(j);
                BigDecimal leftMoney = new BigDecimal(0);
                for (String key : leftRowMsg.keySet()) { //一行多个金额
                    String value = String.valueOf(leftRowMsg.get(key)).replace(",", "");//230
                    leftMoney = leftMoney.add(new BigDecimal(value));
                }

                Map<String, Object> rightRowMsg = excelMsgRight.get(j);
                BigDecimal rightMoney = new BigDecimal(0);
                for (String key : rightRowMsg.keySet()) {  //一行多个金额
                    String value = String.valueOf(rightRowMsg.get(key)).replace(",", "");//230
                    rightMoney = rightMoney.add(new BigDecimal(value));
                }
                //两边的数是相反数
                if(leftMoney.add(rightMoney).compareTo(BigDecimal.ZERO)!=0){
                    if(flagLog){
                        sb.append("报表文件：").append(paramData.getFileName()).append(":");
                        flagLog =false;
                    }
                    sb.append("第"+String.valueOf(param1.getStartRow().intValue()+j+1)+"行的"+Arrays.toString(leftColumns)+"列数相加="+leftMoney.toString()+"不等于"+Arrays.toString(rightColumns)+"列数相加"+rightMoney.toString()+"的相反数;");
                }
            }
        }
        return sb.toString();
    }

    public String comparePriceWord(ParamData paramData, ExcelParam excelParam){
        StringBuffer sb=new StringBuffer();

            //1.获取表格中的金额
            ExcelParam.Param param1 = excelParam.getParam1();
            Integer[] columns1 = this.getColumns(param1);
            List<Map<String, Object>> dataList = this.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
//            List<Map<String, Object>> dataList = this.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
            //获取word文档中的金额
            String pathName = paramData.getPathName();
            String worldPathName=pathName.replace("xls","doc");
            File file=new File(worldPathName);
            if (!file.exists()) {
                return sb.append(pathName).append("对应的doc文件不存在或者文件名不正确").append(System.lineSeparator()).toString();
            }
            boolean flag = true;
            BigDecimal totalWordPrice = new BigDecimal(0);
            try {
                if (4==(param1.getType())){
                    FileInputStream fis = new FileInputStream(file);
                    log.info("文件大小："+file.length());
                    XWPFDocument doc  = new XWPFDocument(OPCPackage.open(fis));
                    //提取段落
                    java.util.List<XWPFParagraph> paragraphs =  doc.getParagraphs();
                    for (XWPFParagraph paragraph: paragraphs){
                        String text = paragraph.getText();
                        if (!paramData.getFileName().contains("调账")) {
                            if (text.contains("省公司结入费用")) {
                                String[] split = text.split("省公司结入费用");
                                String cont = split[1];
                                String price = cont.split("=")[0].trim();
                                flag = false;
                                totalWordPrice = new BigDecimal(price);
                                break;
                            }
                        }else {
                            if (text.contains("省公司结入调账费用")) {
                                String[] split = text.split("省公司结入调账费用");
                                String cont = split[1];
                                String price = cont.split("=")[0].trim();
                                if (price.contains("-")){
                                    price = price.split("-")[1];
                                }
                                flag = false;
                                totalWordPrice = new BigDecimal(price);
                                break;
                            }
                        }
                    }
                    fis.close();
                }else  if (3==(param1.getType())){
                    FileInputStream fis = new FileInputStream(file);
                    log.info("文件大小："+file.length());
                    XWPFDocument doc  = new XWPFDocument(OPCPackage.open(fis));
                    //提取段落
                    java.util.List<XWPFParagraph> paragraphs =  doc.getParagraphs();
                    for (XWPFParagraph paragraph: paragraphs){
                        String text = paragraph.getText();
                        if (!paramData.getFileName().contains("调账")) {
                            if (text.contains("结算额（结入 - 结出）=")) {
                                String[] split = text.split("结算额（结入 - 结出）=");
                                String cont = split[1];
                                String substring = cont.substring( 0,cont.length() - 3);
                                String trim = substring.trim();
                                flag = false;
                                totalWordPrice = new BigDecimal(trim);
                                break;
                            }
                        }else {
                            if (text.contains("调账结算额（结入 - 结出）=")) {
                                String[] split = text.split("调账结算额（结入 - 结出）=");
                                String cont = split[1];
                                String substring = cont.substring( 0,cont.length() - 3);
                                String trim = substring.trim();
                                if (trim.contains("-")){
                                     trim = trim.split("-")[1];
                                }
                                flag = false;
                                totalWordPrice = new BigDecimal(trim);
                                break;
                            }
                        }
                    }
                    fis.close();
                }else if (1==(param1.getType())){
                    FileInputStream fis = new FileInputStream(file);
                    log.info("文件大小："+file.length());
                    XWPFDocument doc  = new XWPFDocument(OPCPackage.open(fis));
                    //提取段落
                    java.util.List<XWPFParagraph> paragraphs =  doc.getParagraphs();
                    for (XWPFParagraph paragraph: paragraphs){
                        String text = paragraph.getText();
                        if (!paramData.getFileName().contains("调账")) {
                            if (text.contains("本结算报表结算费用为：")) {
                                String[] split = text.split("本结算报表结算费用为：");
                                String cont = split[1];
//                                String substring = cont.substring( 0,cont.length() - 2);
                                String substring = cont.split("元")[0];
                                String trim = substring.trim();
                                flag = false;
                                totalWordPrice = new BigDecimal(trim);
                                break;
                            }
                        }else {
                            if (text.contains("本结算报表结算调账费用为：")) {
                                String[] split = text.split("本结算报表结算调账费用为：");
                                String cont = split[1];
                                String substring = cont.split("元")[0];
                                String trim = substring.trim();
                                if (trim.contains("-")){
                                    trim = trim.split("-")[1];
                                }
                                flag = false;
                                totalWordPrice = new BigDecimal(trim);
                                break;
                            }
                        }
                    }
                    fis.close();
                }else if (2==(param1.getType())){
                    FileInputStream fis = new FileInputStream(file);
                    log.info("文件大小："+file.length());
                    XWPFDocument doc  = new XWPFDocument(OPCPackage.open(fis));
                    //提取段落
                    java.util.List<XWPFParagraph> paragraphs =  doc.getParagraphs();
                    for (XWPFParagraph paragraph: paragraphs){
                        String text = paragraph.getText();
                        if (!paramData.getFileName().contains("调账")) {
                            if (text.contains("金额：")) {
                                String cont = text.split("金额：")[1];
                                String split1 = cont.split("元")[0].trim();
                                flag = false;
                                totalWordPrice = new BigDecimal(split1);
                                break;
                            }
                        }else {
                            if (text.contains("调账金额：")) {
                                String cont = text.split("调账金额：")[1];
                                String trim = cont.split("元")[0].trim();
                                if (trim.contains("-")){
                                    trim = trim.split("-")[1];
                                }
                                flag = false;
                                totalWordPrice = new BigDecimal(trim);
                                break;
                            }
                        }
                    }
                    fis.close();
                }
                if (flag) {
                    return   sb.append("Excel中").append(worldPathName).append("word中未获取到金额！请检查文件。").toString();
                }
                for (String s : dataList.get(0).keySet()) {
                    String replace = String.valueOf(dataList.get(0).get(s)).replace(",", "");
                    if (paramData.getFileName().contains("调账")&&replace.contains("-")){
                        replace = replace.split("-")[1];
                    }
                    BigDecimal totalExcelPrice = new BigDecimal(replace);
                    int i = totalExcelPrice.compareTo(totalWordPrice);
                    if (i!= 0) {
                        sb.append("Excel中").append(s).append("与").append(worldPathName).append("文件中金额与excel中金额不符，excel金额").append(totalExcelPrice).append("doc金额|").append(totalWordPrice);
                    }
                }
            }catch (Exception e) {
                return  sb.append("获取word文档金额异常，请检查word文件!"+worldPathName).toString();
            }
//        }
        return sb.toString();
    }


    /**
     * 获取分表中的sheet名称和总金额，与总表中的省份金额进行稽核
     * @param paramData
     * @param excelParam
     * @return
     */
    public String totalComparePrice(ParamData paramData, ExcelParam excelParam)  {
        StringBuffer sb = new StringBuffer();
        String pathName = paramData.getPathName();
        Integer sheetNums =  this.getsheetNumAll(new File(pathName));
        //SQL Results&1&1 -> 230   1行 1列  230
        Map<String, String> subProMap = new HashMap<>();
        for(int i=0;i<sheetNums;i++) {//轮询sheet
            ExcelParam.Param param1 = excelParam.getParam1();
            Integer[] columns1 = this.getColumns(param1);
            List<Map<String, Object>> taxAmounts = this.getColumnDataList(paramData.getPathName(), i, param1.getStartRow(), param1.getEndRow(), columns1);
            Map<String, Object> subtMap = taxAmounts.get(0);
            for (Map.Entry<String, Object> subEntry : subtMap.entrySet()) {
                String bigKey = subEntry.getKey();
                String key = bigKey.split("&")[0];
                String value = String.valueOf(subEntry.getValue()).replace(",","");
                subProMap.put(key, value);
            }
        }
        ExcelParam.Param param2 = excelParam.getParam2();
        List<File> fileList = new ArrayList<>();
        Path parent = Paths.get(paramData.getDirectory()).getParent().getParent();
        File file = parent.toFile();
        String itemName = param2.getItemName();
        String totaleExcelName = null;
        if (paramData.getFileName().contains("调账")){
            String[] split = itemName.split("&");
            if (split.length <= 1) {
                return sb.append("配置中没有配置调账文件信息。").toString();
            }else {
                totaleExcelName = split[1];
            }
        }else{
            String[] split = itemName.split("&");
            totaleExcelName = split[0];
        }
        if (paramData.getTaxRate()!=null){
            totaleExcelName = totaleExcelName.replace("taxRate", paramData.getTaxRate());
        }
        if (paramData.getSettleMonth()!=null){
            totaleExcelName = totaleExcelName.replace("YYYYMM", paramData.getSettleMonth());
        }
        this.searchXlsFile(file,fileList,totaleExcelName);
        if (fileList.size() != 1) {
            sb.append("报表文件：").append(totaleExcelName).append(",");
            return sb.append("文件未找到或者存在多个文件信息。").toString();
        }
        String path = fileList.get(0).getPath();
        Integer[] columns2 = this.getColumns(param2);
        List<Map<String, Object>> dataList = this.getColumnDataList2(path, param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        Map<String, String> totalMap = new HashMap<>();
        for (Map<String, Object> stringObjectMap : dataList) {
            //把stringObjectMap封装到totalmap中
            Set<Map.Entry<String, Object>> entries = stringObjectMap.entrySet();
            String province = null;
            String price = null;
            for (Map.Entry<String, Object> entry : entries) {
                String value = String.valueOf(entry.getValue());
                if (DictMap.provinceMapPlus.contains(value)) {
                    province = value;
                }else {
                    price = value.replace(",", "");
                }
            }
            totalMap.put(province, price);
        }
        for (Map.Entry<String, String> entry : subProMap.entrySet()) {
            String province = entry.getKey();
            String value1 = entry.getValue();
            // 在map2中查找相同的键
            String value2 = totalMap.get(province);
            // 检查value2是否为null，以避免NullPointerException
            if (value2 != null && value1 != null) {
                // 比较值
                if (!value1.equals(value2)) {
                    sb.append("报表文件：").append(paramData.getFileName()).append(",");
                    sb.append("sheet名称").append(province).append(",");
                    sb.append("“").append(totaleExcelName).append("”总额不相等").append(System.lineSeparator());
                }
            } else {
                sb.append("报表文件：").append(paramData.getFileName()).append(",");
                sb.append("sheet名称").append(province).append(",");
                sb.append("“").append(totaleExcelName).append("”总额为空，请检查！").append(System.lineSeparator());
            }
        }
        return sb.toString();
    }


    /**
     * 通过获取sheet的税率
     * @param paramData
     * @param excelParam
     * @return
     */
    public String totalComparePriceAndTaxRate(ParamData paramData, ExcelParam excelParam)  {
        StringBuffer sb = new StringBuffer();
        String pathName = paramData.getPathName();
        Integer sheetNums =  this.getsheetNumAll(new File(pathName));
        Map<String, String> subProMap = new ConcurrentHashMap<>();
        log.info("开始执行最耗时的报表！");
        List<CompletableFuture<Void>> futures=new ArrayList<>();
        StopWatch watch = new StopWatch();
        watch.start();
        for(int i=0;i<sheetNums;i++) {
            int finalI = i;
            CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {
                //轮询sheet
                ExcelParam.Param param1 = excelParam.getParam1();
                Integer[] columns1 = this.getColumns(param1);
                List<Map<String, Object>> taxAmounts = this.getColumnDataList(paramData.getPathName(), finalI, param1.getStartRow(), param1.getEndRow(), columns1);
                Map<String, List<String>> taxPriceHashMap = new HashMap<>();
                String province = null;
                for (Map<String, Object> taxAmount : taxAmounts) {
                    String taxRate = null;
                    String price = null;
                    for (Map.Entry<String, Object> stringObjectEntry : taxAmount.entrySet()) {
                        province = stringObjectEntry.getKey().split("&")[0];
                        String value = String.valueOf(stringObjectEntry.getValue());
                        if (value.contains("%")) {
                            taxRate = value;
                        }else {
                            price = value.replace(",", "");
                        }
                    }
                    List<String> prilst = taxPriceHashMap.get(taxRate);
                    if (CollectionUtils.isEmpty(prilst)) {
                        prilst = new ArrayList<>();
                    }
                    prilst.add(price);
                    taxPriceHashMap.put(taxRate, prilst);
                }
                for (Map.Entry<String, List<String>> stringListEntry : taxPriceHashMap.entrySet()) {
                    String key = stringListEntry.getKey();
                    List<String> value = stringListEntry.getValue();
                    BigDecimal totalAmount = value.stream()
                            .map(BigDecimal::new)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    subProMap.put(key+"&"+province,totalAmount.toString());
                }
            });
            futures.add(voidCompletableFuture);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        watch.stop();
        //5522
        log.info("耗时" + watch.getTotalTimeMillis()+"ms");
        // 创建一个Set集合，用于存储去重后的前缀
        Set<String> taxRateExcel = new HashSet<>();
        // 遍历Map中的所有键
        for (String key : subProMap.keySet()) {
            // 假设前缀和后缀由'&'分隔，提取前缀部分
            String[] parts = key.split("&");
            if (parts.length > 0) {
                String prefix = parts[0]; // 获取前缀，这里是"6%"或"7%"
                taxRateExcel.add(prefix); // 将前缀添加到Set集合中
            }
        }
        for (String taxRate : taxRateExcel) {
            ExcelParam.Param param2 = excelParam.getParam2();
            Integer type = param2.getType();
            List<File> fileList = new ArrayList<>();
            Path parent = Paths.get(paramData.getDirectory()).getParent().getParent();
            File file = parent.toFile();
            String itemName = param2.getItemName();
            String totaleExcelName = null;
            if (paramData.getFileName().contains("调账")){
                String[] split = itemName.split("&");
                if (split.length <= 1) {
                    return sb.append("配置中没有配置调账文件信息。").toString();
                }else {
                    totaleExcelName = split[1];
                }
            }else{
                String[] split = itemName.split("&");
                totaleExcelName = split[0];
            }
            totaleExcelName = totaleExcelName.replace("taxRate", taxRate).replace("YYYYMM", paramData.getSettleMonth());
            this.searchXlsFile(file,fileList,totaleExcelName);
            if (fileList.size() != 1) {
                sb.append("报表文件：").append(totaleExcelName).append(",");
                sb.append("文件未找到或者存在多个文件信息。").toString();
                continue;
            }
            String path = fileList.get(0).getPath();
            Integer[] columns2 = this.getColumns(param2);
            List<Map<String, Object>> dataList = this.getColumnDataList2(path, param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
            Map<String, String> totalMap = new HashMap<>();
            for (Map<String, Object> stringObjectMap : dataList) {
                //把stringObjectMap封装到totalmap中
                Set<Map.Entry<String, Object>> entries = stringObjectMap.entrySet();
                String province = null;
                String price = null;
                for (Map.Entry<String, Object> entry : entries) {
                    String value = String.valueOf(entry.getValue());
                    if (DictMap.provinceMap.contains(value)) {
                        province = value;
                    }else {
                        price = value.replace(",", "");
                    }
                }
                if (province != null){
                    totalMap.put(province, price);
                }
            }
            for (String key : subProMap.keySet()) {
                if (key.contains(taxRate)){
                    String subProvince = key.split("&")[1];
                    if (DictMap.provinceMap.contains(subProvince)){
                        String subAmount = subProMap.get(key);
                        String totalAmount = totalMap.get(subProvince);
                        int i = 0;
                        if ( 1== type) {
                             i = new BigDecimal(subAmount).subtract(new BigDecimal(totalAmount)).compareTo(new BigDecimal(0));
                        } else if (2 ==type) {
                             i = new BigDecimal(subAmount).add(new BigDecimal(totalAmount)).compareTo(new BigDecimal(0));
                        }
                        if (i != 0) {
                            sb.append("报表文件：").append(paramData.getFileName()).append(",");
                            sb.append("sheet名称").append(subProvince).append(",");
                            sb.append("“").append(totaleExcelName).append(taxRate).append("”总额根据税率去区分不相等").append(subAmount).append(":").append(totalAmount).append(System.lineSeparator());
                        }
                    }
                }
            }
        }
        return sb.toString();
    }


    /**
     * 配置在总表上，获取总表的配置和分表的金额，累加分表的总金额进行比较
     * @param paramData
     * @param excelParam
     * @return
     */
    public String totalCompareToMorePrice(ParamData paramData, ExcelParam excelParam)  {
        StringBuilder tipsInfo=new StringBuilder();
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = this.getColumns(param1);
        List<Map<String, Object>> taxAmounts = this.getColumnDataList(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1);
        ExcelParam.Param param2 = excelParam.getParam2();
        Path parent = Paths.get(paramData.getDirectory()).getParent();
        File file = parent.toFile();
        String itemName = param2.getItemName();
        String totaleExcelName = null;
        if (paramData.getFileName().contains("调账")){
            String[] split = itemName.split("&");
            if (split.length <= 1) {
                return tipsInfo.append("配置中没有配置调账文件信息。").toString();
            }else {
                totaleExcelName = split[1];
            }
        }else{
            String[] split = itemName.split("&");
            totaleExcelName = split[0];
        }
        totaleExcelName = totaleExcelName.replace("taxRate", paramData.getTaxRate()).replace("YYYYMM", paramData.getSettleMonth());
        BigDecimal subTotalAmount = new BigDecimal(0);
        String subFileNames = null;
        if (totaleExcelName.contains("province")){
            String oldFileName = totaleExcelName;
            for (String province : DictMap.provinceMap) {
                totaleExcelName=oldFileName.replace("province", province);
                List<File> fileList = new ArrayList<>();
                this.searchXlsFile(file,fileList,totaleExcelName);
                if (fileList.isEmpty()) {
                    continue;
                }
                if (fileList.size() > 1) {
                    tipsInfo.append("报表文件：").append(totaleExcelName).append(",");
                    return tipsInfo.append("文件未找到或者存在多个文件信息。").toString();
                }
                subFileNames = subFileNames+totaleExcelName + ",";
                Integer[] columns2 = this.getColumns(param2);
                List<Map<String, Object>> dataList = this.getColumnDataList2(fileList.get(0).getPath(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
                String totalStr = dataList.get(0).keySet().stream().map(o -> {
                    return String.valueOf(dataList.get(0).get(o)).replace(",", "");
                }).findFirst().get();
                subTotalAmount = subTotalAmount.add(new BigDecimal(totalStr));
            }
        }else {
            String[] fileNames = totaleExcelName.split(",");
            for (String fileName : fileNames) {
                List<File> fileList = new ArrayList<>();
                this.searchXlsFile(file,fileList,fileName);
                if (fileList.isEmpty()) {
                    continue;
                }
                if (fileList.size() > 1) {
                    tipsInfo.append("报表文件：").append(totaleExcelName).append(",");
                    return tipsInfo.append("文件未找到或者存在多个文件信息。").toString();
                }
                subFileNames = subFileNames+fileName + ",";
                Integer[] columns2 = this.getColumns(param2);
                List<Map<String, Object>> dataList = this.getColumnDataList2(fileList.get(0).getPath(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
                String totalStr = dataList.get(0).keySet().stream().map(o -> {
                    return String.valueOf(dataList.get(0).get(o)).replace(",", "");
                }).findFirst().get();
                subTotalAmount = subTotalAmount.add(new BigDecimal(totalStr));
            }
        }
        for (Map<String, Object> mapFromList1 : taxAmounts) {
            String key = mapFromList1.keySet().iterator().next();
            String[] keyArr = key.split("&");
            String value1 = String.valueOf(mapFromList1.get(key)).replace(",", "");
            if(StringUtils.isEmpty(value1)){
                tipsInfo.append("报表文件：").append(paramData.getFileName()).append(",");
                tipsInfo.append("sheet名称").append(keyArr[0]).append(",");
                tipsInfo.append("第").append(keyArr[1]).append("行");
                tipsInfo.append("第").append(keyArr[2]).append("列");
                tipsInfo.append("值为空，请检查配置或者报表！").append(System.lineSeparator());
                break;
            }else{
                BigDecimal totalDecimal = new BigDecimal(value1);
                if (totalDecimal.compareTo(subTotalAmount) != 0) {
                    tipsInfo.append("报表文件：").append(paramData.getFileName()).append(",");
                    tipsInfo.append("sheet名称").append(keyArr[0]).append(",");
                    tipsInfo.append("第").append(keyArr[1]).append("行");
                    tipsInfo.append("第").append(keyArr[2]).append("列");
                    tipsInfo.append("合计不等于分表[").append(subFileNames).append("]总金额：").append(totalDecimal).append(":").append(subTotalAmount).append(System.lineSeparator());
                }
                break;
            }

        }
        return tipsInfo.toString();
    }


    public String compareTwoExcelColumnPrice(ParamData paramData, ExcelParam excelParam)  {
        StringBuilder tipsInfo=new StringBuilder();
        ExcelParam.Param param1 = excelParam.getParam1();
        Integer[] columns1 = this.getColumns(param1);
        List<Map<String, Object>> provinceAmounts = this.getColumnDataList2(paramData.getPathName(), param1.getStartRow(), param1.getEndRow(), columns1,param1.getStartColumn());
        Map<String, String> subMap = new HashMap<>();
        for (Map<String, Object> stringObjectMap : provinceAmounts) {
            //把stringObjectMap封装到totalmap中
            Set<Map.Entry<String, Object>> entries = stringObjectMap.entrySet();
            String province = null;
            String price = null;
            for (Map.Entry<String, Object> entry : entries) {
                String value = String.valueOf(entry.getValue());
                if (DictMap.provinceMapAll.contains(value)) {
                    province = value;
                }else {
                    price = value.replace(",", "");
                }
            }
            if (province != null){
                subMap.put(province, price);
            }
        }
        ExcelParam.Param param2 = excelParam.getParam2();
        Path parent = Paths.get(paramData.getDirectory()).getParent().getParent();
        File file = parent.toFile();
        String itemName = param2.getItemName();
        String totaleExcelName = null;
        if (paramData.getFileName().contains("调账")){
            String[] split = itemName.split("&");
            if (split.length <= 1) {
                return tipsInfo.append("配置中没有配置调账文件信息。").toString();
            }else {
                totaleExcelName = split[1];
            }
        }else{
            String[] split = itemName.split("&");
            totaleExcelName = split[0];
        }
        totaleExcelName = totaleExcelName.replace("taxRate", paramData.getTaxRate()).replace("YYYYMM", paramData.getSettleMonth());
        List<File> fileList = new ArrayList<>();
        this.searchXlsFile(file,fileList,totaleExcelName);
        if (fileList.isEmpty()) {
            return  tipsInfo.append("未找到报表文件：").append(totaleExcelName).append("。").toString();
        }
        if (fileList.size() > 1) {
            tipsInfo.append("报表文件：").append(totaleExcelName).append(",");
            return tipsInfo.append("文件未找到或者存在多个文件信息。").toString();
        }
        Integer[] columns2 = this.getColumns(param2);
        List<Map<String, Object>> dataList = this.getColumnDataList2(fileList.get(0).getPath(), param2.getStartRow(), param2.getEndRow(), columns2,param2.getStartColumn());
        Map<String, String> totalMap = new HashMap<>();
        for (Map<String, Object> totalPriceMap : dataList) {
            //把stringObjectMap封装到totalmap中
            Set<Map.Entry<String, Object>> entries = totalPriceMap.entrySet();
            String province = null;
            String price = null;
            for (Map.Entry<String, Object> entry : entries) {
                String value = String.valueOf(entry.getValue());
                if (DictMap.provinceMapAll.contains(value)) {
                    province = value;
                }else {
                    price = value.replace(",", "");
                }
            }
            if (province != null){
                totalMap.put(province, price);
            }
        }
        if (subMap.size() != totalMap.size()) {
            tipsInfo.append("报表文件：").append(paramData.getFileName()).append(",");
            tipsInfo.append("sheet名称").append(itemName.split("&")[0]).append(",");
            tipsInfo.append("数量分别为：").append(subMap.size()).append(":").append(totalMap.size());
            tipsInfo.append("分表与[").append(totaleExcelName).append("]省份数量不符").append(System.lineSeparator());
            return tipsInfo.toString();
        }
        for (String key : subMap.keySet()) {
            String subValue = subMap.get(key);
            String totalValue = totalMap.get(key);
            if (new BigDecimal(totalValue).compareTo(new BigDecimal(subValue)) !=0){
                tipsInfo.append("报表文件：").append(paramData.getFileName()).append(",");
                tipsInfo.append("sheet名称").append(itemName.split("&")[0]).append(",");
                tipsInfo.append("省份：").append(key).append(",");
                tipsInfo.append("分表与总表[").append(totaleExcelName).append("]金额不符").append(System.lineSeparator());
            }
        }
        return tipsInfo.toString();
    }


    public String compareTwoExcelProvinceTotalPrice(ParamData paramData, ExcelParam excelParam)  {
        StringBuffer tipsInfo = new StringBuffer();
        String pathName = paramData.getPathName();
        Integer sheetNums =  this.getsheetNumAll(new File(pathName));
        //SQL Results&1&1 -> 230   1行 1列  230
        Map<String, String> totalMap = new HashMap<>();
        for(int i=0;i<sheetNums;i++) {//轮询sheet
            ExcelParam.Param param1 = excelParam.getParam1();
            Integer[] columns1 = this.getColumns(param1);
            List<Map<String, Object>> taxAmounts = this.getColumnDataList(paramData.getPathName(), i, param1.getStartRow(), param1.getEndRow(), columns1);
            Map<String, Object> subtMap = taxAmounts.get(0);
            for (Map.Entry<String, Object> subEntry : subtMap.entrySet()) {
                String bigKey = subEntry.getKey();
                String key = bigKey.split("&")[0];
                String value = String.valueOf(subEntry.getValue()).replace(",","");
                totalMap.put(key, value);
            }
        }
        ExcelParam.Param param2 = excelParam.getParam2();
        Path parent = Paths.get(paramData.getDirectory()).getParent().getParent();
        File file = parent.toFile();
        String itemName = param2.getItemName();
        String totaleExcelName = null;
        if (paramData.getFileName().contains("调账")){
            String[] split = itemName.split("&");
            if (split.length <= 1) {
                return tipsInfo.append("配置中没有配置调账文件信息。").toString();
            }else {
                totaleExcelName = split[1];
            }
        }else{
            String[] split = itemName.split("&");
            totaleExcelName = split[0];
        }
        totaleExcelName = totaleExcelName.replace("taxRate", paramData.getTaxRate()).replace("YYYYMM", paramData.getSettleMonth());
        String[] fileNames = totaleExcelName.split(",");
        Map<String, String> subTotalMap = new HashMap<>();
        for (int i = 0; i < fileNames.length; i++) {
            String fileName = fileNames[i];
             List<File> fileList = new ArrayList<>();
            this.searchXlsFile(file,fileList,fileName);
            if (fileList.isEmpty()) {
                continue;
            }
            if (fileList.size() > 1) {
                tipsInfo.append("报表文件：").append(fileName).append(",");
                return tipsInfo.append("文件存在多个文件信息。").toString();
            }
            Integer[] columns2 = this.getColumns(param2);
            Integer columns = columns2[i];
            Integer[] numbers = { 1, columns};
            List<Map<String, Object>> dataList = this.getColumnDataList2(fileList.get(0).getPath(), param2.getStartRow(), param2.getEndRow(), numbers,param2.getStartColumn());
            Map<String, String> subMap = new HashMap<>();
            for (Map<String, Object> totalPriceMap : dataList) {
                Set<Map.Entry<String, Object>> entries = totalPriceMap.entrySet();
                String province = null;
                String price = null;
                for (Map.Entry<String, Object> entry : entries) {
                    String value = String.valueOf(entry.getValue());
                    if (DictMap.provinceMapAll.contains(value)) {
                        province = value;
                    }else {
                        price = value.replace(",", "");
                    }
                }
                if (province != null){
                    subMap.put(province, price);
                }
            }
            for (Map.Entry<String, String> stringStringEntry : subMap.entrySet()) {
                String key = stringStringEntry.getKey();
                String value = stringStringEntry.getValue();
                String totalPrice = subTotalMap.get(key);
                if (totalPrice != null) {
                    String addPrice = new BigDecimal(totalPrice).add(new BigDecimal(value)).toString();
                    subTotalMap.put(key, addPrice);
                }else{
                    subTotalMap.put(key, value);
                }
            }
        }
        for (String key : totalMap.keySet()) {
            String subValue = subTotalMap.get(key);
            String totalValue = totalMap.get(key);
            if (StringUtils.isEmpty(subValue)|| StringUtils.isEmpty(totalValue)){
                tipsInfo.append("报表文件：").append(paramData.getFileName()).append(",");
                tipsInfo.append("sheet名称").append(key).append(",");
                tipsInfo.append("总表与分表的金额为空！总表金额为").append(totalValue).append("分表金额为：").append(subValue).append(System.lineSeparator());
            }else{
                if (new BigDecimal(totalValue).compareTo(new BigDecimal(subValue)) !=0){
                    tipsInfo.append("报表文件：").append(paramData.getFileName()).append(",");
                    tipsInfo.append("sheet名称").append(key).append(",");
                    tipsInfo.append("总表与分表的总合计不含税额不等").append(System.lineSeparator());
                }
            }

        }
        return tipsInfo.toString();
    }

    public Integer getsheetNumAll(File file){
        int numberOfSheets = 1;
        try{
            FileInputStream fis = new FileInputStream(file);
            Workbook workbook = WorkbookFactory.create(fis);
            //sheet的数量
            numberOfSheets = workbook.getNumberOfSheets();
        }catch (Exception e){
            log.info("sheet数量异常");
        }
        return numberOfSheets;
    }

    public void searchXlsFile(File directory,List<File> pdfFiles,String xlsName) {
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile()) {
                        if (file.isFile() && file.getName().toLowerCase().endsWith(".xls") && file.getName().equals(xlsName)) {
                            pdfFiles.add(file);
                        }
                    } else if (file.isDirectory()) {
                        searchXlsFile(file,pdfFiles,xlsName);
                    }
                }
            }
        }
    }
}
