package com.bboss.report.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 模板变量定义模型
 * 用于定义邮件模板中的可替换变量
 * 
 * <AUTHOR>
 * @create 2025/7/26
 * @since 1.0.0
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class TemplateVariable {
    
    /**
     * 变量类型枚举
     */
    public enum VariableType {
        TEXT("文本"),
        DATE("日期"),
        DATETIME("日期时间"),
        EMAIL("邮箱");
        
        private final String displayName;
        
        VariableType(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        @Override
        public String toString() {
            return displayName;
        }
    }
    
    // 变量名称（用于模板中的占位符）
    private String name;
    
    // 变量显示标签
    private String label;
    
    // 变量类型
    private VariableType type;
    
    // 默认值
    private String defaultValue;
    
    // 是否必填
    private boolean required;
    
    // 变量描述
    private String description;
    private boolean valid;
    
    /**
     * 默认构造函数
     */
    public TemplateVariable() {
        this.type = VariableType.TEXT;
        this.required = false;
    }
    
    /**
     * 带参数的构造函数
     */
    public TemplateVariable(String name, String label, VariableType type) {
        this();
        this.name = name;
        this.label = label;
        this.type = type;
    }
    
    /**
     * 完整构造函数
     */
    public TemplateVariable(String name, String label, VariableType type, 
                           String defaultValue, boolean required, String description) {
        this.name = name;
        this.label = label;
        this.type = type;
        this.defaultValue = defaultValue;
        this.required = required;
        this.description = description;
    }
    
    // Getter 和 Setter 方法
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getLabel() {
        return label;
    }
    
    public void setLabel(String label) {
        this.label = label;
    }
    
    public VariableType getType() {
        return type;
    }
    
    public void setType(VariableType type) {
        this.type = type;
    }
    
    public String getDefaultValue() {
        return defaultValue;
    }
    
    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }
    
    public boolean isRequired() {
        return required;
    }
    
    public void setRequired(boolean required) {
        this.required = required;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    /**
     * 获取变量的占位符格式
     * @return 占位符字符串，如 {{variable_name}}
     */
    public String getPlaceholder() {
        return "{{" + name + "}}";
    }
    
    /**
     * 验证变量定义是否有效
     */
    public boolean isValid() {
        return name != null && !name.trim().isEmpty() &&
               label != null && !label.trim().isEmpty() &&
               type != null;
    }
    
    /**
     * 验证变量名称格式
     * 变量名只能包含字母、数字和下划线，且不能以数字开头
     */
    public static boolean isValidVariableName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        return name.matches("^[a-zA-Z_][a-zA-Z0-9_]*$");
    }
    
    /**
     * 验证输入值是否符合变量类型要求
     */
    public boolean validateValue(String value) {
        if (required && (value == null || value.trim().isEmpty())) {
            return false;
        }
        
        if (value == null || value.trim().isEmpty()) {
            return true; // 非必填字段可以为空
        }
        
        switch (type) {
            case TEXT:
                return true; // 文本类型不需要特殊验证
            case EMAIL:
                return value.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
            case DATE:
                // 支持年月格式 (yyyy-MM),(yyyyMM) 和完整日期格式 (yyyy-MM-dd)
                return value.matches("^\\d{4}-\\d{2}$") ||
                        value.matches("^\\d{6}$")
                        || value.matches("^\\d{4}-\\d{2}-\\d{2}$");
            default:
                return true;
        }
    }
    
    /**
     * 获取变量类型的验证提示信息
     */
    public String getValidationHint() {
        switch (type) {
            case EMAIL:
                return "请输入有效的邮箱地址";
            case DATE:
                return "请选择日期";
            case DATETIME:
                return "请选择日期/填入时间";
            case TEXT:
            default:
                return "请输入文本内容";
        }
    }
    
    @Override
    public String toString() {
        return label + " (" + name + ")";
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        TemplateVariable variable = (TemplateVariable) obj;
        return name != null && name.equals(variable.name);
    }
    
    @Override
    public int hashCode() {
        return name != null ? name.hashCode() : 0;
    }
}
