package com.bboss.report.model;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ConfigData {

private String rKey;    //R_KEY
private String reportName;    // 报表名称
private String directory;    // 目录
private String fileName;    // 文件名称
private String auditEntry;    // 稽核项
private String taxRate;    // 费率参数
private String signSubject;    // 签约主体
private String parameter;    // 表格读取参数
}
