package com.bboss.report.model;

/**
 * 邮件发送配置类
 * 用于存储SMTP服务器配置信息
 * 
 * <AUTHOR>
 * @create 2025/6/30
 * @since 1.0.0
 */
public class EmailSettings {
    
    // SMTP服务器地址
    private String smtpHost;
    
    // SMTP端口号
    private int smtpPort;
    
    // 发件人邮箱地址
    private String senderEmail;
    
    // 发件人邮箱密码/授权码
    private String senderPassword;
    
    // 是否启用SSL/TLS加密
    private boolean enableSSL;
    
    // 发件人显示名称
    private String senderName;

    // 目录附件处理模式：true=压缩模式，false=展开模式
    private boolean compressDirectories;

    // 最大附件大小限制（MB）
    private int maxAttachmentSizeMB;

    // 单个文件大小限制（MB）
    private int maxIndividualFileSizeMB;

    /**
     * 默认构造函数，设置常用的默认值
     */
    public EmailSettings() {
        this.smtpHost = "smtp.139.com";
        this.smtpPort = 465;
        this.enableSSL = true;
        this.senderName = "";
        this.compressDirectories = true; // 默认使用压缩模式
        this.maxAttachmentSizeMB = 25; // 默认25MB限制
        this.maxIndividualFileSizeMB = 10; // 默认10MB单文件限制
    }
    
    /**
     * 带参数的构造函数
     */
    public EmailSettings(String smtpHost, int smtpPort, String senderEmail,
                         String senderPassword, boolean enableSSL, String senderName) {
        this.smtpHost = smtpHost;
        this.smtpPort = smtpPort;
        this.senderEmail = senderEmail;
        this.senderPassword = senderPassword;
        this.enableSSL = enableSSL;
        this.senderName = senderName;
        this.compressDirectories = true;
        this.maxAttachmentSizeMB = 25;
    }
    
    // Getter 和 Setter 方法
    public String getSmtpHost() {
        return smtpHost;
    }
    
    public void setSmtpHost(String smtpHost) {
        this.smtpHost = smtpHost;
    }
    
    public int getSmtpPort() {
        return smtpPort;
    }
    
    public void setSmtpPort(int smtpPort) {
        this.smtpPort = smtpPort;
    }
    
    public String getSenderEmail() {
        return senderEmail;
    }
    
    public void setSenderEmail(String senderEmail) {
        this.senderEmail = senderEmail;
    }
    
    public String getSenderPassword() {
        return senderPassword;
    }
    
    public void setSenderPassword(String senderPassword) {
        this.senderPassword = senderPassword;
    }
    
    public boolean isEnableSSL() {
        return enableSSL;
    }
    
    public void setEnableSSL(boolean enableSSL) {
        this.enableSSL = enableSSL;
    }
    
    public String getSenderName() {
        return senderName;
    }
    
    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public boolean isCompressDirectories() {
        return compressDirectories;
    }

    public void setCompressDirectories(boolean compressDirectories) {
        this.compressDirectories = compressDirectories;
    }

    public int getMaxAttachmentSizeMB() {
        return maxAttachmentSizeMB;
    }

    public void setMaxAttachmentSizeMB(int maxAttachmentSizeMB) {
        this.maxAttachmentSizeMB = maxAttachmentSizeMB;
    }

    public int getMaxIndividualFileSizeMB() {
        return maxIndividualFileSizeMB;
    }

    public void setMaxIndividualFileSizeMB(int maxIndividualFileSizeMB) {
        this.maxIndividualFileSizeMB = maxIndividualFileSizeMB;
    }

    /**
     * 验证配置是否有效
     * @return 如果配置有效返回true，否则返回false
     */
    public boolean isValid() {
        return smtpHost != null && !smtpHost.trim().isEmpty() &&
               smtpPort > 0 && smtpPort <= 65535 &&
               senderEmail != null && !senderEmail.trim().isEmpty() &&
               senderPassword != null && !senderPassword.trim().isEmpty();
    }
    
    /**
     * 获取配置的字符串表示
     */
    @Override
    public String toString() {
        return "EmailSettings{" +
                "smtpHost='" + smtpHost + '\'' +
                ", smtpPort=" + smtpPort +
                ", senderEmail='" + senderEmail + '\'' +
                ", enableSSL=" + enableSSL +
                ", senderName='" + senderName + '\'' +
                '}';
    }
}
