package com.bboss.report.model;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 收件人信息模型
 * 包含邮箱地址、显示名称、标签、使用频次等信息
 * 
 * <AUTHOR>
 * @create 2025/7/1
 * @since 1.0.0
 */
public class RecipientInfo {
    
    private String email;           // 邮箱地址
    private String displayName;     // 显示名称
    private List<String> tags;      // 标签列表
    private int useCount;           // 使用频次
    private LocalDateTime lastUsed; // 最后使用时间
    private String type;            // 类型：TO（收件人）、CC（抄送）、BCC（密送）
    
    public RecipientInfo() {
        this.tags = new ArrayList<>();
        this.useCount = 0;
        this.lastUsed = LocalDateTime.now();
        this.type = "TO";
    }
    
    public RecipientInfo(String email) {
        this();
        this.email = email;
        this.displayName = email; // 默认显示名称为邮箱地址
    }
    
    public RecipientInfo(String email, String displayName) {
        this(email);
        this.displayName = displayName;
    }
    
    // Getter和Setter方法
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
    
    public List<String> getTags() {
        return tags;
    }
    
    public void setTags(List<String> tags) {
        this.tags = tags != null ? tags : new ArrayList<>();
    }
    
    public void addTag(String tag) {
        if (tag != null && !tag.trim().isEmpty() && !this.tags.contains(tag.trim())) {
            this.tags.add(tag.trim());
        }
    }
    
    public void removeTag(String tag) {
        this.tags.remove(tag);
    }
    
    public boolean hasTag(String tag) {
        return this.tags.contains(tag);
    }
    
    public int getUseCount() {
        return useCount;
    }
    
    public void setUseCount(int useCount) {
        this.useCount = useCount;
    }
    
    public void incrementUseCount() {
        this.useCount++;
        this.lastUsed = LocalDateTime.now();
    }
    
    public LocalDateTime getLastUsed() {
        return lastUsed;
    }
    
    public void setLastUsed(LocalDateTime lastUsed) {
        this.lastUsed = lastUsed;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    /**
     * 获取格式化的最后使用时间
     */
    public String getFormattedLastUsed() {
        if (lastUsed == null) {
            return "";
        }
        return lastUsed.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
    }
    
    /**
     * 获取标签的字符串表示（用逗号分隔）
     */
    public String getTagsAsString() {
        try {
            if (tags == null || tags.isEmpty()) {
                return "";
            }
            return String.join(", ", tags);
        } catch (Exception e) {
            return "";
        }
    }
    
    /**
     * 从字符串设置标签（用逗号分隔）
     */
    public void setTagsFromString(String tagsString) {
        this.tags.clear();
        if (tagsString != null && !tagsString.trim().isEmpty()) {
            String[] tagArray = tagsString.split(",");
            for (String tag : tagArray) {
                addTag(tag.trim());
            }
        }
    }
    
    /**
     * 获取显示文本（用于ComboBox显示）
     */
    public String getDisplayText() {
        try {
            StringBuilder sb = new StringBuilder();

            // 安全地添加显示名称
            String safeName = (displayName != null && !displayName.trim().isEmpty()) ? displayName : email;
            if (safeName != null) {
                sb.append(safeName);
            }

            // 安全地添加邮箱地址
            if (email != null && !email.equals(safeName)) {
                sb.append(" <").append(email).append(">");
            }

            // 安全地添加标签
            if (tags != null && !tags.isEmpty()) {
                String tagsString = getTagsAsString();
                if (tagsString != null && !tagsString.trim().isEmpty()) {
                    sb.append(" [").append(tagsString).append("]");
                }
            }

            return sb.toString();
        } catch (Exception e) {
            // 如果出现任何错误，返回邮箱地址作为备用
            return email != null ? email : "未知邮箱";
        }
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RecipientInfo that = (RecipientInfo) o;
        return Objects.equals(email, that.email);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(email);
    }
    
    @Override
    public String toString() {
        return "RecipientInfo{" +
                "email='" + email + '\'' +
                ", displayName='" + displayName + '\'' +
                ", tags=" + tags +
                ", useCount=" + useCount +
                ", lastUsed=" + lastUsed +
                ", type='" + type + '\'' +
                '}';
    }
}
