package com.bboss.report.model;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AuditEntryParam {
    private ExcelParam keepTwoDecimals;
    private ExcelParam endSign;
    private ExcelParam settleMonth;
    private ExcelParam inTaxAmount;
    private ExcelParam inNoIncludeTaxAmount;
    private ExcelParam inIncludeTaxAmount;
    private ExcelParam outTaxAmount;
    private ExcelParam outNoIncludeTaxAmount;
    private ExcelParam outIncludeTaxAmount;
    private ExcelParam taxRate;
    private ExcelParam productNotNull;
    private ExcelParam chargesNotNull;
    private ExcelParam sumColumn;
    private ExcelParam settleIncludeTaxAmount;
    private ExcelParam paidInSettleMonth;
    private ExcelParam billingPeriodNotNull;
    private ExcelParam settleOtherProvNotNull;
    private ExcelParam signSubject;
    private ExcelParam provNotNull;
    private ExcelParam settleItem;
    private ExcelParam taxAmount;
    private ExcelParam noIncludeTaxAmount;
    private ExcelParam includeTaxAmount;
    private ExcelParam includeTaxAmountAdd;
    private ExcelParam totalCharge;
    private ExcelParam totalPay;
    private ExcelParam custAndSettleAmount;
    private ExcelParam aggregateIncomeCommunicationFunctionFee;
    private ExcelParam aggregate;
    private ExcelParam groupCustomersNotNull;
    private ExcelParam settleTotalMinusLiaoningProvince;
    private ExcelParam settleIncludeTaxAmountMarket;
    private ExcelParam provFinalAmnountEqualTo31ProSumAmountsOppositeNum;
    private ExcelParam totalNotZero;
    private ExcelParam notNull;
    private ExcelParam companyNameNotNull;
    private ExcelParam companyNumNotNull;
    private ExcelParam productNumNotNull;
    private ExcelParam tariffNameNotNull;
    private ExcelParam tariffNumNotNull;
    private ExcelParam appraisalReservedAmount;
    private ExcelParam actualAmountPaid;
    private ExcelParam includeTaxAmountAdd2;
    private ExcelParam groupCustomersProvinceNotNull;
    private ExcelParam producTypeNumNotNll;
    private ExcelParam cooperativeManufacturerNotNull;
    private ExcelParam taxRateIsSix;
    private ExcelParam noIncludeTaxAmountGreaterTax;
    private ExcelParam settleAmountEqualIncludeTaxAmount;
    private ExcelParam amountMultByProportion;
    private ExcelParam amountMinusAndAdd ;
    private ExcelParam totalChargeAddTotalPay;
    private ExcelParam worldFileName;
    private ExcelParam totalChargeTSE;
    private ExcelParam totalPayTSE;
    private ExcelParam endSignPlus;
    private ExcelParam misMatchInOutcludeTaxAmount;
    private ExcelParam misMatchInIncludeTaxAmount;
    private ExcelParam misMatchInOutTaxAmount;
    private ExcelParam misMatchOutIncludeTaxAmount;
    private ExcelParam minusTaxAmountMarket;
    private ExcelParam groupCustomerNotNull;
    private ExcelParam customerCodeNotNull;
    private ExcelParam productNameNotNull;
    private ExcelParam manufacturerCodeNotNull;
    private ExcelParam subSumColumnJSZJ;
    private ExcelParam matchSubSumJSZJ;
    private ExcelParam isEmptyJSZJ;
    private ExcelParam matchHeader;
    private ExcelParam matchDoubleHeader;
    private ExcelParam moneyAdd;
    private ExcelParam moneyAddAutomatic;
    private ExcelParam moneyAddAutomaticDif;
    private ExcelParam outTaxSkuAmount;
    private ExcelParam totalCalculatedAmount;
    private ExcelParam matchSheetHeader;
    private ExcelParam matchHeaderPlus;
    private ExcelParam matchSheet;
    private ExcelParam interSectionNotNull;
    private ExcelParam comparePriceWord;
    private ExcelParam totalComparePrice;
    private ExcelParam totalComparePriceAndTaxRate;
    private ExcelParam totalCompareToMorePrice;
    private ExcelParam compareTwoExcelColumnPrice;
    private ExcelParam compareTwoExcelProvinceTotalPrice;
    private ExcelParam keepTwoDecimalsNotNull;
}
