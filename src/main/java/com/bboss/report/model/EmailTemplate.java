package com.bboss.report.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 邮件模板数据模型
 * 用于存储邮件模板配置信息，包括收件人、主题、正文和附件过滤条件
 *
 * <AUTHOR>
 * @create 2025/7/2
 * @since 1.0.0
 */
public class EmailTemplate {

    // 模板ID（数据库主键）
    private Long id;

    // 模板名称
    private String name;

    // 收件人列表（分号分隔）
    private String recipients;

    // 抄送人列表（分号分隔）
    private String ccRecipients;

    // 邮件主题
    private String subject;

    // 邮件正文
    private String body;

    // 文件名前缀过滤条件（逗号分隔）
    private String filePrefixes;

    // 文件扩展名过滤条件（逗号分隔）
    private String fileExtensions;

    // 排除文件的正则表达式模式
    private String excludeFilePattern;

    // 模板描述
    private String description;

    // 模板变量定义（JSON格式存储）
    private String variables;

    // 创建时间
    private LocalDateTime createdTime;

    // 最后修改时间
    private LocalDateTime lastModified;

    // 使用次数
    private int useCount;

    // 最后使用时间
    private LocalDateTime lastUsed;

    // 是否启用
    private boolean enabled;

    // 是否启用文件压缩（全部文件）
    private boolean enableCompression;

    // 压缩文件的解压密码（可选，为空表示无密码）
    private String compressionPassword;

    // 是否启用单文件压缩
    private boolean singleFileCompressionEnabled;

    // 需要压缩的目标文件名（支持通配符或正则表达式）
    private String targetFileName;

    // 单文件压缩密码（可选，为空表示无密码）
    private String singleFileCompressionPassword;

    /**
     * 默认构造函数
     */
    public EmailTemplate() {
        this.createdTime = LocalDateTime.now();
        this.lastModified = LocalDateTime.now();
        this.useCount = 0;
        this.enabled = true;
        this.enableCompression = false;
        this.compressionPassword = null;
        this.singleFileCompressionEnabled = false;
        this.targetFileName = "";
        this.singleFileCompressionPassword = null;
    }
    
    /**
     * 带参数的构造函数
     */
    public EmailTemplate(String name, String recipients, String ccRecipients,
                        String subject, String body, String filePrefixes, String fileExtensions) {
        this();
        this.name = name;
        this.recipients = recipients;
        this.ccRecipients = ccRecipients;
        this.subject = subject;
        this.body = body;
        this.filePrefixes = filePrefixes;
        this.fileExtensions = fileExtensions;
        this.excludeFilePattern = "";
        this.singleFileCompressionEnabled = false;
        this.targetFileName = "";
        this.singleFileCompressionPassword = null;
    }
    
    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getRecipients() {
        return recipients;
    }
    
    public void setRecipients(String recipients) {
        this.recipients = recipients;
    }
    
    public String getCcRecipients() {
        return ccRecipients;
    }
    
    public void setCcRecipients(String ccRecipients) {
        this.ccRecipients = ccRecipients;
    }
    
    public String getSubject() {
        return subject;
    }
    
    public void setSubject(String subject) {
        this.subject = subject;
    }
    
    public String getBody() {
        return body;
    }
    
    public void setBody(String body) {
        this.body = body;
    }
    
    public String getFilePrefixes() {
        return filePrefixes;
    }
    
    public void setFilePrefixes(String filePrefixes) {
        this.filePrefixes = filePrefixes;
    }
    
    public String getFileExtensions() {
        return fileExtensions;
    }
    
    public void setFileExtensions(String fileExtensions) {
        this.fileExtensions = fileExtensions;
    }

    public String getExcludeFilePattern() {
        return excludeFilePattern;
    }

    public void setExcludeFilePattern(String excludeFilePattern) {
        this.excludeFilePattern = excludeFilePattern;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getVariables() {
        return variables;
    }

    public void setVariables(String variables) {
        this.variables = variables;
    }
    
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }
    
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }
    
    public LocalDateTime getLastModified() {
        return lastModified;
    }
    
    public void setLastModified(LocalDateTime lastModified) {
        this.lastModified = lastModified;
    }
    
    public int getUseCount() {
        return useCount;
    }
    
    public void setUseCount(int useCount) {
        this.useCount = useCount;
    }
    
    public LocalDateTime getLastUsed() {
        return lastUsed;
    }
    
    public void setLastUsed(LocalDateTime lastUsed) {
        this.lastUsed = lastUsed;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isEnableCompression() {
        return enableCompression;
    }

    public void setEnableCompression(boolean enableCompression) {
        this.enableCompression = enableCompression;
    }

    public String getCompressionPassword() {
        return compressionPassword;
    }

    public void setCompressionPassword(String compressionPassword) {
        this.compressionPassword = compressionPassword;
    }

    /**
     * 检查是否设置了压缩密码
     */
    public boolean hasCompressionPassword() {
        return compressionPassword != null && !compressionPassword.trim().isEmpty();
    }

    public boolean isSingleFileCompressionEnabled() {
        return singleFileCompressionEnabled;
    }

    public void setSingleFileCompressionEnabled(boolean singleFileCompressionEnabled) {
        this.singleFileCompressionEnabled = singleFileCompressionEnabled;
    }

    public String getTargetFileName() {
        return targetFileName;
    }

    public void setTargetFileName(String targetFileName) {
        this.targetFileName = targetFileName;
    }

    /**
     * 检查是否设置了目标文件名
     */
    public boolean hasTargetFileName() {
        return targetFileName != null && !targetFileName.trim().isEmpty();
    }

    public String getSingleFileCompressionPassword() {
        return singleFileCompressionPassword;
    }

    public void setSingleFileCompressionPassword(String singleFileCompressionPassword) {
        this.singleFileCompressionPassword = singleFileCompressionPassword;
    }

    /**
     * 检查是否设置了单文件压缩密码
     */
    public boolean hasSingleFileCompressionPassword() {
        return singleFileCompressionPassword != null && !singleFileCompressionPassword.trim().isEmpty();
    }

    /**
     * 增加使用次数并更新最后使用时间
     */
    public void incrementUseCount() {
        this.useCount++;
        this.lastUsed = LocalDateTime.now();
        this.lastModified = LocalDateTime.now();
    }
    
    /**
     * 获取文件前缀列表
     */
    public List<String> getFilePrefixList() {
        if (filePrefixes == null || filePrefixes.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return Arrays.asList(filePrefixes.split(","));
    }
    
    /**
     * 设置文件前缀列表
     */
    public void setFilePrefixList(List<String> prefixes) {
        if (prefixes == null || prefixes.isEmpty()) {
            this.filePrefixes = "";
        } else {
            this.filePrefixes = String.join(",", prefixes);
        }
    }
    
    /**
     * 获取文件扩展名列表
     */
    public List<String> getFileExtensionList() {
        if (fileExtensions == null || fileExtensions.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return Arrays.asList(fileExtensions.split(","));
    }
    
    /**
     * 设置文件扩展名列表
     */
    public void setFileExtensionList(List<String> extensions) {
        if (extensions == null || extensions.isEmpty()) {
            this.fileExtensions = "";
        } else {
            this.fileExtensions = String.join(",", extensions);
        }
    }

    /**
     * 检查是否设置了排除模式
     */
    public boolean hasExcludePattern() {
        return excludeFilePattern != null && !excludeFilePattern.trim().isEmpty();
    }

    /**
     * 验证排除模式的正则表达式语法
     * @return 如果语法有效返回true，否则返回false
     */
    public boolean isExcludePatternValid() {
        if (!hasExcludePattern()) {
            return true; // 空模式视为有效
        }

        try {
            java.util.regex.Pattern.compile(excludeFilePattern);
            return true;
        } catch (java.util.regex.PatternSyntaxException e) {
            return false;
        }
    }

    /**
     * 获取排除模式的验证错误信息
     * @return 错误信息，如果模式有效则返回null
     */
    public String getExcludePatternValidationError() {
        if (!hasExcludePattern()) {
            return null;
        }

        try {
            java.util.regex.Pattern.compile(excludeFilePattern);
            return null;
        } catch (java.util.regex.PatternSyntaxException e) {
            return "正则表达式语法错误: " + e.getMessage();
        }
    }
    
    /**
     * 获取收件人列表
     */
    public List<String> getRecipientList() {
        if (recipients == null || recipients.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return Arrays.asList(recipients.split(";"));
    }
    
    /**
     * 设置收件人列表
     */
    public void setRecipientList(List<String> recipientList) {
        if (recipientList == null || recipientList.isEmpty()) {
            this.recipients = "";
        } else {
            this.recipients = String.join(";", recipientList);
        }
    }
    
    /**
     * 获取抄送人列表
     */
    public List<String> getCcRecipientList() {
        if (ccRecipients == null || ccRecipients.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return Arrays.asList(ccRecipients.split(";"));
    }
    
    /**
     * 设置抄送人列表
     */
    public void setCcRecipientList(List<String> ccRecipientList) {
        if (ccRecipientList == null || ccRecipientList.isEmpty()) {
            this.ccRecipients = "";
        } else {
            this.ccRecipients = String.join(";", ccRecipientList);
        }
    }
    
    /**
     * 验证模板是否有效
     */
    public boolean isValid() {
        return name != null && !name.trim().isEmpty() &&
               subject != null && !subject.trim().isEmpty();
    }
    
    /**
     * 获取显示名称（用于ComboBox显示）
     */
    public String getDisplayName() {
        if (description != null && !description.trim().isEmpty()) {
            return name + " - " + description;
        }
        return name;
    }
    
    @Override
    public String toString() {
        return getDisplayName();
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        EmailTemplate template = (EmailTemplate) obj;
        return id != null && id.equals(template.id);
    }
    
    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
