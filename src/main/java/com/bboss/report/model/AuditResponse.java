package com.bboss.report.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.bboss.report.component.ObjectConverter;
import lombok.Data;

import java.util.List;

@Data
public class AuditResponse {
    public static final String SUCCESS = "0";
    public static final String SUCCESS_DESC = "稽核成功";

    public static final String FAIL = "1";
    public static final String FAIL_DESC = "稽核失败";
    public static final String EXCEPTION = "-1";
    public static final String EXCEPTION_DESC = "程序运行失败";
    /**
     * 文件没有找到
     */
    public static final String NOTFIND =  "2";
    @ExcelIgnore
    public String rKey;
    @ExcelProperty(value="报表名称" ,index=0)
    private String reportName;
    @ExcelProperty(value="账期" ,index=1)
    private String settleMonth;
    @ExcelProperty(value="目录" ,index=2)
    private String directory;
    @ExcelProperty(value="报表文件名称" ,index=3)
    private String fileName;
    @ExcelProperty(value="报表稽核项" ,index=4 )
    private String auditEntryStr;
    @ExcelProperty(value="费率" ,index=5)
    private String taxRate;
    @ExcelProperty(value="稽核状态" ,index=6)
    public String rspCode;
    @ExcelProperty(value="稽核描述" ,index=7)
    public String rspDesc;
}
