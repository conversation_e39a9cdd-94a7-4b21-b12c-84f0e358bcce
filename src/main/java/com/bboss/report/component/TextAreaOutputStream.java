package com.bboss.report.component;

import javafx.application.Platform;
import javafx.scene.control.TextArea;

import java.io.IOException;
import java.io.OutputStream;

public class TextAreaOutputStream extends OutputStream {
    private TextArea textArea;

    public TextAreaOutputStream(TextArea textArea) {
        this.textArea = textArea;
    }

    @Override
    public void write(int b) throws IOException {
        Platform.runLater(() -> {
            //用Platform.runLater来运行需要高频调用的方法 否则会出现textArea报空指针和下标越界
            textArea.appendText(String.valueOf((char) b));
            //        滚动到底部
            textArea.setScrollTop(Double.MAX_VALUE);
        });

    }
    @Override
    public void write(byte[] b) {
        Platform.runLater(() -> {
            //用Platform.runLater来运行需要高频调用的方法 否则会出现textArea报空指针和下标越界
            textArea.appendText((new String(b)));
            //        滚动到底部
            textArea.setScrollTop(Double.MAX_VALUE);
        });
    }
}
