package com.bboss.report.component;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class DictMap {
    public static Map<String,String> taxRateMap = new LinkedHashMap<>();
    static{
        taxRateMap.put("1","0%");
        taxRateMap.put("2","6%");
        taxRateMap.put("3","9%");
        taxRateMap.put("4","10%");
        taxRateMap.put("5","11%");
        taxRateMap.put("6","13%");
        taxRateMap.put("7","16%");
        taxRateMap.put("8","17%");
    }

    public static List<String> provinceMap = new ArrayList<>();
    static{
        provinceMap.add("北京");
        provinceMap.add("广东");
        provinceMap.add("上海");
        provinceMap.add("天津");
        provinceMap.add("重庆");
        provinceMap.add("辽宁");
        provinceMap.add("江苏");
        provinceMap.add("湖北");
        provinceMap.add("四川");
        provinceMap.add("陕西");
        provinceMap.add("河北");
        provinceMap.add("山西");
        provinceMap.add("河南");
        provinceMap.add("吉林");
        provinceMap.add("黑龙江");
        provinceMap.add("内蒙古");
        provinceMap.add("山东");
        provinceMap.add("安徽");
        provinceMap.add("浙江");
        provinceMap.add("福建");
        provinceMap.add("湖南");
        provinceMap.add("广西");
        provinceMap.add("江西");
        provinceMap.add("贵州");
        provinceMap.add("云南");
        provinceMap.add("西藏");
        provinceMap.add("海南");
        provinceMap.add("甘肃");
        provinceMap.add("宁夏");
        provinceMap.add("青海");
        provinceMap.add("新疆");
    }

    public static List<String> provinceMapPlus = new ArrayList<>();
    static{
        provinceMapPlus.add("北京");
        provinceMapPlus.add("广东");
        provinceMapPlus.add("上海");
        provinceMapPlus.add("天津");
        provinceMapPlus.add("重庆");
        provinceMapPlus.add("辽宁");
        provinceMapPlus.add("江苏");
        provinceMapPlus.add("湖北");
        provinceMapPlus.add("四川");
        provinceMapPlus.add("陕西");
        provinceMapPlus.add("河北");
        provinceMapPlus.add("山西");
        provinceMapPlus.add("河南");
        provinceMapPlus.add("吉林");
        provinceMapPlus.add("黑龙江");
        provinceMapPlus.add("内蒙古");
        provinceMapPlus.add("山东");
        provinceMapPlus.add("安徽");
        provinceMapPlus.add("浙江");
        provinceMapPlus.add("福建");
        provinceMapPlus.add("湖南");
        provinceMapPlus.add("广西");
        provinceMapPlus.add("江西");
        provinceMapPlus.add("贵州");
        provinceMapPlus.add("云南");
        provinceMapPlus.add("西藏");
        provinceMapPlus.add("海南");
        provinceMapPlus.add("甘肃");
        provinceMapPlus.add("宁夏");
        provinceMapPlus.add("青海");
        provinceMapPlus.add("新疆");
        provinceMapPlus.add("互联网公司");
    }


    public static List<String> provinceMapAll = new ArrayList<>();
    static{
        provinceMapAll.add("北京");
        provinceMapAll.add("广东");
        provinceMapAll.add("上海");
        provinceMapAll.add("天津");
        provinceMapAll.add("重庆");
        provinceMapAll.add("辽宁");
        provinceMapAll.add("江苏");
        provinceMapAll.add("湖北");
        provinceMapAll.add("四川");
        provinceMapAll.add("陕西");
        provinceMapAll.add("河北");
        provinceMapAll.add("山西");
        provinceMapAll.add("河南");
        provinceMapAll.add("吉林");
        provinceMapAll.add("黑龙江");
        provinceMapAll.add("内蒙古");
        provinceMapAll.add("山东");
        provinceMapAll.add("安徽");
        provinceMapAll.add("浙江");
        provinceMapAll.add("福建");
        provinceMapAll.add("湖南");
        provinceMapAll.add("广西");
        provinceMapAll.add("江西");
        provinceMapAll.add("贵州");
        provinceMapAll.add("云南");
        provinceMapAll.add("西藏");
        provinceMapAll.add("海南");
        provinceMapAll.add("甘肃");
        provinceMapAll.add("宁夏");
        provinceMapAll.add("青海");
        provinceMapAll.add("新疆");
        provinceMapAll.add("互联网公司");
        provinceMapAll.add("成研院");
        provinceMapAll.add("中移互联网");
        provinceMapAll.add("中移金科");
        provinceMapAll.add("咪咕视讯");
        provinceMapAll.add("终端公司");
        provinceMapAll.add("卓望信息网络");
        provinceMapAll.add("中移集成");
        provinceMapAll.add("中移建设");
        provinceMapAll.add("物联网公司");
        provinceMapAll.add("政企分公司");
    }

    public static Map<String,String> auditEntryMap = new LinkedHashMap<>();
    static{
        auditEntryMap.put("1","报表金额是否保留两位小数");
        auditEntryMap.put("2","表尾签字处是否有制表人、IT中心部门审核、制表单位");
        auditEntryMap.put("3","报表结算月账期稽核");
        auditEntryMap.put("4","应结入：税额 = 含税金额- 不含税金额");
        auditEntryMap.put("5","应结入：不含税金额= 含税金额/（1+税率）");
        auditEntryMap.put("6","应结入：含税金额=税额 +不含税金额");
        auditEntryMap.put("7","应结出：税额 = 含税金额- 不含税金额");
        auditEntryMap.put("8","应结出：不含税金额= 含税金额/（1+税率）");
        auditEntryMap.put("9","应结出：含税金额=税额 +不含税金额");
        auditEntryMap.put("10","“税率”的报表税率稽核");
        auditEntryMap.put("11","“产品”不为空，不为全数字");
        auditEntryMap.put("12","“费项”不为空，不为全数字");
        auditEntryMap.put("13","合计=对应列求和");
        auditEntryMap.put("14","“结算额”的“含税金额”=“应结入”的“含税金额”-“应结出”的“含税金额”");
        auditEntryMap.put("15","“实收账期”为当前账期");
        auditEntryMap.put("16","“计费账期”不为空");
        auditEntryMap.put("17","“结算对方省“不为空");
        auditEntryMap.put("18","报表签约主体稽核（帐套）");
        auditEntryMap.put("19","“归属省”不为空，不为全数字");
        auditEntryMap.put("20","“结算项目”不为空，不为全数字");
        auditEntryMap.put("21","税额 = 含税金额- 不含税金额");
        auditEntryMap.put("22","不含税金额= 含税金额/（1+税率）");
        auditEntryMap.put("23","含税金额=税额 +不含税金额");
        auditEntryMap.put("24","“业务总收入”的含税金额=“政企结算金额”的含税金额+“省公司结算金额”的含税金额");
        auditEntryMap.put("25","“省公司”不为空，不为全数字！");
        auditEntryMap.put("26", "“应收合计”的“结算额”=所有结算方负值之和");
        auditEntryMap.put("27", "“应付合计”的“结算额”=所有结算方正值之和");
        auditEntryMap.put("28","31个省中，当客户数为0，“结算额”也为0");
        auditEntryMap.put("29","“结算额”的“含税金额”=“市场化结算”下的各项“不含税金额”和“税额”的和");
        auditEntryMap.put("30","合计不为0");
        auditEntryMap.put("31","产品科目编码不为空");
        auditEntryMap.put("32","“厂家名称”不为空，不为全数字");
        auditEntryMap.put("33","“厂家代码”不为空");
        auditEntryMap.put("34","产品代码不为空");
        auditEntryMap.put("35","产品名称不为空，不为全数字");
        auditEntryMap.put("36","产品资费名称不为空，不为全数字");
        auditEntryMap.put("37","产品资费代码不为空");
        auditEntryMap.put("38","本期考核预留金额（含税）=（本期应收可结算金额（含税）之和+本期核减之和）*考核预留比例");
        auditEntryMap.put("39","实际支付金额的含税金额=（本期应收可结算金额（含税）之和+本期核减之和）-本期考核预留金额（含税）");
        auditEntryMap.put("40","结算金额合计(含税)=应结算金额(含税)+调整金额(含税)");
        auditEntryMap.put("41","含税金额=结算金额合计（含税）+政企分公司应结算给合作伙伴的含税金额(政企直签客户)");
        auditEntryMap.put("42","不含税金额> 税金");
        auditEntryMap.put("43","合作伙伴不为空，不为全数字");
        auditEntryMap.put("44","结算额=含税金额");
        auditEntryMap.put("45","产品编码不为空");
        auditEntryMap.put("46","业务名称不为空，不为全数字");
        auditEntryMap.put("47","本期考核预留金额=本期结算金额*20%");
        auditEntryMap.put("48","实际支付金额总计 = 本期结算金额 - 本期考核预留金额+调整金额");
        auditEntryMap.put("49","应分成金额(含税)=本期结算金额");
        auditEntryMap.put("50","农政通:总共收入=通信费+功能费");
        auditEntryMap.put("51","本期应结算金额=计费金额*结算比例(%)，保留两位小数，四舍五入");
        auditEntryMap.put("52","“集团客户”不为空，不为全数字");
        auditEntryMap.put("53","除辽宁30省合计=合计-辽宁省份");
        auditEntryMap.put("54","\"集团客户签约省\"不为空，不为全数字");
        auditEntryMap.put("55","产品类型不为空，不为全数字");
        auditEntryMap.put("56","合作厂家名称不为空，不为全数字");
        auditEntryMap.put("57","税率为6");
        auditEntryMap.put("58","税额 = 结算额（含税）- 结算额（不含数据），保留两位小数，四舍五入");
        auditEntryMap.put("59","合计=“应收合计”+“应付合计”");
        auditEntryMap.put("60","world文件名称是否正确");
        auditEntryMap.put("61", "“应收合计”的“结算额”=所有结算方正值之和(销暂估)");
        auditEntryMap.put("62", "“应付合计”的“结算额”=所有结算方负值之和(销暂估)");
        auditEntryMap.put("63","表尾签字处是否有制表单位:中国移动信息技术中心、制表人、IT中心部门审核、IT中心领导审核");
        auditEntryMap.put("64","应结入“***”“不含税金额”=应结出“不含税金额”的总和");
        auditEntryMap.put("65","应结入“***”“含税金额”=应结出“含税金额”的总和");
        auditEntryMap.put("66","应结入“***”“税额”=应结出“税额”的总和");
        auditEntryMap.put("67","应结出“含税金额”=应结入“含税金额”-结算额“含税金额”");
        auditEntryMap.put("68","结算额“***”=结算额中31省的金额总和的相反数");
        auditEntryMap.put("69","“集团客户”不为空");       //竖着
        auditEntryMap.put("70","“客户编码” 不为空");       //竖着
        auditEntryMap.put("71","各产品名称不为空，不为全数字");
        auditEntryMap.put("72","厂家代码不为空");          //竖着
        auditEntryMap.put("73","小计=对应列求和(江苏&浙江)");
        auditEntryMap.put("74","合计=小计(江苏&浙江)");
        auditEntryMap.put("75","“***”为空(江苏&浙江)");
        auditEntryMap.put("76","匹配报表头");
        auditEntryMap.put("77","匹配双行报表头");
        auditEntryMap.put("78","结算额“***”=市场化结算31省中‘‘***’’的金额总和的相反数");
        auditEntryMap.put("79","多个分表的金额相加综合等于总表金额");
        auditEntryMap.put("80","不含税金额相加=合计不含税金额,税额相加=合计税额");
        auditEntryMap.put("81","金额相加(配置读取)");
        auditEntryMap.put("82","金额相加-相反数(配置读取)");
        auditEntryMap.put("83","暂估与销暂估的数据金额对比");
        auditEntryMap.put("84","指定产品是否突破封顶金额的判断");
        auditEntryMap.put("85","税额=（不含税金额+税额）-round（（不含税金额+税额）/1.06,2）");
        auditEntryMap.put("86","合计：税额=含税金额-不含税金额");
        auditEntryMap.put("87","特殊分表的sheet页名称稽核，名称需与表头省份一致，同时校验名称需为31省名称");
        auditEntryMap.put("88","pdf是否为文字");
        auditEntryMap.put("89","plus匹配报表头");
        auditEntryMap.put("90","sheet和省公司匹配");
        auditEntryMap.put("91","往来段不能为空");
        auditEntryMap.put("92","非特殊分表必须同时有pdf及xls文件存在");
        auditEntryMap.put("93","excle与word文件中的结算金额进行比对");
        auditEntryMap.put("94","总表与分表的稽核比对");
        auditEntryMap.put("95","总表与分表的稽核比对通过税率");
        auditEntryMap.put("96","总表与分表的稽核比对公司");
        auditEntryMap.put("97","总表与分表的稽核根据省份比对金额信息");
        auditEntryMap.put("98","总表sheet合计不含税与分表的省份不含税金额稽核比对信息");
        auditEntryMap.put("99","报表金额保留两位小数，金额不能为空！");
        auditEntryMap.put("100","暂估与正常报表估的数据金额对比");
        auditEntryMap.put("101","分表报表文件和总表文件匹配信息");
    }
    public static Map<String,String> auditEntryMethodMap = new LinkedHashMap<>();
    static{
        auditEntryMethodMap.put("1","keepTwoDecimals");
        auditEntryMethodMap.put("2","endSign");
        auditEntryMethodMap.put("3","settleMonth");
        auditEntryMethodMap.put("4","inTaxAmount");
        auditEntryMethodMap.put("5","inNoIncludeTaxAmount");
        auditEntryMethodMap.put("6","inIncludeTaxAmount");
        auditEntryMethodMap.put("7","outTaxAmount");
        auditEntryMethodMap.put("8","outNoIncludeTaxAmount");
        auditEntryMethodMap.put("9","outIncludeTaxAmount");
        auditEntryMethodMap.put("10","taxRate");
        auditEntryMethodMap.put("11","productNotNull");
        auditEntryMethodMap.put("12","chargesNotNull");
        auditEntryMethodMap.put("13","sumColumn");
        auditEntryMethodMap.put("14","settleIncludeTaxAmount");
        auditEntryMethodMap.put("15","paidInSettleMonth");
        auditEntryMethodMap.put("16","billingPeriodNotNull");
        auditEntryMethodMap.put("17","settleOtherProvNotNull");
        auditEntryMethodMap.put("18","signSubject");
        auditEntryMethodMap.put("19","provNotNull");
        auditEntryMethodMap.put("20","settleItem");
        auditEntryMethodMap.put("21","taxAmount");
        auditEntryMethodMap.put("22","noIncludeTaxAmount");
        auditEntryMethodMap.put("23","includeTaxAmount");
        auditEntryMethodMap.put("24","includeTaxAmountAdd");
        auditEntryMethodMap.put("25","provNotNull");
        auditEntryMethodMap.put("26","totalCharge");
        auditEntryMethodMap.put("27","totalPay");
        auditEntryMethodMap.put("28","custAndSettleAmount");
        auditEntryMethodMap.put("29","settleIncludeTaxAmountMarket");
        auditEntryMethodMap.put("30","totalNotZero");
        auditEntryMethodMap.put("31","notNull");
        auditEntryMethodMap.put("32","companyNameNotNull");
        auditEntryMethodMap.put("33","companyNumNotNull");
        auditEntryMethodMap.put("34","productNumNotNull");
        auditEntryMethodMap.put("35","productNotNull");
        auditEntryMethodMap.put("36","tariffNameNotNull");
        auditEntryMethodMap.put("37","tariffNumNotNull");
        auditEntryMethodMap.put("38","appraisalReservedAmount");
        auditEntryMethodMap.put("39","actualAmountPaid");
        auditEntryMethodMap.put("40","includeTaxAmountAdd");
        auditEntryMethodMap.put("41","includeTaxAmountAdd2");
        auditEntryMethodMap.put("42","noIncludeTaxAmountGreaterTax");
        auditEntryMethodMap.put("43","notNull");
        auditEntryMethodMap.put("44","settleAmountEqualIncludeTaxAmount");
        auditEntryMethodMap.put("45","productNumNotNull");
        auditEntryMethodMap.put("46","productNotNull");
        auditEntryMethodMap.put("47","amountMultByProportion");
        auditEntryMethodMap.put("48","amountMinusAndAdd");
        auditEntryMethodMap.put("49","settleAmountEqualIncludeTaxAmount");
        auditEntryMethodMap.put("50","aggregateIncomeCommunicationFunctionFee");
        auditEntryMethodMap.put("51","aggregate");
        auditEntryMethodMap.put("52","groupCustomersNotNull");
        auditEntryMethodMap.put("53","settleTotalMinusLiaoningProvince");
        auditEntryMethodMap.put("54","groupCustomersProvinceNotNull");
        auditEntryMethodMap.put("55","producTypeNumNotNll");
        auditEntryMethodMap.put("56","cooperativeManufacturerNotNull");
        auditEntryMethodMap.put("57","taxRateIsSix");
        auditEntryMethodMap.put("58","taxAmountSettlementAmount");
        auditEntryMethodMap.put("59","totalChargeAddTotalPay");
        auditEntryMethodMap.put("60","worldFileName");
        auditEntryMethodMap.put("61","totalChargeTSE");
        auditEntryMethodMap.put("62","totalPayTSE");
        auditEntryMethodMap.put("63","endSignPlus");
        auditEntryMethodMap.put("64","misMatchInOutcludeTaxAmount");
        auditEntryMethodMap.put("65","misMatchInIncludeTaxAmount");
        auditEntryMethodMap.put("66","misMatchInOutTaxAmount");
        auditEntryMethodMap.put("67","misMatchOutIncludeTaxAmount");
        auditEntryMethodMap.put("68","minusTaxAmountMarket");
        auditEntryMethodMap.put("69","groupCustomerNotNull");
        auditEntryMethodMap.put("70","customerCodeNotNull");
        auditEntryMethodMap.put("71","productNameNotNull");
        auditEntryMethodMap.put("72","manufacturerCodeNotNull");
        auditEntryMethodMap.put("73","subSumColumnJSZJ");
        auditEntryMethodMap.put("74","matchSubSumJSZJ");
        auditEntryMethodMap.put("75","isEmptyJSZJ");
        auditEntryMethodMap.put("76","matchHeader");
        auditEntryMethodMap.put("77","matchDoubleHeader");
        auditEntryMethodMap.put("78","provFinalAmnountEqualTo31ProSumAmountsOppositeNum");
        auditEntryMethodMap.put("79","aggregateSum");
        auditEntryMethodMap.put("80","moneyAdd");
        auditEntryMethodMap.put("81","moneyAddAutomatic");
        auditEntryMethodMap.put("82","moneyAddAutomaticDif");
        auditEntryMethodMap.put("83","elstimateMoney");
        auditEntryMethodMap.put("84","cappingMoney");
        auditEntryMethodMap.put("85","outTaxSkuAmount");
        auditEntryMethodMap.put("86","totalCalculatedAmount");
        auditEntryMethodMap.put("87","matchSheetHeader");
        auditEntryMethodMap.put("89","matchHeaderPlus");
        auditEntryMethodMap.put("90","matchSheet");
        auditEntryMethodMap.put("91","interSectionNotNull");
        auditEntryMethodMap.put("93","comparePriceWord");
        auditEntryMethodMap.put("94","totalComparePrice");
        auditEntryMethodMap.put("95","totalComparePriceAndTaxRate");
        auditEntryMethodMap.put("96","totalCompareToMorePrice");
        auditEntryMethodMap.put("97","compareTwoExcelColumnPrice");
        auditEntryMethodMap.put("98","compareTwoExcelProvinceTotalPrice");
        auditEntryMethodMap.put("99","keepTwoDecimalsNotNull");
//        auditEntryMethodMap.put("101","subFileNameCompareTotalName");
    }

}
