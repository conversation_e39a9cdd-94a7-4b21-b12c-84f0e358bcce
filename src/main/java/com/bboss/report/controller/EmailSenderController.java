package com.bboss.report.controller;

import cn.hutool.core.date.DateUtil;
import com.bboss.report.model.EmailSettings;
import com.bboss.report.model.EmailTemplate;
import com.bboss.report.model.RecipientInfo;
import com.bboss.report.model.TemplateVariable;
import com.bboss.report.util.AttachmentCompressionHandler;
import com.bboss.report.util.ConfigManager;
import com.bboss.report.util.DirectoryAttachmentProcessor;
import com.bboss.report.util.FileFilterUtil;
import com.bboss.report.util.RecipientManager;
import com.bboss.report.util.TemplateManager;
import com.bboss.report.util.TemplateVariableProcessor;
import javafx.animation.KeyFrame;
import javafx.animation.Timeline;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonBar;
import javafx.scene.control.ButtonType;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.control.ListCell;
import javafx.scene.control.ListView;
import javafx.scene.control.MenuItem;
import javafx.scene.control.TextArea;
import javafx.scene.control.TextField;
import javafx.stage.DirectoryChooser;
import javafx.stage.FileChooser;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.stage.Window;
import javafx.util.Duration;
import lombok.extern.slf4j.Slf4j;

import javax.activation.DataHandler;
import javax.activation.FileDataSource;
import javax.mail.Authenticator;
import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.Multipart;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

@Slf4j
public class EmailSenderController {

    // --- FXML 控件注入 ---
    @FXML private MenuItem settingsMenuItem;
    @FXML private ComboBox<EmailTemplate> templateComboBox;
    @FXML private Button applyTemplateButton;
    @FXML private Button selectDirectoryButton;
    @FXML private Button manageTemplatesButton;
    @FXML private ComboBox<String> senderEmailComboBox;
    @FXML private ComboBox<String> recipientComboBox;
    @FXML private Button addRecipientButton;
    @FXML private Button manageRecipientsButton;
    @FXML private TextArea recipientField;
    @FXML private ComboBox<String> ccComboBox;
    @FXML private Button addCcButton;
    @FXML private TextArea ccField;
    @FXML private TextField subjectField;
    @FXML private Button addFileButton;
    @FXML private Button addFolderButton;
    @FXML private Button removeAttachmentButton;
    @FXML private Button smartProcessButton;
    @FXML private ListView<String> attachmentListView;
    @FXML private TextArea bodyArea;
    @FXML private Label statusLabel;
    @FXML private Button sendButton;

    // 存储显示文本和实际文件路径的映射
    private Map<String, String> attachmentPathMap = new HashMap<>();

    // 模板管理器
    private TemplateManager templateManager;

    // 当前选择的目录路径（用于模板应用）
    private String selectedDirectoryPath;

    // 智能处理历史记录
    private SmartProcessingHistory processingHistory = new SmartProcessingHistory();

    /**
     * 模板应用结果类
     */
    private static class TemplateApplicationResult {
        private final int totalFilesFound;
        private final int filesAdded;
        private final List<String> skippedDuplicates;
        private final FileFilterUtil.FilterResult filterResult;

        public TemplateApplicationResult(int totalFilesFound, int filesAdded,
                                       List<String> skippedDuplicates,
                                       FileFilterUtil.FilterResult filterResult) {
            this.totalFilesFound = totalFilesFound;
            this.filesAdded = filesAdded;
            this.skippedDuplicates = skippedDuplicates;
            this.filterResult = filterResult;
        }

        public int getTotalFilesFound() { return totalFilesFound; }
        public int getFilesAdded() { return filesAdded; }
        public List<String> getSkippedDuplicates() { return skippedDuplicates; }
        public FileFilterUtil.FilterResult getFilterResult() { return filterResult; }
        public boolean hasDuplicates() { return !skippedDuplicates.isEmpty(); }
    }

    /**
     * FXML 加载后会自动调用此方法 (如果设置了 fx:controller)。
     * 我们在这里通过编程方式绑定事件，以获得更大的灵活性。
     * 如果在 FXML 中设置了 onAction="#methodName"，则不需要手动绑定。
     */
    @FXML
    public void initialize() {
        // --- 初始化模板管理器 ---
        templateManager = TemplateManager.getInstance();

        // --- 绑定菜单项事件 ---
        settingsMenuItem.setOnAction(event -> showSettingsDialog());

        // --- 绑定模板相关按钮事件 ---
        applyTemplateButton.setOnAction(event -> applySelectedTemplate());
        selectDirectoryButton.setOnAction(event -> selectDirectory());
        manageTemplatesButton.setOnAction(event -> showTemplateManagementDialog());

        // --- 绑定附件按钮事件 ---
        addFileButton.setOnAction(event -> addFiles());
        addFolderButton.setOnAction(event -> addFolder());
        removeAttachmentButton.setOnAction(event -> removeSelectedAttachment());
        smartProcessButton.setOnAction(event -> handleSmartProcessing());

        // --- 绑定收件人管理按钮事件 ---
        addRecipientButton.setOnAction(event -> addRecipientFromComboBox());
        addCcButton.setOnAction(event -> addCcFromComboBox());
        manageRecipientsButton.setOnAction(event -> showRecipientManagementDialog());

        // --- 绑定发送按钮事件 ---
        sendButton.setOnAction(event -> sendEmailWithRetry());

        // --- 初始化发件人ComboBox ---
        initializeSenderEmailComboBox();

        // --- 初始化模板ComboBox ---
        initializeTemplateComboBox();

        // --- 初始化收件人ComboBox ---
        initializeRecipientComboBoxes();

        statusLabel.setText("状态：准备就绪");

        // 初始化时检查附件大小
        checkAttachmentSizes();

        // 添加测试按钮事件（临时调试用）
//        if (smartProcessButton != null) {
//            smartProcessButton.setOnMouseClicked(event -> {
//                if (event.getClickCount() == 2) { // 双击触发测试
//                    testAttachmentSizeValidation();
//                }
//            });
//        }
    }

    /**
     * 初始化发件人邮箱ComboBox
     */
    private void initializeSenderEmailComboBox() {
        ConfigManager configManager = ConfigManager.getInstance();

        // 加载保存的发件人邮箱列表
        List<String> savedEmails = configManager.getSavedSenderEmails();
        ObservableList<String> emailList = FXCollections.observableArrayList(savedEmails);
        senderEmailComboBox.setItems(emailList);

        // 设置当前配置中的发件人邮箱为默认选择
        EmailSettings currentSettings = configManager.getCurrentSettings();
        if (currentSettings.getSenderEmail() != null && !currentSettings.getSenderEmail().trim().isEmpty()) {
            String currentEmail = currentSettings.getSenderEmail().trim();
            senderEmailComboBox.setValue(currentEmail);

            // 如果当前邮箱不在保存列表中，添加到列表
            if (!savedEmails.contains(currentEmail)) {
                configManager.addSenderEmail(currentEmail);
                emailList.add(currentEmail);
            }
        }

        // 监听ComboBox值变化，自动保存新输入的邮箱
        senderEmailComboBox.valueProperty().addListener((observable, oldValue, newValue) -> {
            if (newValue != null && !newValue.trim().isEmpty()) {
                String email = newValue.trim();
                if (!savedEmails.contains(email)) {
                    configManager.addSenderEmail(email);
                    // 刷新ComboBox列表
                    refreshSenderEmailComboBox();
                }
            }
        });

        // 监听编辑器文本变化（当用户直接输入时）
        senderEmailComboBox.getEditor().textProperty().addListener((observable, oldValue, newValue) -> {
            if (newValue != null && !newValue.trim().isEmpty()) {
                String email = newValue.trim();
                // 简单的邮箱格式验证
                if (email.contains("@") && email.contains(".")) {
                    if (!savedEmails.contains(email)) {
                        configManager.addSenderEmail(email);
                    }
                }
            }
        });
    }

    /**
     * 刷新发件人邮箱ComboBox列表
     */
    private void refreshSenderEmailComboBox() {
        ConfigManager configManager = ConfigManager.getInstance();
        List<String> savedEmails = configManager.getSavedSenderEmails();

        String currentValue = senderEmailComboBox.getValue();
        senderEmailComboBox.setItems(FXCollections.observableArrayList(savedEmails));
        senderEmailComboBox.setValue(currentValue);
    }

    /**
     * 初始化收件人ComboBox
     */
    private void initializeRecipientComboBoxes() {
        try {
            // 初始化收件人ComboBox
            refreshRecipientComboBox();

            // 监听收件人ComboBox的选择事件
            recipientComboBox.setOnAction(event -> {
                try {
                    String selected = recipientComboBox.getValue();
                    if (selected != null && !selected.trim().isEmpty()) {
                        // 从显示文本中提取邮箱地址
                        String email = extractEmailFromDisplayText(selected);
                        if (!email.isEmpty()) {
                            addEmailToField(recipientField, email);
                            // 延迟清空选择，避免在事件处理期间修改ComboBox状态
                            Platform.runLater(() -> recipientComboBox.setValue(""));
                        }
                    }
                } catch (Exception e) {
                    log.error("处理收件人选择时发生错误: ", e);
                }
            });

            // 监听抄送ComboBox的选择事件
            ccComboBox.setOnAction(event -> {
                try {
                    String selected = ccComboBox.getValue();
                    if (selected != null && !selected.trim().isEmpty()) {
                        String email = extractEmailFromDisplayText(selected);
                        if (!email.isEmpty()) {
                            addEmailToField(ccField, email);
                            // 延迟清空选择，避免在事件处理期间修改ComboBox状态
                            Platform.runLater(() -> ccComboBox.setValue(""));
                        }
                    }
                } catch (Exception e) {
                    log.error("处理抄送选择时发生错误: ", e);
                }
            });

        } catch (Exception e) {
            log.error("初始化收件人ComboBox时发生错误: ", e);
            // 如果初始化失败，至少确保ComboBox可以正常使用
            recipientComboBox.setItems(FXCollections.observableArrayList());
            ccComboBox.setItems(FXCollections.observableArrayList());
        }
    }

    /**
     * 刷新收件人ComboBox列表
     */
    private void refreshRecipientComboBox() {
        try {
            RecipientManager recipientManager = RecipientManager.getInstance();

            // 获取最近使用的收件人
            List<RecipientInfo> recentRecipients = recipientManager.getRecentlyUsedRecipients(20);
            List<String> displayTexts = new ArrayList<>();

            if (recentRecipients != null) {
                for (RecipientInfo recipient : recentRecipients) {
                    if (recipient != null) {
                        try {
                            String displayText = recipient.getDisplayText();
                            if (displayText != null && !displayText.trim().isEmpty()) {
                                displayTexts.add(displayText);
                            }
                        } catch (Exception e) {
                            // 继续处理其他收件人
                            log.error("处理收件人显示文本时发生错误: ", e);
                        }
                    }
                }
            }

            // 安全地更新ComboBox
            if (recipientComboBox != null) {
                recipientComboBox.setItems(FXCollections.observableArrayList(displayTexts));
            }
            if (ccComboBox != null) {
                ccComboBox.setItems(FXCollections.observableArrayList(displayTexts));
            }

        } catch (Exception e) {
            log.error("刷新收件人ComboBox时发生错误: ", e);

            // 如果出错，至少确保ComboBox有空列表
            try {
                if (recipientComboBox != null) {
                    recipientComboBox.setItems(FXCollections.observableArrayList());
                }
                if (ccComboBox != null) {
                    ccComboBox.setItems(FXCollections.observableArrayList());
                }
            } catch (Exception ex) {
                log.error("设置空列表时也发生错误: ", ex);
            }
        }
    }

    /**
     * 从显示文本中提取邮箱地址
     */
    private String extractEmailFromDisplayText(String displayText) {
        if (displayText == null || displayText.trim().isEmpty()) {
            return "";
        }

        // 处理格式：Name <<EMAIL>> [tags]
        if (displayText.contains("<") && displayText.contains(">")) {
            int start = displayText.indexOf("<") + 1;
            int end = displayText.indexOf(">");
            return displayText.substring(start, end).trim();
        }

        // 如果没有尖括号，可能直接是邮箱地址
        String[] parts = displayText.split("\\s+");
        for (String part : parts) {
            if (part.contains("@")) {
                return part.trim();
            }
        }

        return displayText.trim();
    }

    /**
     * 向文本区域添加邮箱地址
     */
    private void addEmailToField(TextArea field, String email) {
        try {
            if (field == null) {
                log.error("文本区域为空，无法添加邮箱地址");
                return;
            }

            if (email == null || email.trim().isEmpty()) {
                log.error("邮箱地址为空，无法添加");
                return;
            }

            String trimmedEmail = email.trim();
            String currentText = field.getText();

            if (currentText == null || currentText.trim().isEmpty()) {
                field.setText(trimmedEmail);
            } else {
                // 检查是否已经存在该邮箱
                String[] existingEmails = currentText.split("[;,]");
                for (String existing : existingEmails) {
                    if (existing.trim().equalsIgnoreCase(trimmedEmail)) {
                        log.warn("邮箱地址已存在，不重复添加: " + trimmedEmail);
                        return; // 已存在，不重复添加
                    }
                }

                // 添加新邮箱
                if (!currentText.endsWith(";") && !currentText.endsWith(",")) {
                    field.setText(currentText + "; " + trimmedEmail);
                } else {
                    field.setText(currentText + " " + trimmedEmail);
                }
            }

        } catch (Exception e) {
            log.error("添加邮箱地址到文本区域时发生错误: ", e);
        }
    }

    /**
     * 从ComboBox添加收件人
     */
    private void addRecipientFromComboBox() {
        try {
            if (recipientComboBox == null || recipientComboBox.getEditor() == null) {
                log.error("收件人ComboBox或编辑器为空");
                return;
            }

            String input = recipientComboBox.getEditor().getText();
            if (input != null && !input.trim().isEmpty()) {
                addEmailToField(recipientField, input.trim());
                recipientComboBox.getEditor().clear();
            }
        } catch (Exception e) {
            log.error("从ComboBox添加收件人时发生错误: ", e);
        }
    }

    /**
     * 从ComboBox添加抄送人
     */
    private void addCcFromComboBox() {
        try {
            if (ccComboBox == null || ccComboBox.getEditor() == null) {
                log.error("抄送ComboBox或编辑器为空");
                return;
            }

            String input = ccComboBox.getEditor().getText();
            if (input != null && !input.trim().isEmpty()) {
                addEmailToField(ccField, input.trim());
                ccComboBox.getEditor().clear();
            }
        } catch (Exception e) {
            log.error("从ComboBox添加抄送人时发生错误: ", e);
        }
    }

    /**
     * 显示收件人管理对话框
     */
    private void showRecipientManagementDialog() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/RecipientManagementView.fxml"));
            Parent root = loader.load();

            Stage recipientStage = new Stage();
            recipientStage.setTitle("收件人管理");
            recipientStage.setScene(new Scene(root));
            recipientStage.initModality(Modality.WINDOW_MODAL);
            recipientStage.initOwner(statusLabel.getScene().getWindow());

            // 显示对话框并等待用户操作
            recipientStage.showAndWait();

            // 对话框关闭后，刷新收件人ComboBox
            refreshRecipientComboBox();
            statusLabel.setText("状态：收件人管理对话框已关闭。");

        } catch (Exception e) {
            showError("无法打开收件人管理对话框：" + e.getMessage());
            log.error("无法打开收件人管理对话框: ", e);
        }
    }

    /**
     * 显示邮件发送设置对话框
     */
    private void showSettingsDialog() {
        try {
            // 加载设置对话框的FXML文件
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/settings.fxml"));
            Parent root = loader.load();

            // 创建新的舞台（窗口）
            Stage settingsStage = new Stage();
            settingsStage.setTitle("邮件发送设置");
            settingsStage.setScene(new Scene(root));

            // 设置为模态对话框
            settingsStage.initModality(Modality.APPLICATION_MODAL);

            // 设置父窗口
            Window parentWindow = statusLabel.getScene().getWindow();
            settingsStage.initOwner(parentWindow);

            // 设置窗口属性
            settingsStage.setResizable(false);

            // 显示对话框并等待用户操作
            settingsStage.showAndWait();

            // 设置对话框关闭后，刷新发件人ComboBox
            refreshSenderEmailComboBox();
            statusLabel.setText("状态：设置对话框已关闭。");

        } catch (IOException e) {
            showError("无法打开设置对话框：" + e.getMessage());
            statusLabel.setText("状态：打开设置对话框失败。");
        }
    }

    /**
     * 使用 FileChooser 添加一个或多个文件作为附件
     */
    private void addFiles() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("选择附件文件");
        // 获取当前窗口用于显示 FileChooser
        Window ownerWindow = attachmentListView.getScene().getWindow();
        List<File> selectedFiles = fileChooser.showOpenMultipleDialog(ownerWindow);

        if (selectedFiles != null && !selectedFiles.isEmpty()) {
            for (File file : selectedFiles) {
                String filePath = file.getAbsolutePath();
                // 避免重复添加
                if (!attachmentPathMap.containsValue(filePath)) {
                    String displayText = formatAttachmentDisplay(filePath, false, true); // 手动添加显示路径信息
                    attachmentListView.getItems().add(displayText);
                    attachmentPathMap.put(displayText, filePath);
                }
            }
            statusLabel.setText("状态：" + selectedFiles.size() + " 个文件已添加。");
            // 检查附件大小
            checkAttachmentSizes();
        } else {
            statusLabel.setText("状态：未选择任何文件。");
        }
    }

    /**
     * 使用 DirectoryChooser 添加一个文件夹作为附件
     */
    private void addFolder() {
        DirectoryChooser directoryChooser = new DirectoryChooser();
        directoryChooser.setTitle("选择附件文件夹");
        Window ownerWindow = attachmentListView.getScene().getWindow();
        File selectedDirectory = directoryChooser.showDialog(ownerWindow);

        if (selectedDirectory != null) {
            String dirPath = selectedDirectory.getAbsolutePath();
            // 避免重复添加
            if (!attachmentPathMap.containsValue(dirPath)) {
                String displayText = formatAttachmentDisplay(dirPath, true, true); // 手动添加目录显示路径信息
                attachmentListView.getItems().add(displayText);
                attachmentPathMap.put(displayText, dirPath);
            }
            statusLabel.setText("状态：文件夹 '" + selectedDirectory.getName() + "' 已添加。");
            // 检查附件大小
            checkAttachmentSizes();
        } else {
            statusLabel.setText("状态：未选择任何文件夹。");
        }
    }

    /**
     * 从附件列表中移除选中的项
     */
    private void removeSelectedAttachment() {
        // getSelectionModel() 可能为 null
        if (attachmentListView.getSelectionModel() == null) return;
        
        ObservableList<String> selectedItems = attachmentListView.getSelectionModel().getSelectedItems();
        if (selectedItems != null && !selectedItems.isEmpty()) {
            // 创建选中项目的副本，避免使用只读视图
            List<String> itemsToRemove = new ArrayList<>(selectedItems);
            
            // 清除选择状态，避免索引冲突
            attachmentListView.getSelectionModel().clearSelection();
            
            // 从映射中移除对应的条目
            for (String item : itemsToRemove) {
                attachmentPathMap.remove(item);
            }
            
            // 从 ListView 的 items 列表中移除
            attachmentListView.getItems().removeAll(itemsToRemove);
            statusLabel.setText("状态：已移除选中的附件。");
            // 检查附件大小
            checkAttachmentSizes();
        } else {
            statusLabel.setText("状态：请先在列表中选择要移除的附件。");
        }
    }

    /**
     * 带重试功能的邮件发送
     */
    private void sendEmailWithRetry() {
        // 基本验证
        if (!validateEmailInput()) {
            return;
        }

        // 获取发件人邮箱
        String senderEmail = getSenderEmailFromComboBox();
        if (senderEmail == null || senderEmail.trim().isEmpty()) {
            showError("请选择或输入发件人邮箱地址！");
            statusLabel.setText("状态：错误，发件人邮箱不能为空！");
            return;
        }

        // 显示发送确认对话框
        if (!showSendConfirmationDialog()) {
            statusLabel.setText("状态：用户取消发送邮件");
            return;
        }

        // 保存发件人邮箱到历史记录
        ConfigManager.getInstance().addSenderEmail(senderEmail);

        // 禁用发送按钮，防止重复点击
        sendButton.setDisable(true);
        statusLabel.setText("状态：正在准备发送邮件...");

        try {
            // 简化的重试逻辑，避免线程问题
            int maxRetries = 3;
            int retryCount = 0;

            while (true) {
                try {
                    if (retryCount > 0) {
                        statusLabel.setText("状态：正在重试发送邮件... (第 " + (retryCount + 1) + " 次尝试)");
                        // 简单延迟
                        Thread.sleep(2000);
                    } else {
                        statusLabel.setText("状态：正在发送邮件...");
                    }

                    sendEmail();

                    // 邮件发送成功后，保存收件人信息
                    saveRecipientsAfterSuccess();

                    // 清理智能处理历史
                    clearSmartProcessingHistory();

                    statusLabel.setText("状态：邮件发送成功！");
                    return; // 成功发送，退出

                } catch (Exception e) {
                    retryCount++;
                    log.error("邮件发送第：{}次失败: ",retryCount, e);

                    if (retryCount >= maxRetries) {
                        // 最后一次尝试失败，显示错误
                        statusLabel.setText("状态：邮件发送失败！");

                        // 检查是否是450错误
                        if (e.getMessage() != null &&
                            (e.getMessage().contains("450") || e.getMessage().toLowerCase().contains("please try again"))) {

                            // 显示450错误的特殊处理
                            Alert alert = new Alert(Alert.AlertType.WARNING);
                            alert.setTitle("邮件发送临时失败");
                            alert.setHeaderText("SMTP 450 错误 - 邮件被临时拒绝");
                            alert.setContentText("邮件服务器临时拒绝了您的邮件发送请求。这通常是由于服务器负载过高或临时的反垃圾邮件策略限制。\n\n" +
                                               "建议稍后重试发送。\n\n错误详情：" + e.getMessage());

                            ButtonType retryButton = new ButtonType("重试");
                            ButtonType cancelButton = new ButtonType("取消");
                            alert.getButtonTypes().setAll(retryButton, cancelButton);

                            Optional<ButtonType> result = alert.showAndWait();
                            if (result.isPresent() && result.get() == retryButton) {
                                // 用户选择重试，重置计数器
                                retryCount = 0;
                                statusLabel.setText("状态：用户选择重试，重新开始发送...");
                                continue;
                            }
                        } else {
                            log.error("邮件发送失败: ", e);
                            // 其他错误，显示一般错误信息
                            showError("邮件发送失败：" + e.getMessage());
                        }
                        return;
                    }
                }
            }
        } finally {
            // 确保无论成功还是失败，都恢复发送按钮的可点击状态
            sendButton.setDisable(false);
        }
    }

    /**
     * 邮件发送成功后保存收件人信息
     */
    private void saveRecipientsAfterSuccess() {
        try {
            RecipientManager recipientManager = RecipientManager.getInstance();

            // 获取收件人和抄送人
            String recipients = recipientField.getText();
            String ccRecipients = ccField.getText();

            // 保存收件人信息
            recipientManager.addRecipientsFromEmail(recipients, ccRecipients);

            // 刷新ComboBox列表
            refreshRecipientComboBox();

        } catch (Exception e) {
            log.error("保存收件人信息失败: ", e);
            // 不影响邮件发送的成功状态，只记录错误
        }
    }

    /**
     * 验证邮件输入
     */
    private boolean validateEmailInput() {
        String recipients = recipientField.getText();
        String subject = subjectField.getText();

        if (recipients == null || recipients.trim().isEmpty()) {
            showError("收件人不能为空！");
            statusLabel.setText("状态：错误，收件人不能为空！");
            return false;
        }
        if (subject == null || subject.trim().isEmpty()) {
            showError("主题不能为空！");
            statusLabel.setText("状态：错误，主题不能为空！");
            return false;
        }
        return true;
    }

    /**
     * 从ComboBox获取发件人邮箱
     */
    private String getSenderEmailFromComboBox() {
        String email = senderEmailComboBox.getValue();
        if (email == null || email.trim().isEmpty()) {
            // 尝试从编辑器获取
            email = senderEmailComboBox.getEditor().getText();
        }
        return email != null ? email.trim() : null;
    }

    /**
     * 发送邮件核心方法
     * 使用保存的配置通过JavaMail API发送邮件
     */
    private void sendEmail() throws Exception {
        // 1. 获取所有输入值
        String recipients = recipientField.getText();
        String cc = ccField.getText();
        String subject = subjectField.getText();
        String body = bodyArea.getText();
        String senderEmail = getSenderEmailFromComboBox();
        ObservableList<String> attachmentDisplays = attachmentListView.getItems();

        // 2. 检查邮件配置
        ConfigManager configManager = ConfigManager.getInstance();
        EmailSettings settings = configManager.getCurrentSettings();

        // 3. 检查是否需要分批发送（当状态显示准备分批发送时）
        if (statusLabel.getText().contains("准备分批发送")) {
            sendEmailInBatches(recipients, cc, subject, body, senderEmail, attachmentDisplays, settings);
            return;
        }

        if (!settings.isValid()) {
            throw new Exception("邮件配置无效！请先在设置中配置SMTP服务器信息。");
        }

        // 4. 使用ComboBox中的发件人邮箱覆盖配置中的邮箱
        if (senderEmail != null && !senderEmail.trim().isEmpty()) {
            settings.setSenderEmail(senderEmail.trim());
        }

        // 5. 发送邮件
        statusLabel.setText("状态：正在发送邮件...");

        List<DirectoryAttachmentProcessor.AttachmentInfo> processedAttachments = new ArrayList<>();

        try {
            // 配置邮件属性
            Properties props = new Properties();
            props.put("mail.smtp.host", settings.getSmtpHost());
            props.put("mail.smtp.port", String.valueOf(settings.getSmtpPort()));
            props.put("mail.smtp.auth", "true");

            if (settings.isEnableSSL()) {
                props.put("mail.smtp.starttls.enable", "true");
                props.put("mail.smtp.ssl.enable", "true");
            }
            props.put("mail.smtp.connectiontimeout", "300000"); // 5分钟
            props.put("mail.smtp.timeout", "300000"); // 5分钟
            props.put("mail.smtp.writetimeout", "300000"); // 5分钟
            // 创建会话
            Session session = Session.getInstance(props, new Authenticator() {
                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(settings.getSenderEmail(), settings.getSenderPassword());
                }
            });

            // 创建邮件消息
            Message message = new MimeMessage(session);

            // 设置发件人
            String senderName = settings.getSenderName();
            if (senderName != null && !senderName.trim().isEmpty()) {
                message.setFrom(new InternetAddress(settings.getSenderEmail(), senderName, "UTF-8"));
            } else {
                message.setFrom(new InternetAddress(settings.getSenderEmail()));
            }

            // 设置收件人
            String[] recipientArray = recipients.split("[;,]");
            InternetAddress[] recipientAddresses = new InternetAddress[recipientArray.length];
            for (int i = 0; i < recipientArray.length; i++) {
                recipientAddresses[i] = new InternetAddress(recipientArray[i].trim());
            }
            message.setRecipients(Message.RecipientType.TO, recipientAddresses);

            // 设置抄送
            if (cc != null && !cc.trim().isEmpty()) {
                String[] ccArray = cc.split("[;,]");
                InternetAddress[] ccAddresses = new InternetAddress[ccArray.length];
                for (int i = 0; i < ccArray.length; i++) {
                    ccAddresses[i] = new InternetAddress(ccArray[i].trim());
                }
                message.setRecipients(Message.RecipientType.CC, ccAddresses);
            }

            // 设置主题
            message.setSubject(subject);

            // 创建邮件内容
            if (attachmentDisplays.isEmpty()) {
                // 没有附件，直接设置文本内容
                message.setText(body);
            } else {
                // 有附件，创建多部分内容
                Multipart multipart = new MimeMultipart();

                // 添加文本内容
                BodyPart textPart = new MimeBodyPart();
                textPart.setContent(body, "text/plain; charset=UTF-8");
                multipart.addBodyPart(textPart);

                // 处理附件（包括目录附件）

                for (String displayText : attachmentDisplays) {
                    String attachmentPath = attachmentPathMap.get(displayText);
                    if (attachmentPath == null) {
                        System.err.println("无法找到附件路径: " + displayText);
                        continue;
                    }
                    File file = new File(attachmentPath);
                    if (file.exists()) {
                        if (file.isDirectory()) {
                            // 处理目录附件
                            try {
                                DirectoryAttachmentProcessor.ProcessResult result =
                                    DirectoryAttachmentProcessor.processDirectory(
                                        attachmentPath,
                                        settings.isCompressDirectories(),
                                        settings.getMaxAttachmentSizeMB()
                                    );
                                processedAttachments.addAll(result.getAttachments());
                                System.out.println("目录处理结果: " + result.getSummary());
                            } catch (Exception e) {
                                System.err.println("处理目录附件失败: " + attachmentPath + " - " + e.getMessage());
                                showError("处理目录附件失败: " + file.getName() + "\n" + e.getMessage());
                                continue;
                            }
                        } else {
                            // 处理普通文件附件
                            processedAttachments.add(new DirectoryAttachmentProcessor.AttachmentInfo(
                                file, file.getName(), file.length(), false));
                        }
                    }
                }

                // 添加所有处理后的附件到邮件
                for (DirectoryAttachmentProcessor.AttachmentInfo attachmentInfo : processedAttachments) {
                    try {
                        BodyPart attachmentPart = new MimeBodyPart();
                        FileDataSource fileDataSource = new FileDataSource(attachmentInfo.getFile());
                        attachmentPart.setDataHandler(new DataHandler(fileDataSource));
                        attachmentPart.setFileName(attachmentInfo.getDisplayName());
                        multipart.addBodyPart(attachmentPart);
                    } catch (Exception e) {
                        System.err.println("添加附件失败: " + attachmentInfo.getDisplayName() + " - " + e.getMessage());
                    }
                }

                message.setContent(multipart);
            }

            // 发送邮件
            Transport.send(message);

            // 清理临时文件
            if (!attachmentDisplays.isEmpty()) {
                DirectoryAttachmentProcessor.cleanupTempFiles(processedAttachments);
            }

            statusLabel.setText("状态：邮件发送成功！");

            // 清理智能处理历史
            clearSmartProcessingHistory();

            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("发送成功");
            alert.setHeaderText(null);
            alert.setContentText("邮件已成功发送！");
            alert.showAndWait();

        } finally {
            // 确保清理临时文件
            if (!processedAttachments.isEmpty()) {
                DirectoryAttachmentProcessor.cleanupTempFiles(processedAttachments);
            }
        }
    }
    
    /**
     * 显示一个错误提示框
     * @param message 错误信息
     */
    private void showError(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("输入错误");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * 显示一个警告提示框
     * @param message 警告信息
     */
    private void showWarning(String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("警告");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * 显示一个信息提示框
     * @param message 信息内容
     */
    private void showInfo(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("信息");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * 显示发送确认对话框
     * @return true表示用户确认发送，false表示取消
     */
    private boolean showSendConfirmationDialog() {
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("确认发送邮件");
        confirmAlert.setHeaderText("请确认邮件信息");

        // 构建确认信息
        StringBuilder content = new StringBuilder();
        content.append("📧 邮件信息确认：\n\n");

        // 发件人信息
        String senderEmail = getSenderEmailFromComboBox();
        content.append("发件人：").append(senderEmail != null ? senderEmail : "未设置").append("\n");

        // 收件人信息
        String recipients = recipientField.getText().trim();
        content.append("收件人：").append(recipients.isEmpty() ? "未设置" : recipients).append("\n");

        // 抄送信息
        String ccRecipients = ccField.getText().trim();
        if (!ccRecipients.isEmpty()) {
            content.append("抄送：").append(ccRecipients).append("\n");
        }

        // 主题
        String subject = subjectField.getText().trim();
        content.append("主题：").append(subject.isEmpty() ? "无主题" : subject).append("\n");

        // 附件信息
        int attachmentCount = attachmentListView.getItems().size();
        content.append("附件数量：").append(attachmentCount).append(" 个\n");

        if (attachmentCount > 0) {
            content.append("\n📎 附件列表：\n");
            for (int i = 0; i < Math.min(attachmentCount, 5); i++) {
                String attachmentDisplay = attachmentListView.getItems().get(i);
                content.append("• ").append(attachmentDisplay).append("\n");
            }
            if (attachmentCount > 5) {
                content.append("• ... 还有 ").append(attachmentCount - 5).append(" 个附件\n");
            }
        }

        content.append("\n确认发送此邮件吗？");

        // 创建可滚动的文本区域
        TextArea textArea = new TextArea(content.toString());
        textArea.setEditable(false);
        textArea.setWrapText(true);
        textArea.setPrefRowCount(15);
        textArea.setPrefColumnCount(60);

        // 设置对话框内容
        confirmAlert.getDialogPane().setContent(textArea);

        // 设置对话框大小
        confirmAlert.getDialogPane().setPrefWidth(600);
        confirmAlert.getDialogPane().setPrefHeight(450);
        confirmAlert.setResizable(true);

        // 自定义按钮
        ButtonType sendButton = new ButtonType("发送邮件", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelButton = new ButtonType("取消", ButtonBar.ButtonData.CANCEL_CLOSE);
        confirmAlert.getButtonTypes().setAll(sendButton, cancelButton);

        // 显示对话框并获取结果
        Optional<ButtonType> result = confirmAlert.showAndWait();
        return result.isPresent() && result.get() == sendButton;
    }

    /**
     * 格式化附件显示文本
     * @param filePath 文件路径
     * @param isDirectory 是否为目录
     * @return 格式化后的显示文本
     */
    private String formatAttachmentDisplay(String filePath, boolean isDirectory) {
        return formatAttachmentDisplay(filePath, isDirectory, false);
    }

    /**
     * 格式化附件显示文本（增强版，包含路径信息）
     * @param filePath 文件路径
     * @param isDirectory 是否为目录
     * @param showPath 是否显示路径信息
     * @return 格式化后的显示文本
     */
    private String formatAttachmentDisplay(String filePath, boolean isDirectory, boolean showPath) {
        File file = new File(filePath);
        ConfigManager configManager = ConfigManager.getInstance();
        EmailSettings settings = configManager.getCurrentSettings();

        String baseDisplay;
        if (isDirectory) {
            try {
                // 预览目录处理结果
                DirectoryAttachmentProcessor.ProcessResult result =
                    DirectoryAttachmentProcessor.processDirectory(filePath, settings.isCompressDirectories(), settings.getMaxAttachmentSizeMB());

                String mode = settings.isCompressDirectories() ? "压缩" : "展开";
                baseDisplay = String.format("[目录-%s] %s (%d个文件, %s)",
                    mode, file.getName(), result.getFileCount(),
                    DirectoryAttachmentProcessor.formatFileSize(result.getTotalSize()));
            } catch (Exception e) {
                baseDisplay = String.format("[目录-错误] %s (处理失败: %s)", file.getName(), e.getMessage());
            }
        } else {
            long size = file.length();
            baseDisplay = String.format("[文件] %s (%s)", file.getName(),
                DirectoryAttachmentProcessor.formatFileSize(size));
        }

        // 如果需要显示路径信息
        if (showPath) {
            String pathInfo = getPathDisplayInfo(filePath);
            return baseDisplay + " - " + pathInfo;
        }

        return baseDisplay;
    }

    /**
     * 获取路径显示信息
     * @param filePath 文件路径
     * @return 路径显示信息
     */
    private String getPathDisplayInfo(String filePath) {
        File file = new File(filePath);

        // 判断是否为模板应用的文件（基于selectedDirectoryPath）
        if (selectedDirectoryPath != null && !selectedDirectoryPath.isEmpty()) {
            File selectedDir = new File(selectedDirectoryPath);
            try {
                String selectedDirCanonical = selectedDir.getCanonicalPath();
                String fileCanonical = file.getCanonicalPath();

                // 如果文件在选定目录下，显示相对路径
                if (fileCanonical.startsWith(selectedDirCanonical)) {
                    String relativePath = fileCanonical.substring(selectedDirCanonical.length());
                    if (relativePath.startsWith(File.separator)) {
                        relativePath = relativePath.substring(1);
                    }
                    return "相对路径: " + relativePath;
                }
            } catch (Exception e) {
                // 如果获取规范路径失败，继续使用绝对路径
            }
        }

        // 默认显示绝对路径
        return "绝对路径: " + file.getAbsolutePath();
    }

    // ==================== 邮件模板相关方法 ====================

    /**
     * 初始化模板ComboBox
     */
    private void initializeTemplateComboBox() {
        try {
            List<EmailTemplate> templates = templateManager.getAllTemplates();
            ObservableList<EmailTemplate> templateItems = FXCollections.observableArrayList(templates);
            templateComboBox.setItems(templateItems);

            // 设置显示格式
            templateComboBox.setCellFactory(listView -> new ListCell<EmailTemplate>() {
                @Override
                protected void updateItem(EmailTemplate template, boolean empty) {
                    super.updateItem(template, empty);
                    if (empty || template == null) {
                        setText(null);
                    } else {
                        setText(template.getDisplayName());
                    }
                }
            });

            templateComboBox.setButtonCell(new ListCell<EmailTemplate>() {
                @Override
                protected void updateItem(EmailTemplate template, boolean empty) {
                    super.updateItem(template, empty);
                    if (empty || template == null) {
                        setText("选择邮件模板（可选）");
                    } else {
                        setText(template.getDisplayName());
                    }
                }
            });

        } catch (Exception e) {
            System.err.println("初始化模板ComboBox失败: " + e.getMessage());
            showError("加载邮件模板失败: " + e.getMessage());
        }
    }

    /**
     * 刷新模板列表
     */
    public void refreshTemplateList() {
        initializeTemplateComboBox();
    }

    /**
     * 应用选中的模板
     */
    private void applySelectedTemplate() {
        EmailTemplate selectedTemplate = templateComboBox.getSelectionModel().getSelectedItem();
        if (selectedTemplate == null) {
            showWarning("请先选择一个邮件模板");
            return;
        }

        if (selectedDirectoryPath == null || selectedDirectoryPath.trim().isEmpty()) {
            showWarning("请先选择一个目录来扫描附件文件");
            return;
        }

        try {
            // 处理模板变量
            Map<String, String> variableValues = processTemplateVariables(selectedTemplate);
            if (variableValues == null) {
                return; // 用户取消了变量输入
            }

            // 应用模板数据到表单（包含变量替换）
            if (selectedTemplate.getRecipients() != null && !selectedTemplate.getRecipients().trim().isEmpty()) {
                recipientField.setText(selectedTemplate.getRecipients());
            }

            if (selectedTemplate.getCcRecipients() != null && !selectedTemplate.getCcRecipients().trim().isEmpty()) {
                ccField.setText(selectedTemplate.getCcRecipients());
            }

            String processedSubject = selectedTemplate.getSubject();
            String processedBody = selectedTemplate.getBody();

            // 替换变量
            if (processedSubject != null && !processedSubject.trim().isEmpty()) {
                processedSubject = TemplateVariableProcessor.replaceVariables(processedSubject, variableValues);
                subjectField.setText(processedSubject);
            }

            if (processedBody != null && !processedBody.trim().isEmpty()) {
                processedBody = TemplateVariableProcessor.replaceVariables(processedBody, variableValues);
                bodyArea.setText(processedBody);
            }
            //清理智能按钮状态和历史
            clearSmartProcessingHistory();

            // 根据模板过滤条件扫描并添加附件
            List<String> includePrefixes = selectedTemplate.getFilePrefixList();
            List<String> includeExtensions = selectedTemplate.getFileExtensionList();
            String excludePattern = selectedTemplate.getExcludeFilePattern();

            FileFilterUtil.FilterResult filterResult = FileFilterUtil.filterFiles(
                selectedDirectoryPath, includePrefixes, includeExtensions,
                excludePattern, true);

            if (filterResult.getFileCount() > 0) {
                // 清空现有附件列表
                attachmentListView.getItems().clear();
                attachmentPathMap.clear();

                // 处理文件添加，包含重复检测
                TemplateApplicationResult applicationResult = processFilesWithDuplicateDetection(filterResult);

                // 显示确认对话框
                showTemplateApplyConfirmation(selectedTemplate, applicationResult);

                // 更新模板使用统计
                templateManager.updateTemplateUsage(selectedTemplate.getId());
                // 如果模板启用了压缩，自动执行压缩处理
                if (selectedTemplate.isEnableCompression()) {
                    handleTemplateCompression(selectedTemplate, applicationResult);
                }else {
                    // 检查附件大小
                    checkAttachmentSizes();
                }



            } else {
                showWarning("在指定目录中未找到匹配的文件\n" + filterResult.getSummary());
            }

        } catch (Exception e) {
            showError("应用模板时发生错误: " + e.getMessage());
        }
    }

    /**
     * 处理模板压缩功能
     * @param template 启用了压缩的模板
     * @param applicationResult 模板应用结果
     */
    private void handleTemplateCompression(EmailTemplate template, TemplateApplicationResult applicationResult) {
        // 收集所有附件文件
        List<File> attachmentFiles = new ArrayList<>();
        for (String displayText : attachmentListView.getItems()) {
            String filePath = attachmentPathMap.get(displayText);
            if (filePath != null) {
                File file = new File(filePath);
                if (file.exists() && file.isFile()) {
                    attachmentFiles.add(file);
                }
            }
        }

        if (attachmentFiles.isEmpty()) {
            statusLabel.setText("状态：模板应用成功，但没有文件需要压缩");
            return;
        }

        // 显示智能处理按钮并设置为压缩模式
        smartProcessButton.setVisible(true);
        smartProcessButton.setText("模板压缩");
        sendButton.setDisable(true);
        statusLabel.setText("状态：模板启用了压缩功能，点击\"模板压缩\"按钮执行压缩");

        // 自动执行压缩（可选：也可以让用户手动点击）
//        Platform.runLater(() -> executeTemplateCompression(template, attachmentFiles));
    }

    /**
     * 执行模板压缩
     * @param template 模板对象
     * @param files 要压缩的文件列表
     */
    private void executeTemplateCompression(EmailTemplate template, List<File> files) {
        String zipFileName = template.getName() +
                           "_" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + ".zip";

        // 确定压缩文件保存位置
        String zipFilePath = getZipFilePath(zipFileName);

        statusLabel.setText("状态：正在压缩模板文件...");

        // 异步压缩
        AttachmentCompressionHandler.compressFilesAsync(
            files.toArray(new File[0]),
            zipFilePath,
            template.getCompressionPassword(),
            () -> onTemplateCompressionSuccess(zipFilePath, template),
            this::onTemplateCompressionError,
            progress -> Platform.runLater(() ->
                statusLabel.setText("状态：压缩进度 " + String.format("%.0f%%", progress * 100)))
        );
        //不能重复点击
        smartProcessButton.setDisable(true);
    }

    /**
     * 模板压缩成功回调
     */
    private void onTemplateCompressionSuccess(String zipFilePath, EmailTemplate template) {
        File zipFile = new File(zipFilePath);

        // 清空现有附件列表
        ObservableList<String> items = attachmentListView.getItems();
        List<File> attachmentFiles = new ArrayList<>();
        for (String displayText : items) {
            String filePath = attachmentPathMap.get(displayText);
            if (filePath != null) {
                File file = new File(filePath);
                if (file.exists() && file.isFile()) {
                    attachmentFiles.add(file);
                }
            }
        }
        attachmentListView.getItems().clear();
        attachmentPathMap.clear();

        // 添加压缩文件到附件列表
//        String displayText = "[模板压缩] " + zipFile.getName() +
//            " (" + AttachmentCompressionHandler.formatFileSize(zipFile.length()) + ")";
//        if (template.hasCompressionPassword()) {
//            displayText += " [密码保护]";
//        }
//
//        attachmentListView.getItems().add(displayText);
//        attachmentPathMap.put(displayText, zipFile.getAbsolutePath());

        String message = "模板文件已成功压缩！\n\n" +
                "压缩文件: " + zipFile.getName() + "\n" +
                "文件大小: " + AttachmentCompressionHandler.formatFileSize(zipFile.length())+ "\n" +
                "保存路径: " + zipFile.getAbsolutePath();
        // 更新处理历史
        processingHistory.recordProcessing("模板压缩", attachmentFiles, message);

        // 更新UI状态
        smartProcessButton.setDisable(false);
        smartProcessButton.setText("查看历史");
        sendButton.setDisable(false);



        if (template.hasCompressionPassword()) {
            message += "\n密码保护: 已启用";
        }

        statusLabel.setText("状态：模板压缩完成，可以发送邮件");
        showInfo(message);
    }

    /**
     * 模板压缩失败回调
     */
    private void onTemplateCompressionError(String errorMessage) {
        smartProcessButton.setVisible(false);
        sendButton.setDisable(false);
        statusLabel.setText("状态：模板压缩失败");
        showError("模板压缩失败: " + errorMessage);
    }

    /**
     * 处理文件添加，包含重复检测逻辑
     * @param filterResult 文件过滤结果
     * @return 模板应用结果
     */
    private TemplateApplicationResult processFilesWithDuplicateDetection(FileFilterUtil.FilterResult filterResult) {
        List<String> skippedDuplicates = new ArrayList<>();
        Set<String> addedFileNames = new HashSet<>();
        int filesAdded = 0;

        for (File file : filterResult.getMatchedFiles()) {
            String fileName = file.getName();
            String filePath = file.getAbsolutePath();

            // 检查是否已存在同名文件
            if (addedFileNames.contains(fileName)) {
                // 记录跳过的重复文件
                skippedDuplicates.add(fileName + " (" + filePath + ")");
                System.out.println("跳过重复文件: " + fileName + " 路径: " + filePath);
                continue;
            }

            // 添加文件到附件列表
            String displayText = formatAttachmentDisplay(filePath, false, true); // 显示路径信息
            attachmentListView.getItems().add(displayText);
            attachmentPathMap.put(displayText, filePath);

            // 记录已添加的文件名
            addedFileNames.add(fileName);
            filesAdded++;
        }

        return new TemplateApplicationResult(
            filterResult.getFileCount(),
            filesAdded,
            skippedDuplicates,
            filterResult
        );
    }

    /**
     * 显示模板应用确认对话框
     */
    private void showTemplateApplyConfirmation(EmailTemplate template, TemplateApplicationResult applicationResult) {
        Alert confirmAlert = new Alert(Alert.AlertType.INFORMATION);
        confirmAlert.setTitle("模板应用成功");
        confirmAlert.setHeaderText("邮件模板 \"" + template.getName() + "\" 已应用");

        StringBuilder content = new StringBuilder();
        content.append("模板信息已填入邮件表单\n\n");

        // 显示文件处理统计
        content.append("附件处理结果:\n");
        content.append("• 扫描到的文件总数: ").append(applicationResult.getTotalFilesFound()).append("\n");
        content.append("• 实际添加的文件数: ").append(applicationResult.getFilesAdded()).append("\n");

        if (applicationResult.hasDuplicates()) {
            content.append("• 跳过的重复文件数: ").append(applicationResult.getSkippedDuplicates().size()).append("\n");
        }

        content.append("\n").append(applicationResult.getFilterResult().getSummary()).append("\n");

        // 显示实际添加的文件列表
        content.append("\n实际添加的文件:\n");
        int maxFiles = Math.min(10, applicationResult.getFilesAdded());
        int fileIndex = 0;

        for (File file : applicationResult.getFilterResult().getMatchedFiles()) {
            if (fileIndex >= maxFiles) break;

            // 只显示未被跳过的文件
            boolean isSkipped = applicationResult.getSkippedDuplicates().stream()
                .anyMatch(skipped -> skipped.startsWith(file.getName() + " ("));

            if (!isSkipped) {
                content.append("• ").append(file.getName()).append("\n");
                fileIndex++;
            }
        }

        if (applicationResult.getFilesAdded() > 10) {
            content.append("... 还有 ").append(applicationResult.getFilesAdded() - 10).append(" 个文件\n");
        }

        // 显示跳过的重复文件
        if (applicationResult.hasDuplicates()) {
            content.append("\n⚠️ 跳过的重复文件:\n");
            for (String skippedFile : applicationResult.getSkippedDuplicates()) {
                content.append("• ").append(skippedFile).append("\n");
            }
            content.append("\n💡 提示: 只保留了每个文件名的第一个文件，跳过了后续的同名文件。");
        }

        // 创建可滚动的文本区域
        TextArea textArea = new TextArea(content.toString());
        textArea.setEditable(false);
        textArea.setWrapText(true);
        textArea.setPrefRowCount(20);
        textArea.setPrefColumnCount(70);

        // 设置对话框内容为TextArea
        confirmAlert.getDialogPane().setContent(textArea);

        // 设置对话框大小
        confirmAlert.getDialogPane().setPrefWidth(700);
        confirmAlert.getDialogPane().setPrefHeight(500);
        confirmAlert.setResizable(true);

        confirmAlert.showAndWait();
    }

    /**
     * 选择目录
     */
    private void selectDirectory() {
        DirectoryChooser directoryChooser = new DirectoryChooser();
        directoryChooser.setTitle("选择要扫描的目录");
        if (recipientField == null) {
            showWarning("请先选择邮件模板");
            return;
        }
        // 如果之前选择过目录，设置为初始目录
        if (selectedDirectoryPath != null) {
            File previousDir = new File(selectedDirectoryPath);
            if (previousDir.exists() && previousDir.isDirectory()) {
                directoryChooser.setInitialDirectory(previousDir);
            }
        }

        Window ownerWindow = selectDirectoryButton.getScene().getWindow();
        File selectedDirectory = directoryChooser.showDialog(ownerWindow);

        if (selectedDirectory != null) {
            selectedDirectoryPath = selectedDirectory.getAbsolutePath();
            statusLabel.setText("状态：已选择目录 \"" + selectedDirectory.getName() + "\"");

            // 启用应用模板按钮
            applyTemplateButton.setDisable(templateComboBox.getSelectionModel().getSelectedItem() == null);
        }
    }

    /**
     * 显示模板管理对话框
     */
    private void showTemplateManagementDialog() {
        try {
            FXMLLoader fxmlLoader = new FXMLLoader(getClass().getResource("/fxml/TemplateManagementView.fxml"));
            Parent root = fxmlLoader.load();

            Stage dialogStage = new Stage();
            //设置宽度和长度
//            dialogStage.setWidth(840);
//            dialogStage.setHeight(900);
            dialogStage.setTitle("邮件模板管理");
            dialogStage.setScene(new Scene(root));
            dialogStage.initModality(Modality.APPLICATION_MODAL);
            dialogStage.initOwner(manageTemplatesButton.getScene().getWindow());

            // 对话框关闭后刷新模板列表
            dialogStage.setOnHidden(event -> refreshTemplateList());

            dialogStage.showAndWait();

        } catch (Exception e) {
            showError("打开模板管理对话框失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // ==================== 模板变量处理相关方法 ====================

    /**
     * 处理模板变量，如果模板包含变量则显示输入对话框
     * @param template 邮件模板
     * @return 变量值映射，如果用户取消则返回null
     */
    private Map<String, String> processTemplateVariables(EmailTemplate template) {
        // 解析模板中定义的变量
        List<TemplateVariable> templateVariables = TemplateVariableProcessor.deserializeVariables(template.getVariables());

        // 从主题和正文中提取变量名
        Set<String> usedVariableNames = TemplateVariableProcessor.extractVariableNames(template.getSubject(), template.getBody());

        // 过滤出实际使用的变量（排除预设变量）
        List<TemplateVariable> usedVariables = new ArrayList<>();
        for (TemplateVariable variable : templateVariables) {
            if (usedVariableNames.contains(variable.getName()) &&
                !TemplateVariableProcessor.isPresetVariable(variable.getName())) {
                usedVariables.add(variable);
            }
        }

        // 如果没有需要用户输入的变量，直接返回空映射
        if (usedVariables.isEmpty()) {
            return new HashMap<>();
        }

        // 显示变量输入对话框
        return showVariableInputDialog(template, usedVariables);
    }

    /**
     * 显示变量输入对话框
     * @param template 邮件模板
     * @param variables 需要输入的变量列表
     * @return 变量值映射，如果用户取消则返回null
     */
    private Map<String, String> showVariableInputDialog(EmailTemplate template, List<TemplateVariable> variables) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/VariableInputView.fxml"));
            Parent root = loader.load();

            VariableInputController controller = loader.getController();
            controller.setTemplateAndVariables(template, variables);

            Stage dialogStage = new Stage();
            dialogStage.setTitle("模板变量输入");
            dialogStage.setScene(new Scene(root));
            dialogStage.initModality(Modality.APPLICATION_MODAL);
            dialogStage.initOwner(applyTemplateButton.getScene().getWindow());
            dialogStage.setResizable(true);

            dialogStage.showAndWait();

            if (controller.isConfirmed()) {
                return controller.getVariableValues();
            } else {
                return null; // 用户取消
            }

        } catch (Exception e) {
            showError("打开变量输入对话框失败: " + e.getMessage());
            log.error("打开变量输入对话框失败: ", e);
            return new HashMap<>(); // 返回空映射以继续处理
        }
    }

    // ==================== 智能附件处理相关方法 ====================

    /**
     * 检查附件大小并显示/隐藏智能处理按钮
     */
    private void checkAttachmentSizes() {
        System.out.println("=== 开始检查附件大小 ===");
        System.out.println("附件列表项数量: " + attachmentListView.getItems().size());

        if (attachmentListView.getItems().isEmpty()) {
            smartProcessButton.setVisible(false);
            sendButton.setDisable(false);
            // 重置处理历史
            processingHistory.reset();
            return;
        }

        ConfigManager configManager = ConfigManager.getInstance();
        EmailSettings settings = configManager.getCurrentSettings();

        // 收集所有附件文件（包括目录中的文件）
        List<File> attachmentFiles = new ArrayList<>();
        long totalSize = 0;

        for (String displayText : attachmentListView.getItems()) {
            String filePath = attachmentPathMap.get(displayText);
            System.out.println("处理附件: " + displayText + " -> " + filePath);

            if (filePath != null) {
                File file = new File(filePath);
                if (file.exists()) {
                    if (file.isFile()) {
                        attachmentFiles.add(file);
                        long fileSize = file.length();
                        totalSize += fileSize;
                        System.out.println("  文件大小: " + AttachmentCompressionHandler.formatFileSize(fileSize));
                    } else if (file.isDirectory()) {
                        // 对于目录，我们需要预处理来获取实际的文件大小
                        try {
                            DirectoryAttachmentProcessor.ProcessResult result =
                                DirectoryAttachmentProcessor.processDirectory(
                                    filePath,
                                    settings.isCompressDirectories(),
                                    settings.getMaxAttachmentSizeMB()
                                );
                            // 添加处理后的文件信息用于大小检查
                            for (DirectoryAttachmentProcessor.AttachmentInfo info : result.getAttachments()) {
                                attachmentFiles.add(info.getFile());
                            }
                        } catch (Exception e) {
                            // 如果处理失败，跳过这个目录
                            System.err.println("处理目录时出错: " + e.getMessage());
                        }
                    }
                }
            }
        }

        if (attachmentFiles.isEmpty()) {
            smartProcessButton.setVisible(false);
            sendButton.setDisable(false);
            System.out.println("没有找到有效的附件文件");
            return;
        }

        System.out.println("总共收集到 " + attachmentFiles.size() + " 个文件");
        System.out.println("总大小: " + AttachmentCompressionHandler.formatFileSize(totalSize));
        System.out.println("单文件限制: " + settings.getMaxIndividualFileSizeMB() + "MB");
        System.out.println("总大小限制: " + settings.getMaxAttachmentSizeMB() + "MB");

        // 验证附件大小
        AttachmentCompressionHandler.AttachmentValidationResult result =
            AttachmentCompressionHandler.validateAttachments(
                attachmentFiles.toArray(new File[0]),
                settings.getMaxIndividualFileSizeMB(),
                settings.getMaxAttachmentSizeMB()
            );

        System.out.println("验证结果:");
        System.out.println("  有超大文件: " + result.hasOversizedFiles());
        System.out.println("  超过总限制: " + result.exceedsTotalLimit());
        System.out.println("  验证总大小: " + AttachmentCompressionHandler.formatFileSize(result.getTotalSize()));


        // 检查是否需要显示智能处理按钮
        boolean shouldShowSmartButton = result.hasOversizedFiles() || result.exceedsTotalLimit() ||
                                       (processingHistory.hasProcessed() && result.exceedsTotalLimit());

        if (shouldShowSmartButton) {
            smartProcessButton.setVisible(true);

            // 判断按钮功能：如果已处理过且仍有大小问题，提供进一步处理选项；否则显示历史
            if (processingHistory.hasProcessed() && result.exceedsTotalLimit()) {
                // 已处理过但总大小仍超限，需要进一步处理（压缩或分批）
                sendButton.setDisable(true);
                smartProcessButton.setText("智能处理");
                statusLabel.setText("状态：总大小仍超限，需要压缩或分批发送");
            } else if (processingHistory.hasProcessed()) {
                // 已处理过且大小正常，可以查看历史
                sendButton.setDisable(false);
                smartProcessButton.setText("查看历史");
                statusLabel.setText("状态：已完成智能处理，点击按钮查看详情");
            } else {
                // 未处理过，需要智能处理
                sendButton.setDisable(true);
                smartProcessButton.setText("智能处理");
                if (result.hasOversizedFiles()) {
                    statusLabel.setText("状态：检测到超大文件，请使用智能处理");
                } else if (result.exceedsTotalLimit()) {
                    statusLabel.setText("状态：附件总大小超限，请使用智能处理");
                }
            }
        } else {
            // 没有大小问题
            if (!processingHistory.hasProcessed()) {
                // 从未处理过，隐藏按钮
                smartProcessButton.setVisible(false);
                processingHistory.reset();
            } else {
                // 已处理过且大小正常，保持按钮可见以查看历史
                smartProcessButton.setVisible(true);
                smartProcessButton.setText("查看历史");
            }
            sendButton.setDisable(false);
            statusLabel.setText("状态：准备就绪");
        }
    }



    /**
     * 处理智能附件处理
     */
    private void handleSmartProcessing() {
        // 添加视觉反馈
        addButtonClickFeedback(smartProcessButton);

        // 检查当前按钮状态
        if ("查看历史".equals(smartProcessButton.getText())) {
            // 按钮显示"查看历史"，显示历史对话框
            showRemovedFilesDialog();
            return;
        }

        if ("模板压缩".equals(smartProcessButton.getText())) {
            // 按钮显示"模板压缩"，重新执行模板压缩
            EmailTemplate selectedTemplate = templateComboBox.getSelectionModel().getSelectedItem();
            if (selectedTemplate != null && selectedTemplate.isEnableCompression()) {
                List<File> attachmentFiles = new ArrayList<>();
                for (String displayText : attachmentListView.getItems()) {
                    String filePath = attachmentPathMap.get(displayText);
                    if (filePath != null) {
                        File file = new File(filePath);
                        if (file.exists() && file.isFile()) {
                            attachmentFiles.add(file);
                        }
                    }
                }
                executeTemplateCompression(selectedTemplate, attachmentFiles);
            }
            return;
        }

        // 按钮显示"智能处理"，执行处理逻辑（无论是否已处理过）

        ConfigManager configManager = ConfigManager.getInstance();
        EmailSettings settings = configManager.getCurrentSettings();

        // 收集所有附件文件（只处理普通文件，目录附件在发送时处理）
        List<File> attachmentFiles = new ArrayList<>();
        for (String displayText : attachmentListView.getItems()) {
            String filePath = attachmentPathMap.get(displayText);
            if (filePath != null) {
                File file = new File(filePath);
                if (file.exists() && file.isFile()) {
                    attachmentFiles.add(file);
                }
            }
        }

        if (attachmentFiles.isEmpty()) {
            showWarning("没有找到有效的附件文件");
            return;
        }

        // 验证附件大小
        AttachmentCompressionHandler.AttachmentValidationResult result =
            AttachmentCompressionHandler.validateAttachments(
                attachmentFiles.toArray(new File[0]),
                settings.getMaxIndividualFileSizeMB(),
                settings.getMaxAttachmentSizeMB()
            );

        boolean hasProcessedOversizedFiles = false;

        // 1. 处理超大文件
        if (result.hasOversizedFiles()) {
            // 记录处理历史
            processingHistory.recordProcessing("移除超大文件", result.getOversizedFiles());

            showOversizedFilesDialog(result.getOversizedFiles());

            // 创建移除文件的ZIP归档
            createRemovedFilesZip(result.getOversizedFiles());

            // 从附件列表中移除超大文件
            removeOversizedFilesFromList(result.getOversizedFiles());

            hasProcessedOversizedFiles = true;

            // 重新验证剩余文件的大小
            List<File> remainingFiles = new ArrayList<>();
            for (String displayText : attachmentListView.getItems()) {
                String filePath = attachmentPathMap.get(displayText);
                if (filePath != null) {
                    File file = new File(filePath);
                    if (file.exists() && file.isFile()) {
                        remainingFiles.add(file);
                    }
                }
            }

            // 重新验证剩余文件
            if (!remainingFiles.isEmpty()) {
                result = AttachmentCompressionHandler.validateAttachments(
                    remainingFiles.toArray(new File[0]),
                    settings.getMaxIndividualFileSizeMB(),
                    settings.getMaxAttachmentSizeMB()
                );
            }
        }

        // 2. 处理总大小超限（在处理超大文件后仍可能存在）
        if (result.exceedsTotalLimit()) {
            // 如果之前没有记录处理历史，现在记录
            if (!hasProcessedOversizedFiles) {
                processingHistory.recordProcessing("处理总大小超限", new ArrayList<>());
            }

            showTotalSizeExceededDialog(result);
        } else {
            // 如果处理了超大文件但总大小现在正常，启用发送按钮
            sendButton.setDisable(false);
            if (hasProcessedOversizedFiles) {
                statusLabel.setText("状态：已移除超大文件，可以发送邮件");
            }
        }

        // 最后重新检查附件大小以更新UI状态
        if (hasProcessedOversizedFiles) {
            checkAttachmentSizes();
        }
    }

    /**
     * 显示超大文件对话框
     */
    private void showOversizedFilesDialog(List<File> oversizedFiles) {
        StringBuilder message = new StringBuilder();
        message.append("以下文件超过单个文件大小限制，已从附件列表中移除：\n\n");

        for (File file : oversizedFiles) {
            message.append("• ").append(file.getName())
                   .append(" (").append(AttachmentCompressionHandler.formatFileSize(file.length())).append(")\n");
        }

        message.append("\n建议：请将超大文件上传到云存储服务（如移动云盘、阿里云盘等），然后在邮件中分享下载链接。");

        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("文件大小超限");
        alert.setHeaderText("检测到超大文件");
        alert.setContentText(message.toString());
        alert.showAndWait();
    }

    /**
     * 显示总大小超限对话框
     */
    private void showTotalSizeExceededDialog(AttachmentCompressionHandler.AttachmentValidationResult result) {
        String message = String.format(
            "附件总大小 (%s) 超过 25MB 限制。\n\n请选择处理方式：",
            AttachmentCompressionHandler.formatFileSize(result.getTotalSize())
        );

        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("附件大小超限");
        alert.setHeaderText("总附件大小超过限制");
        alert.setContentText(message);

        ButtonType compressButton = new ButtonType("压缩附件");
        ButtonType batchButton = new ButtonType("分批发送");
        ButtonType cancelButton = new ButtonType("取消", ButtonBar.ButtonData.CANCEL_CLOSE);

        alert.getButtonTypes().setAll(compressButton, batchButton, cancelButton);

        Optional<ButtonType> result2 = alert.showAndWait();

        if (result2.isPresent()) {
            if (result2.get() == compressButton) {
                handleCompressAttachments(result);
            } else if (result2.get() == batchButton) {
                handleSendInBatches(result);
            }
        }
    }

    /**
     * 处理压缩附件
     */
    private void handleCompressAttachments(AttachmentCompressionHandler.AttachmentValidationResult result) {
        try {
            statusLabel.setText("状态：正在压缩附件...");

            // 创建临时压缩文件
            String zipFileName = "attachments_" + System.currentTimeMillis() + ".zip";
            File tempDir = new File(System.getProperty("java.io.tmpdir"));
            File zipFile = new File(tempDir, zipFileName);

            AttachmentCompressionHandler handler = new AttachmentCompressionHandler();
            File compressedFile = handler.compressFiles(
                result.getValidFiles().toArray(new File[0]),
                zipFile.getAbsolutePath()
            );

            // 检查压缩后的大小
            if (compressedFile.length() <= 25 * 1024 * 1024) {
                // 压缩成功，替换附件列表
                attachmentListView.getItems().clear();
                attachmentPathMap.clear();

                String displayText = "[压缩文件] " + compressedFile.getName() +
                    " (" + AttachmentCompressionHandler.formatFileSize(compressedFile.length()) + ")";
                attachmentListView.getItems().add(displayText);
                attachmentPathMap.put(displayText, compressedFile.getAbsolutePath());

                showPostProcessingConfirmation("附件已成功压缩为单个文件，可以正常发送邮件。");

                // 更新处理历史
                processingHistory.recordProcessing("压缩附件", new ArrayList<>());

            } else {
                compressedFile.delete();
                showError("压缩后文件仍然过大，请选择分批发送。");
            }

        } catch (Exception e) {
            showError("压缩附件失败: " + e.getMessage());
            statusLabel.setText("状态：压缩失败");
        }
    }

    /**
     * 处理分批发送
     */
    private void handleSendInBatches(AttachmentCompressionHandler.AttachmentValidationResult result) {
        showPostProcessingConfirmation("系统将自动将附件分为多个批次发送，每批次不超过 25MB。");

        // 更新处理历史
        processingHistory.recordProcessing("分批发送", new ArrayList<>());

        // 启用发送按钮，发送时会自动分批处理
        sendButton.setDisable(false);
        smartProcessButton.setText("查看历史");
        statusLabel.setText("状态：准备分批发送");
    }

    /**
     * 从附件列表中移除超大文件
     */
    private void removeOversizedFilesFromList(List<File> oversizedFiles) {
        List<String> toRemove = new ArrayList<>();

        for (String displayText : attachmentListView.getItems()) {
            String filePath = attachmentPathMap.get(displayText);
            if (filePath != null) {
                File file = new File(filePath);
                if (oversizedFiles.contains(file)) {
                    toRemove.add(displayText);
                }
            }
        }

        for (String displayText : toRemove) {
            attachmentListView.getItems().remove(displayText);
            attachmentPathMap.remove(displayText);
        }
    }

    /**
     * 显示处理后确认对话框
     */
    private void showPostProcessingConfirmation(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("处理完成");
        alert.setHeaderText("智能处理完成");
        alert.setContentText(message + "\n\n点击确定继续发送邮件。");
        alert.showAndWait();
    }

    /**
     * 添加按钮点击视觉反馈
     */
    private void addButtonClickFeedback(Button button) {
        // 保存原始样式
        String originalStyle = button.getStyle();

        // 添加点击效果
        button.setStyle(originalStyle + "; -fx-background-color: #45a049; -fx-scale-x: 0.95; -fx-scale-y: 0.95;");

        // 200毫秒后恢复原样
        Timeline timeline = new Timeline(new KeyFrame(Duration.millis(200), e -> {
            button.setStyle(originalStyle);
        }));
        timeline.play();

        // 更新状态
        statusLabel.setText("状态：正在处理智能附件功能...");
    }

    /**
     * 显示被移除文件信息对话框
     */
    private void showRemovedFilesDialog() {
        List<RemovedFileInfo> removedFiles = processingHistory.getRemovedFiles();

        if (removedFiles.isEmpty()) {
            // 如果没有被移除的文件，显示处理历史
            showProcessingHistoryDialog();
            return;
        }

        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("智能处理历史");
        alert.setHeaderText("已移除的大文件信息");

        StringBuilder content = new StringBuilder();
        content.append("以下文件在智能处理过程中被移除：\n\n");

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        for (RemovedFileInfo fileInfo : removedFiles) {
            content.append("📄 ").append(fileInfo.getFileName()).append("\n");
            content.append("   大小: ").append(AttachmentCompressionHandler.formatFileSize(fileInfo.getFileSize())).append("\n");
            content.append("   路径: ").append(fileInfo.getFilePath()).append("\n");
            content.append("   原因: ").append(fileInfo.getReason()).append("\n");
            content.append("   时间: ").append(dateFormat.format(new Date(fileInfo.getRemovalTime()))).append("\n\n");
        }

        content.append("💡 建议：将这些大文件上传到云存储服务，然后在邮件中分享下载链接。\n\n");
        content.append("上次处理操作: ").append(processingHistory.getLastProcessingAction()).append("\n");
        content.append("处理时间: ").append(dateFormat.format(new Date(processingHistory.getLastProcessingTime())));

        // 创建可滚动的文本区域
        TextArea textArea = new TextArea(content.toString());
        textArea.setEditable(false);
        textArea.setWrapText(true);
        textArea.setPrefRowCount(15);
        textArea.setPrefColumnCount(60);

        // 设置对话框内容
        alert.getDialogPane().setContent(textArea);

        // 设置对话框大小
        alert.getDialogPane().setPrefWidth(650);
        alert.getDialogPane().setPrefHeight(500);
        alert.setResizable(true);

        alert.showAndWait();

        // 更新状态
        statusLabel.setText("状态：已显示移除文件历史 (" + removedFiles.size() + " 个文件)");
    }

    /**
     * 显示处理历史对话框（当没有被移除文件时）
     */
    private void showProcessingHistoryDialog() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("智能处理历史");
        alert.setHeaderText("处理历史信息");

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        StringBuilder content = new StringBuilder();
        content.append("智能处理状态：已完成\n\n");
        content.append("上次处理操作: ").append(processingHistory.getLastProcessingAction()).append("\n");
        content.append("处理时间: ").append(dateFormat.format(new Date(processingHistory.getLastProcessingTime()))).append("\n\n");

        if (processingHistory.getRemovedFiles().isEmpty()) {
            content.append("✅ 没有文件因大小限制被移除\n");
            content.append("✅ 所有附件都符合大小要求\n\n");
        }

        content.append("当前状态: 可以正常发送邮件");

        alert.setContentText(content.toString());
        alert.showAndWait();

        // 更新状态
        statusLabel.setText("状态：已显示处理历史信息");
    }

    /**
     * 清理智能处理历史并重置UI状态
     */
    private void clearSmartProcessingHistory() {
        // 重置处理历史
        processingHistory.reset();

        // 重置智能处理按钮状态
        smartProcessButton.setText("智能处理");
        smartProcessButton.setVisible(false);

        // 启用发送按钮
        sendButton.setDisable(false);

        // 更新状态标签
        statusLabel.setText("状态：邮件发送完成，已清理处理历史");

        System.out.println("智能处理历史已清理，UI状态已重置");
    }

    /**
     * 创建移除文件的ZIP归档（异步执行）
     * @param removedFiles 被移除的超大文件列表
     */
    private void createRemovedFilesZip(List<File> removedFiles) {
        if (removedFiles.isEmpty()) {
            return;
        }

        // 立即更新状态标签，告知用户正在处理
        Platform.runLater(() -> {
            statusLabel.setText("状态：正在创建移除文件的ZIP归档...");
        });

        // 异步执行ZIP创建
        CompletableFuture.supplyAsync(() -> {
            try {
                // 生成ZIP文件名
                String zipFileName = generateZipFileName();

                // 确定ZIP文件保存位置
                String zipFilePath = getZipFilePath(zipFileName);

                // 创建ZIP文件
                AttachmentCompressionHandler handler = new AttachmentCompressionHandler();
                File zipFile = handler.compressFiles(
                    removedFiles.toArray(new File[0]),
                    zipFilePath
                );

                System.out.println("成功创建移除文件ZIP归档: " + zipFile.getAbsolutePath());
                return zipFile;

            } catch (Exception e) {
                System.err.println("创建移除文件ZIP归档失败: " + e.getMessage());
                throw new RuntimeException(e);
            }
        }).whenComplete((zipFile, throwable) -> {
            // 在JavaFX应用线程中更新UI
            Platform.runLater(() -> {
                if (throwable == null && zipFile != null) {
                    // 成功创建ZIP
                    handleZipCreationSuccess(zipFile);
                } else {
                    // 创建失败
                    handleZipCreationFailure(throwable);
                }
            });
        });
    }

    /**
     * 处理ZIP创建成功
     * @param zipFile 创建的ZIP文件
     */
    private void handleZipCreationSuccess(File zipFile) {
        String successMessage = "已将移除的大文件压缩保存至: " + zipFile.getAbsolutePath();
        statusLabel.setText("状态：" + successMessage);

        // 显示成功通知对话框
        Alert infoAlert = new Alert(Alert.AlertType.INFORMATION);
        infoAlert.setTitle("文件归档成功");
        infoAlert.setHeaderText("超大文件已归档");

        StringBuilder content = new StringBuilder();
        content.append("移除的大文件已成功压缩保存！\n\n");
        content.append("保存位置: ").append(zipFile.getAbsolutePath()).append("\n");
        content.append("文件大小: ").append(AttachmentCompressionHandler.formatFileSize(zipFile.length())).append("\n\n");
        content.append("这些文件已从附件列表中移除，但已保存在ZIP文件中供您参考。\n");
        content.append("您可以稍后解压此文件来访问原始文件。");

        infoAlert.setContentText(content.toString());

        // 设置对话框大小
        infoAlert.getDialogPane().setPrefWidth(500);
        infoAlert.getDialogPane().setPrefHeight(300);
        infoAlert.setResizable(true);

        infoAlert.showAndWait();
    }

    /**
     * 处理ZIP创建失败
     * @param throwable 异常信息
     */
    private void handleZipCreationFailure(Throwable throwable) {
        String errorMessage = "创建移除文件ZIP归档失败";
        if (throwable != null && throwable.getCause() != null) {
            errorMessage += ": " + throwable.getCause().getMessage();
        }

        statusLabel.setText("状态：ZIP归档创建失败，但智能处理已完成");

        Alert errorAlert = new Alert(Alert.AlertType.WARNING);
        errorAlert.setTitle("归档失败");
        errorAlert.setHeaderText("无法创建ZIP归档");

        StringBuilder content = new StringBuilder();
        content.append("移除的文件无法保存为ZIP归档，但智能处理已成功完成。\n\n");
        content.append("可能的原因:\n");
        content.append("• 磁盘空间不足\n");
        content.append("• 目录权限不足\n");
        content.append("• 文件被其他程序占用\n\n");
        if (throwable != null && throwable.getCause() != null) {
            content.append("错误详情: ").append(throwable.getCause().getMessage());
        }

        errorAlert.setContentText(content.toString());

        // 设置对话框大小
        errorAlert.getDialogPane().setPrefWidth(450);
        errorAlert.getDialogPane().setPrefHeight(280);
        errorAlert.setResizable(true);

        errorAlert.showAndWait();
    }

    /**
     * 生成ZIP文件名
     * @return ZIP文件名
     */
    private String generateZipFileName() {
        // 获取当前时间戳
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String timestamp = dateFormat.format(new Date());

        // 尝试获取当前应用的模板名称
        EmailTemplate selectedTemplate = templateComboBox.getSelectionModel().getSelectedItem();
        if (selectedTemplate != null && selectedTemplate.getName() != null && !selectedTemplate.getName().trim().isEmpty()) {
            // 清理模板名称，移除不适合文件名的字符
            String cleanTemplateName = selectedTemplate.getName()
                .replaceAll("[\\\\/:*?\"<>|]", "_")  // 替换Windows不允许的字符
                .trim();
            return cleanTemplateName + "_large_files_" + timestamp + ".zip";
        } else {
            return "removed_oversized_files_" + timestamp + ".zip";
        }
    }

    /**
     * 获取ZIP文件保存路径
     * @param zipFileName ZIP文件名
     * @return 完整的ZIP文件路径
     */
    private String getZipFilePath(String zipFileName) {
        // 优先使用当前选择的目录
        if (selectedDirectoryPath != null && !selectedDirectoryPath.trim().isEmpty()) {
            File selectedDir = new File(selectedDirectoryPath);
            if (selectedDir.exists() && selectedDir.isDirectory()) {
                return new File(selectedDir, zipFileName).getAbsolutePath();
            }
        }

        // 如果没有选择目录或目录无效，使用用户主目录
        String userHome = System.getProperty("user.home");
        return new File(userHome, zipFileName).getAbsolutePath();
    }

    /**
     * 分批发送邮件
     */
    private void sendEmailInBatches(String recipients, String cc, String subject, String body,
                                  String senderEmail, ObservableList<String> attachmentDisplays,
                                  EmailSettings settings) throws Exception {

        // 收集所有附件文件
        List<File> allFiles = new ArrayList<>();
        for (String displayText : attachmentDisplays) {
            String filePath = attachmentPathMap.get(displayText);
            if (filePath != null) {
                File file = new File(filePath);
                if (file.exists() && file.isFile()) {
                    allFiles.add(file);
                }
            }
        }

        if (allFiles.isEmpty()) {
            // 没有附件，直接发送
            sendSingleEmail(recipients, cc, subject, body, senderEmail, new ArrayList<>(), settings, 1, 1);
            return;
        }

        // 按大小分组文件
        List<List<File>> batches = new ArrayList<>();
        List<File> currentBatch = new ArrayList<>();
        long currentBatchSize = 0;
        long maxBatchSize = settings.getMaxAttachmentSizeMB() * 1024L * 1024L;

        for (File file : allFiles) {
            long fileSize = file.length();

            if (currentBatchSize + fileSize > maxBatchSize && !currentBatch.isEmpty()) {
                batches.add(new ArrayList<>(currentBatch));
                currentBatch.clear();
                currentBatchSize = 0;
            }

            currentBatch.add(file);
            currentBatchSize += fileSize;
        }

        if (!currentBatch.isEmpty()) {
            batches.add(currentBatch);
        }

        // 发送每个批次
        for (int i = 0; i < batches.size(); i++) {
            String batchSubject = subject;
            if (batches.size() > 1) {
                batchSubject += " (第 " + (i + 1) + " 部分，共 " + batches.size() + " 部分)";
            }

            statusLabel.setText("状态：正在发送第 " + (i + 1) + " 批邮件...");

            List<DirectoryAttachmentProcessor.AttachmentInfo> batchAttachments = new ArrayList<>();
            for (File file : batches.get(i)) {
                batchAttachments.add(new DirectoryAttachmentProcessor.AttachmentInfo(
                    file, file.getName(), file.length(), false));
            }

            sendSingleEmail(recipients, cc, batchSubject, body, senderEmail, batchAttachments, settings, i + 1, batches.size());

            // 批次间稍作延迟
            if (i < batches.size() - 1) {
                Thread.sleep(1000);
            }
        }

        statusLabel.setText("状态：所有邮件批次发送完成！");

        // 清理智能处理历史
        clearSmartProcessingHistory();
    }

    /**
     * 发送单封邮件
     */
    private void sendSingleEmail(String recipients, String cc, String subject, String body,
                               String senderEmail, List<DirectoryAttachmentProcessor.AttachmentInfo> attachments,
                               EmailSettings settings, int batchNumber, int totalBatches) throws Exception {

        try {
            // 配置邮件属性
            Properties props = new Properties();
            props.put("mail.smtp.host", settings.getSmtpHost());
            props.put("mail.smtp.port", String.valueOf(settings.getSmtpPort()));
            props.put("mail.smtp.auth", "true");

            if (settings.isEnableSSL()) {
                props.put("mail.smtp.starttls.enable", "true");
                props.put("mail.smtp.ssl.enable", "true");
            }
            props.put("mail.smtp.connectiontimeout", "300000");
            props.put("mail.smtp.timeout", "300000");
            props.put("mail.smtp.writetimeout", "300000");

            // 创建会话
            Session session = Session.getInstance(props, new Authenticator() {
                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(settings.getSenderEmail(), settings.getSenderPassword());
                }
            });

            // 创建邮件消息
            MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress(settings.getSenderEmail(), settings.getSenderName()));

            // 设置收件人
            String[] recipientArray = recipients.split("[;,]");
            InternetAddress[] recipientAddresses = new InternetAddress[recipientArray.length];
            for (int i = 0; i < recipientArray.length; i++) {
                recipientAddresses[i] = new InternetAddress(recipientArray[i].trim());
            }
            message.setRecipients(Message.RecipientType.TO, recipientAddresses);

            // 设置抄送
            if (cc != null && !cc.trim().isEmpty()) {
                String[] ccArray = cc.split("[;,]");
                InternetAddress[] ccAddresses = new InternetAddress[ccArray.length];
                for (int i = 0; i < ccArray.length; i++) {
                    ccAddresses[i] = new InternetAddress(ccArray[i].trim());
                }
                message.setRecipients(Message.RecipientType.CC, ccAddresses);
            }

            // 设置主题
            message.setSubject(subject);

            // 创建邮件内容
            if (attachments.isEmpty()) {
                message.setText(body);
            } else {
                Multipart multipart = new MimeMultipart();

                // 添加文本内容
                BodyPart textPart = new MimeBodyPart();
                textPart.setContent(body, "text/plain; charset=UTF-8");
                multipart.addBodyPart(textPart);

                // 添加附件
                for (DirectoryAttachmentProcessor.AttachmentInfo attachmentInfo : attachments) {
                    try {
                        BodyPart attachmentPart = new MimeBodyPart();
                        FileDataSource fileDataSource = new FileDataSource(attachmentInfo.getFile());
                        attachmentPart.setDataHandler(new DataHandler(fileDataSource));
                        attachmentPart.setFileName(attachmentInfo.getDisplayName());
                        multipart.addBodyPart(attachmentPart);
                    } catch (Exception e) {
                        System.err.println("添加附件失败: " + attachmentInfo.getDisplayName() + " - " + e.getMessage());
                    }
                }

                message.setContent(multipart);
            }

            // 发送邮件
            Transport.send(message);

        } catch (Exception e) {
            throw new Exception("发送第 " + batchNumber + " 批邮件失败: " + e.getMessage(), e);
        }
    }

    // ==================== 智能处理历史记录类 ====================

    /**
     * 智能处理历史记录类
     */
    private static class SmartProcessingHistory {
        private boolean hasProcessed = false;
        private List<RemovedFileInfo> removedFiles = new ArrayList<>();
        private String lastProcessingAction = "";
        private long lastProcessingTime = 0;

        public void reset() {
            hasProcessed = false;
            removedFiles.clear();
            lastProcessingAction = "";
            lastProcessingTime = 0;
        }

        public void recordProcessing(String action, List<File> removed) {
            hasProcessed = true;
            lastProcessingAction = action;
            lastProcessingTime = System.currentTimeMillis();

            // 记录被移除的文件
            for (File file : removed) {
                removedFiles.add(new RemovedFileInfo(
                    file.getName(),
                    file.getAbsolutePath(),
                    file.length(),
                    "超过单文件大小限制",
                    lastProcessingTime
                ));
            }
        }

        public void recordProcessing(String action, List<File> removed, String reason) {
            hasProcessed = true;
            lastProcessingAction = reason;
            lastProcessingTime = System.currentTimeMillis();

            // 记录被移除的文件
            for (File file : removed) {
                removedFiles.add(new RemovedFileInfo(
                        file.getName(),
                        file.getAbsolutePath(),
                        file.length(),
                        "模板压缩",
                        lastProcessingTime
                ));
            }
        }

        public boolean hasProcessed() { return hasProcessed; }
        public List<RemovedFileInfo> getRemovedFiles() { return removedFiles; }
        public String getLastProcessingAction() { return lastProcessingAction; }
        public long getLastProcessingTime() { return lastProcessingTime; }
    }

    /**
     * 被移除文件信息类
     */
    private static class RemovedFileInfo {
        private final String fileName;
        private final String filePath;
        private final long fileSize;
        private final String reason;
        private final long removalTime;

        public RemovedFileInfo(String fileName, String filePath, long fileSize, String reason, long removalTime) {
            this.fileName = fileName;
            this.filePath = filePath;
            this.fileSize = fileSize;
            this.reason = reason;
            this.removalTime = removalTime;
        }

        public String getFileName() { return fileName; }
        public String getFilePath() { return filePath; }
        public long getFileSize() { return fileSize; }
        public String getReason() { return reason; }
        public long getRemovalTime() { return removalTime; }
    }

    /**
     * 获取ZIP文件保存路径（用于模板压缩）
     * @param zipFileName ZIP文件名
     * @return 完整的文件路径
     */
//    private String getZipFilePath(String zipFileName) {
//        // 优先使用选择的目录
//        if (selectedDirectoryPath != null && new File(selectedDirectoryPath).exists()) {
//            return new File(selectedDirectoryPath, zipFileName).getAbsolutePath();
//        }
//        // 备用：用户主目录
//        return new File(System.getProperty("user.home"), zipFileName).getAbsolutePath();
//    }

}