package com.bboss.report.controller;

import com.bboss.report.model.RecipientInfo;
import com.bboss.report.util.RecipientManager;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.stage.Stage;
import lombok.extern.slf4j.Slf4j;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 收件人管理对话框控制器
 * 负责处理收件人管理界面的用户交互
 * 
 * <AUTHOR>
 * @create 2025/7/1
 * @since 1.0.0
 */
@Slf4j
public class RecipientManagementController {

    // 搜索和筛选控件
    @FXML private TextField searchField;
    @FXML private ComboBox<String> tagFilterComboBox;
    @FXML private Button searchButton;
    @FXML private Button clearFilterButton;

    // 收件人列表控件
    @FXML private TableView<RecipientInfo> recipientTableView;
    @FXML private TableColumn<RecipientInfo, String> emailColumn;
    @FXML private TableColumn<RecipientInfo, String> displayNameColumn;
    @FXML private TableColumn<RecipientInfo, String> tagsColumn;
    @FXML private TableColumn<RecipientInfo, Integer> useCountColumn;
    @FXML private TableColumn<RecipientInfo, String> lastUsedColumn;
    @FXML private TableColumn<RecipientInfo, String> typeColumn;

    // 收件人操作控件
    @FXML private Button addRecipientButton;
    @FXML private Button editRecipientButton;
    @FXML private Button deleteRecipientButton;
    @FXML private Label recipientCountLabel;

    // 标签管理控件
    @FXML private ListView<String> tagListView;
    @FXML private TextField newTagField;
    @FXML private Button addTagButton;
    @FXML private Button editTagButton;
    @FXML private Button deleteTagButton;

    // 标签分配控件
    @FXML private ComboBox<String> assignTagComboBox;
    @FXML private Button addTagToRecipientButton;
    @FXML private Button removeTagFromRecipientButton;

    // 底部控件
    @FXML private Label statusLabel;
    @FXML private Button exportButton;
    @FXML private Button importButton;
    @FXML private Button closeButton;

    private RecipientManager recipientManager;
    private ObservableList<RecipientInfo> allRecipients;
    private ObservableList<RecipientInfo> filteredRecipients;

    @FXML
    public void initialize() {
        recipientManager = RecipientManager.getInstance();
        
        // 初始化表格列
        initializeTableColumns();
        
        // 绑定事件
        bindEvents();
        
        // 加载数据
        loadData();
        
        statusLabel.setText("收件人管理界面已加载");
    }

    /**
     * 初始化表格列
     */
    private void initializeTableColumns() {
        emailColumn.setCellValueFactory(new PropertyValueFactory<>("email"));
        displayNameColumn.setCellValueFactory(new PropertyValueFactory<>("displayName"));
        tagsColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getTagsAsString()));
        useCountColumn.setCellValueFactory(new PropertyValueFactory<>("useCount"));
        lastUsedColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getFormattedLastUsed()));
        typeColumn.setCellValueFactory(new PropertyValueFactory<>("type"));
        
        // 设置表格选择模式
        recipientTableView.getSelectionModel().setSelectionMode(SelectionMode.MULTIPLE);
    }

    /**
     * 绑定事件
     */
    private void bindEvents() {
        // 搜索和筛选
        searchButton.setOnAction(event -> performSearch());
        clearFilterButton.setOnAction(event -> clearFilters());
        searchField.setOnAction(event -> performSearch());
        tagFilterComboBox.setOnAction(event -> performSearch());

        // 收件人操作
        addRecipientButton.setOnAction(event -> addRecipient());
        editRecipientButton.setOnAction(event -> editRecipient());
        deleteRecipientButton.setOnAction(event -> deleteRecipient());

        // 标签管理
        addTagButton.setOnAction(event -> addTag());
        editTagButton.setOnAction(event -> editTag());
        deleteTagButton.setOnAction(event -> deleteTag());
        newTagField.setOnAction(event -> addTag());

        // 标签分配
        addTagToRecipientButton.setOnAction(event -> addTagToRecipient());
        removeTagFromRecipientButton.setOnAction(event -> removeTagFromRecipient());

        // 底部按钮
        exportButton.setOnAction(event -> exportRecipients());
        importButton.setOnAction(event -> importRecipients());
        closeButton.setOnAction(event -> closeDialog());

        // 表格选择变化
        recipientTableView.getSelectionModel().selectedItemProperty().addListener(
            (obs, oldSelection, newSelection) -> updateButtonStates());
    }

    /**
     * 加载数据
     */
    private void loadData() {
        // 加载收件人数据
        List<RecipientInfo> recipients = recipientManager.getAllRecipients();
        allRecipients = FXCollections.observableArrayList(recipients);
        filteredRecipients = FXCollections.observableArrayList(recipients);
        recipientTableView.setItems(filteredRecipients);
        
        // 加载标签数据
        Set<String> tags = recipientManager.getAvailableTags();
        tagListView.setItems(FXCollections.observableArrayList(tags));
        
        // 更新筛选ComboBox
        ObservableList<String> tagFilterItems = FXCollections.observableArrayList();
        tagFilterItems.add("全部");
        tagFilterItems.addAll(tags);
        tagFilterComboBox.setItems(tagFilterItems);
        tagFilterComboBox.setValue("全部");
        
        // 更新标签分配ComboBox
        assignTagComboBox.setItems(FXCollections.observableArrayList(tags));
        
        // 更新计数
        updateRecipientCount();
        updateButtonStates();
    }

    /**
     * 执行搜索
     */
    private void performSearch() {
        String searchText = searchField.getText();
        String selectedTag = tagFilterComboBox.getValue();
        
        List<RecipientInfo> filtered = allRecipients.stream()
            .filter(recipient -> {
                // 文本搜索
                boolean matchesText = searchText == null || searchText.trim().isEmpty() ||
                    recipient.getEmail().toLowerCase().contains(searchText.toLowerCase()) ||
                    recipient.getDisplayName().toLowerCase().contains(searchText.toLowerCase());
                
                // 标签筛选
                boolean matchesTag = selectedTag == null || "全部".equals(selectedTag) ||
                    recipient.hasTag(selectedTag);
                
                return matchesText && matchesTag;
            })
            .collect(Collectors.toList());
        
        filteredRecipients.setAll(filtered);
        updateRecipientCount();
        statusLabel.setText("搜索完成，找到 " + filtered.size() + " 个匹配的收件人");
    }

    /**
     * 清除筛选
     */
    private void clearFilters() {
        searchField.clear();
        tagFilterComboBox.setValue("全部");
        filteredRecipients.setAll(allRecipients);
        updateRecipientCount();
        statusLabel.setText("已清除所有筛选条件");
    }

    /**
     * 添加收件人
     */
    private void addRecipient() {
        TextInputDialog dialog = new TextInputDialog();
        dialog.setTitle("添加收件人");
        dialog.setHeaderText("添加新收件人");
        dialog.setContentText("请输入邮箱地址:");
        
        Optional<String> result = dialog.showAndWait();
        if (result.isPresent() && !result.get().trim().isEmpty()) {
            String email = result.get().trim();
            
            // 检查是否已存在
            if (recipientManager.getRecipient(email) != null) {
                showError("该邮箱地址已存在！");
                return;
            }
            
            // 添加收件人
            recipientManager.addOrUpdateRecipient(email, email, "TO");
            loadData();
            statusLabel.setText("已添加收件人: " + email);
        }
    }

    /**
     * 编辑收件人
     */
    private void editRecipient() {
        RecipientInfo selected = recipientTableView.getSelectionModel().getSelectedItem();
        if (selected == null) {
            showError("请先选择要编辑的收件人！");
            return;
        }
        
        TextInputDialog dialog = new TextInputDialog(selected.getDisplayName());
        dialog.setTitle("编辑收件人");
        dialog.setHeaderText("编辑收件人信息");
        dialog.setContentText("显示名称:");
        
        Optional<String> result = dialog.showAndWait();
        if (result.isPresent()) {
            selected.setDisplayName(result.get().trim());
            recipientTableView.refresh();
            statusLabel.setText("已更新收件人信息");
        }
    }

    /**
     * 删除收件人
     */
    private void deleteRecipient() {
        List<RecipientInfo> selected = recipientTableView.getSelectionModel().getSelectedItems();
        if (selected.isEmpty()) {
            showError("请先选择要删除的收件人！");
            return;
        }
        
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("确认删除");
        confirmAlert.setHeaderText("删除收件人");
        confirmAlert.setContentText("确定要删除选中的 " + selected.size() + " 个收件人吗？");
        
        Optional<ButtonType> result = confirmAlert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            for (RecipientInfo recipient : selected) {
                recipientManager.removeRecipient(recipient.getEmail());
            }
            loadData();
            statusLabel.setText("已删除 " + selected.size() + " 个收件人");
        }
    }

    /**
     * 添加标签
     */
    private void addTag() {
        String tagName = newTagField.getText();
        if (tagName == null || tagName.trim().isEmpty()) {
            showError("请输入标签名称！");
            return;
        }
        
        recipientManager.addTag(tagName.trim());
        newTagField.clear();
        loadData();
        statusLabel.setText("已添加标签: " + tagName.trim());
    }

    /**
     * 编辑标签
     */
    private void editTag() {
        String selected = tagListView.getSelectionModel().getSelectedItem();
        if (selected == null) {
            showError("请先选择要编辑的标签！");
            return;
        }
        
        TextInputDialog dialog = new TextInputDialog(selected);
        dialog.setTitle("编辑标签");
        dialog.setHeaderText("编辑标签名称");
        dialog.setContentText("标签名称:");
        
        Optional<String> result = dialog.showAndWait();
        if (result.isPresent() && !result.get().trim().isEmpty()) {
            String newName = result.get().trim();
            if (!newName.equals(selected)) {
                // 删除旧标签，添加新标签
                recipientManager.removeTag(selected);
                recipientManager.addTag(newName);
                loadData();
                statusLabel.setText("已更新标签: " + selected + " -> " + newName);
            }
        }
    }

    /**
     * 删除标签
     */
    private void deleteTag() {
        String selected = tagListView.getSelectionModel().getSelectedItem();
        if (selected == null) {
            showError("请先选择要删除的标签！");
            return;
        }
        
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("确认删除");
        confirmAlert.setHeaderText("删除标签");
        confirmAlert.setContentText("确定要删除标签 \"" + selected + "\" 吗？\n这将从所有收件人中移除该标签。");
        
        Optional<ButtonType> result = confirmAlert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            recipientManager.removeTag(selected);
            loadData();
            statusLabel.setText("已删除标签: " + selected);
        }
    }

    /**
     * 为收件人添加标签
     */
    private void addTagToRecipient() {
        List<RecipientInfo> selectedRecipients = recipientTableView.getSelectionModel().getSelectedItems();
        String selectedTag = assignTagComboBox.getValue();
        
        if (selectedRecipients.isEmpty()) {
            showError("请先选择收件人！");
            return;
        }
        
        if (selectedTag == null || selectedTag.trim().isEmpty()) {
            showError("请选择要添加的标签！");
            return;
        }
        
        for (RecipientInfo recipient : selectedRecipients) {
            recipientManager.addTagToRecipient(recipient.getEmail(), selectedTag);
        }
        
        loadData();
        statusLabel.setText("已为 " + selectedRecipients.size() + " 个收件人添加标签: " + selectedTag);
    }

    /**
     * 从收件人移除标签
     */
    private void removeTagFromRecipient() {
        List<RecipientInfo> selectedRecipients = recipientTableView.getSelectionModel().getSelectedItems();
        String selectedTag = assignTagComboBox.getValue();
        
        if (selectedRecipients.isEmpty()) {
            showError("请先选择收件人！");
            return;
        }
        
        if (selectedTag == null || selectedTag.trim().isEmpty()) {
            showError("请选择要移除的标签！");
            return;
        }
        
        for (RecipientInfo recipient : selectedRecipients) {
            recipientManager.removeTagFromRecipient(recipient.getEmail(), selectedTag);
        }
        
        loadData();
        statusLabel.setText("已从 " + selectedRecipients.size() + " 个收件人移除标签: " + selectedTag);
    }

    /**
     * 导出收件人
     */
    private void exportRecipients() {
        // TODO: 实现导出功能
        showInfo("导出功能", "导出功能正在开发中，敬请期待！");
    }

    /**
     * 导入收件人
     */
    private void importRecipients() {
        // TODO: 实现导入功能
        showInfo("导入功能", "导入功能正在开发中，敬请期待！");
    }

    /**
     * 关闭对话框
     */
    private void closeDialog() {
        Stage stage = (Stage) closeButton.getScene().getWindow();
        stage.close();
    }

    /**
     * 更新收件人计数
     */
    private void updateRecipientCount() {
        recipientCountLabel.setText("共 " + filteredRecipients.size() + " 个收件人");
    }

    /**
     * 更新按钮状态
     */
    private void updateButtonStates() {
        boolean hasSelection = !recipientTableView.getSelectionModel().getSelectedItems().isEmpty();
        editRecipientButton.setDisable(!hasSelection);
        deleteRecipientButton.setDisable(!hasSelection);
        addTagToRecipientButton.setDisable(!hasSelection);
        removeTagFromRecipientButton.setDisable(!hasSelection);
        
        boolean hasTagSelection = tagListView.getSelectionModel().getSelectedItem() != null;
        editTagButton.setDisable(!hasTagSelection);
        deleteTagButton.setDisable(!hasTagSelection);
    }

    /**
     * 显示错误信息
     */
    private void showError(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("错误");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * 显示信息
     */
    private void showInfo(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
