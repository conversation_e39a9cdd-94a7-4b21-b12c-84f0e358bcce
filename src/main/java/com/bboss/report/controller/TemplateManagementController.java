package com.bboss.report.controller;

import com.bboss.report.model.EmailTemplate;
import com.bboss.report.model.TemplateVariable;
import com.bboss.report.util.FileFilterUtil;
import com.bboss.report.util.TemplateManager;
import com.bboss.report.util.TemplateVariableProcessor;
import javafx.beans.value.ChangeListener;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.geometry.Insets;
import javafx.scene.control.Alert;
import javafx.scene.control.Alert.AlertType;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonBar;
import javafx.scene.control.ButtonType;
import javafx.scene.control.CheckBox;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Dialog;
import javafx.scene.control.Label;
import javafx.scene.control.ListView;
import javafx.scene.control.PasswordField;
import javafx.scene.control.ScrollPane;
import javafx.scene.control.TextArea;
import javafx.scene.control.TextField;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.stage.DirectoryChooser;
import javafx.stage.FileChooser;
import javafx.stage.Stage;
import javafx.stage.Window;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 邮件模板管理控制器
 * 负责模板的创建、编辑、删除等操作
 * 
 * <AUTHOR>
 * @create 2025/7/2
 * @since 1.0.0
 */
@Slf4j
public class TemplateManagementController {
    
    // FXML 控件注入
    @FXML private ListView<EmailTemplate> templateListView;
    @FXML private TextField templateNameField;
    @FXML private TextField templateDescriptionField;
    @FXML private TextArea recipientsField;
    @FXML private TextArea ccRecipientsField;
    @FXML private TextField subjectField;
    @FXML private TextArea bodyField;
    @FXML private TextField filePrefixesField;
    @FXML private TextField fileExtensionsField;
    @FXML private Button newTemplateButton;
    @FXML private Button editTemplateButton;
    @FXML private Button deleteTemplateButton;
    @FXML private Button duplicateTemplateButton;
    @FXML private Button selectExtensionsButton;
    @FXML private Button testTemplateButton;
    @FXML private Button saveTemplateButton;
    @FXML private Button clearFormButton;
    @FXML private Button importTemplatesButton;
    @FXML private Button exportTemplatesButton;
    @FXML private Button closeButton;

    // 变量管理相关控件
    @FXML private Button manageVariablesButton;
    @FXML private Label variableCountLabel;
    @FXML private TextArea variablePreviewArea;
    @FXML private Label statusLabel;

    // 文件压缩相关控件
    @FXML private CheckBox enableCompressionCheckBox;
    @FXML private VBox compressionPasswordContainer;
    @FXML private PasswordField compressionPasswordField;

    // 排除过滤相关控件
    @FXML private TextArea excludeFilePatternField;
    @FXML private Button validatePatternButton;
    @FXML private Button selectPatternButton;
    @FXML private Label patternValidationLabel;
    
    private TemplateManager templateManager;
    private EmailTemplate currentTemplate;
    private boolean isEditing = false;
    private List<TemplateVariable> currentVariables = new ArrayList<>();

    // 模板选择监听器
    private ChangeListener<EmailTemplate> templateSelectionListener;
    
    /**
     * FXML加载后自动调用的初始化方法
     */
    @FXML
    public void initialize() {
        templateManager = TemplateManager.getInstance();

        // 绑定按钮事件
        newTemplateButton.setOnAction(event -> createNewTemplate());
        editTemplateButton.setOnAction(event -> editSelectedTemplate());
        deleteTemplateButton.setOnAction(event -> deleteSelectedTemplate());
        duplicateTemplateButton.setOnAction(event -> duplicateSelectedTemplate());
        selectExtensionsButton.setOnAction(event -> selectFileExtensions());
        testTemplateButton.setOnAction(event -> testTemplateFilter());
        saveTemplateButton.setOnAction(event -> saveCurrentTemplate());
        clearFormButton.setOnAction(event -> clearForm());
        importTemplatesButton.setOnAction(event -> importTemplates());
        exportTemplatesButton.setOnAction(event -> exportTemplates());
        closeButton.setOnAction(event -> closeDialog());

        // 绑定变量管理按钮事件
        manageVariablesButton.setOnAction(event -> manageVariables());

        // 绑定压缩功能事件
        setupCompressionControls();

        // 绑定排除模式功能事件
        setupExcludePatternControls();

        // 创建并设置模板列表选择监听器
        templateSelectionListener = (observable, oldValue, newValue) -> loadTemplateToForm(newValue);
        templateListView.getSelectionModel().selectedItemProperty().addListener(templateSelectionListener);

        // 初始化界面
        loadTemplateList();
        clearForm();
        updateButtonStates();

        // 添加表单字段监听器以实时验证
        setupFormValidation();
    }



    /**
     * 设置压缩功能控件
     */
    private void setupCompressionControls() {
        // 监听压缩复选框状态变化
        enableCompressionCheckBox.selectedProperty().addListener((observable, oldValue, newValue) -> {
            // 根据复选框状态显示/隐藏密码输入区域
            compressionPasswordContainer.setVisible(newValue);
            compressionPasswordContainer.setManaged(newValue);

            // 如果取消压缩，清空密码字段
            if (!newValue) {
                compressionPasswordField.clear();
            }
        });
    }

    /**
     * 设置排除模式功能控件
     */
    private void setupExcludePatternControls() {
        // 绑定验证按钮事件
        validatePatternButton.setOnAction(event -> validateExcludePattern());

        // 绑定选择模式按钮事件
        selectPatternButton.setOnAction(event -> showPatternSelectionDialog());

        // 监听文本变化，实时验证
        excludeFilePatternField.textProperty().addListener((observable, oldValue, newValue) -> {
            validatePatternInRealTime(newValue);
        });
    }

    /**
     * 验证排除模式
     */
    private void validateExcludePattern() {
        String pattern = excludeFilePatternField.getText().trim();

        if (pattern.isEmpty()) {
            patternValidationLabel.setText("✓ 空模式（不排除任何文件）");
            patternValidationLabel.setStyle("-fx-text-fill: #008000;");
            return;
        }

        String error = FileFilterUtil.getRegexValidationError(pattern);
        if (error == null) {
            patternValidationLabel.setText("✓ 正则表达式语法正确");
            patternValidationLabel.setStyle("-fx-text-fill: #008000;");
        } else {
            patternValidationLabel.setText("✗ " + error);
            patternValidationLabel.setStyle("-fx-text-fill: #ff0000;");
        }
    }

    /**
     * 实时验证模式
     */
    private void validatePatternInRealTime(String pattern) {
        if (pattern == null || pattern.trim().isEmpty()) {
            patternValidationLabel.setText("");
            return;
        }

        if (FileFilterUtil.isValidRegexPattern(pattern)) {
            patternValidationLabel.setText("✓");
            patternValidationLabel.setStyle("-fx-text-fill: #008000;");
        } else {
            patternValidationLabel.setText("✗");
            patternValidationLabel.setStyle("-fx-text-fill: #ff0000;");
        }
    }

    /**
     * 显示模式选择对话框
     */
    private void showPatternSelectionDialog() {
        Alert dialog = new Alert(AlertType.INFORMATION);
        dialog.setTitle("选择排除模式");
        dialog.setHeaderText("常用排除模式");

        VBox content = new VBox(10);
        content.setPrefWidth(500);

        Map<String, String> examples = FileFilterUtil.getExcludePatternExamples();
        for (Map.Entry<String, String> entry : examples.entrySet()) {
            HBox row = new HBox(10);
            Button selectBtn = new Button("选择");
            selectBtn.setOnAction(e -> {
                excludeFilePatternField.setText(entry.getKey());
                dialog.close();
            });

            Label patternLabel = new Label(entry.getKey());
            patternLabel.setStyle("-fx-font-family: monospace; -fx-font-weight: bold;");

            Label descLabel = new Label(entry.getValue());
            descLabel.setStyle("-fx-text-fill: #666666;");

            row.getChildren().addAll(selectBtn, patternLabel, new Label(" - "), descLabel);
            content.getChildren().add(row);
        }

        ScrollPane scrollPane = new ScrollPane(content);
        scrollPane.setFitToWidth(true);
        scrollPane.setPrefHeight(300);

        dialog.getDialogPane().setContent(scrollPane);
        dialog.getDialogPane().setPrefWidth(600);
        dialog.getDialogPane().setPrefHeight(400);
        dialog.setResizable(true);

        dialog.showAndWait();
    }

    /**
     * 设置表单验证监听器
     */
    private void setupFormValidation() {
        // 为关键字段添加文本变化监听器
        templateNameField.textProperty().addListener((observable, oldValue, newValue) -> updateSaveButtonState());
        subjectField.textProperty().addListener((observable, oldValue, newValue) -> updateSaveButtonState());
    }

    /**
     * 更新保存按钮状态
     */
    private void updateSaveButtonState() {
        if (!isEditing) {
            saveTemplateButton.setDisable(true);
            return; // 如果不在编辑状态，禁用保存按钮
        }

        // 检查必填字段
        String nameText = templateNameField.getText();
        String subjectText = subjectField.getText();
        boolean hasName = nameText != null && !nameText.trim().isEmpty();
        boolean hasSubject = subjectText != null && !subjectText.trim().isEmpty();

        // 修复：优化模板名称重复检查逻辑
        boolean nameValid = true;
        if (hasName) {
            String templateName = nameText.trim();
            try {
                EmailTemplate existingTemplate = templateManager.getTemplateByName(templateName);
                if (existingTemplate != null) {
                    // 对于新建模板，currentTemplate的ID为null
                    if (currentTemplate == null || currentTemplate.getId() == null) {
                        // 新建模板时，如果找到同名模板则无效
                        nameValid = false;
                    } else {
                        // 编辑现有模板时，只要不是其他模板的名称就有效
                        nameValid = existingTemplate.getId().equals(currentTemplate.getId());
                    }
                }
            } catch (Exception e) {
                // 如果查询出错，假设名称有效
                nameValid = true;
                System.err.println("检查模板名称时发生错误: " + e.getMessage());
            }
        }

        // 只有在所有必填字段都有效时才启用保存按钮
        boolean canSave = hasName && hasSubject && nameValid;
        saveTemplateButton.setDisable(!canSave);
        //saveTemplateButton.setDisable(false);

        // 更新状态提示（提供更详细的信息）
        if (isEditing) {
            if (!hasName) {
                statusLabel.setText("状态：请输入模板名称");
            } else if (!hasSubject) {
                statusLabel.setText("状态：请输入邮件主题");
            } else if (!nameValid) {
                statusLabel.setText("状态：模板名称 \"" + nameText.trim() + "\" 已存在，请使用其他名称");
            } else {
                statusLabel.setText("状态：表单验证通过，可以保存");
            }
        }
    }

    /**
     * 加载模板列表
     */
    private void loadTemplateList() {
        try {
            List<EmailTemplate> templates = templateManager.getAllTemplates();
            ObservableList<EmailTemplate> templateItems = FXCollections.observableArrayList(templates);
            templateListView.setItems(templateItems);

            statusLabel.setText("状态：已加载 " + templates.size() + " 个模板");
        } catch (Exception e) {
            showError("加载模板列表失败: " + e.getMessage());
        }
    }

    /**
     * 安全地刷新模板列表，避免触发选择监听器
     */
    private void refreshTemplateListSafely() {
        // 临时禁用选择监听器
        templateListView.getSelectionModel().selectedItemProperty().removeListener(templateSelectionListener);

        // 刷新列表
        loadTemplateList();

        // 重新启用选择监听器
        templateListView.getSelectionModel().selectedItemProperty().addListener(templateSelectionListener);
    }
    
    /**
     * 将选中的模板加载到表单
     */
    private void loadTemplateToForm(EmailTemplate template) {
        if (template == null) {
            clearForm();
            return;
        }
        
        currentTemplate = template;
        isEditing = false;
        
        templateNameField.setText(template.getName());
        templateDescriptionField.setText(template.getDescription());
        recipientsField.setText(template.getRecipients());
        ccRecipientsField.setText(template.getCcRecipients());
        subjectField.setText(template.getSubject());
        bodyField.setText(template.getBody());
        filePrefixesField.setText(template.getFilePrefixes());
        fileExtensionsField.setText(template.getFileExtensions());

        // 加载排除模式
        excludeFilePatternField.setText(template.getExcludeFilePattern() != null ? template.getExcludeFilePattern() : "");
        validatePatternInRealTime(excludeFilePatternField.getText());

        // 加载压缩设置
        enableCompressionCheckBox.setSelected(template.isEnableCompression());
        compressionPasswordField.setText(template.getCompressionPassword() != null ? template.getCompressionPassword() : "");
        compressionPasswordContainer.setVisible(template.isEnableCompression());
        compressionPasswordContainer.setManaged(template.isEnableCompression());

        // 加载变量定义
        currentVariables = TemplateVariableProcessor.deserializeVariables(template.getVariables());
        updateVariablePreview();
        
        // 设置表单为只读状态
        setFormEditable(false);
        updateButtonStates();
        
        statusLabel.setText("状态：已选择模板 \"" + template.getName() + "\"");
    }
    
    /**
     * 创建新模板
     */
    private void createNewTemplate() {
        // 修复：确保新建模板的状态正确
        currentTemplate = new EmailTemplate();
        isEditing = true;

        // 清空表单字段并取消模板列表选择
        clearFormFields();
        templateListView.getSelectionModel().clearSelection();
        
        setFormEditable(true);
        updateButtonStates();
        updateSaveButtonState(); // 立即更新保存按钮状态

        templateNameField.requestFocus();
        statusLabel.setText("状态：正在创建新模板，请填写模板名称和邮件主题");
    }
    
    /**
     * 编辑选中的模板
     */
    private void editSelectedTemplate() {
        EmailTemplate selected = templateListView.getSelectionModel().getSelectedItem();
        if (selected == null) {
            showWarning("请先选择要编辑的模板");
            return;
        }
        
        currentTemplate = selected;
        isEditing = true;
        
        setFormEditable(true);
        updateButtonStates();
        updateSaveButtonState(); // 立即更新保存按钮状态

        templateNameField.requestFocus();
        statusLabel.setText("状态：正在编辑模板 \"" + selected.getName() + "\"");
    }
    
    /**
     * 删除选中的模板
     */
    private void deleteSelectedTemplate() {
        EmailTemplate selected = templateListView.getSelectionModel().getSelectedItem();
        if (selected == null) {
            showWarning("请先选择要删除的模板");
            return;
        }
        
        Alert confirmAlert = new Alert(AlertType.CONFIRMATION);
        confirmAlert.setTitle("确认删除");
        confirmAlert.setHeaderText("删除模板");
        confirmAlert.setContentText("确定要删除模板 \"" + selected.getName() + "\" 吗？\n此操作不可撤销。");
        
        Optional<ButtonType> result = confirmAlert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            try {
                boolean success = templateManager.deleteTemplate(selected.getId());
                if (success) {
                    refreshTemplateListSafely();
                    clearForm();
                    statusLabel.setText("状态：模板 \"" + selected.getName() + "\" 已删除");
                } else {
                    showError("删除模板失败");
                }
            } catch (Exception e) {
                showError("删除模板时发生错误: " + e.getMessage());
            }
        }
    }
    
    /**
     * 复制选中的模板
     */
    private void duplicateSelectedTemplate() {
        EmailTemplate selected = templateListView.getSelectionModel().getSelectedItem();
        if (selected == null) {
            showWarning("请先选择要复制的模板");
            return;
        }
        
        // 创建新模板副本
        EmailTemplate duplicate = new EmailTemplate();
        duplicate.setName(selected.getName() + " - 副本");
        duplicate.setDescription(selected.getDescription());
        duplicate.setRecipients(selected.getRecipients());
        duplicate.setCcRecipients(selected.getCcRecipients());
        duplicate.setSubject(selected.getSubject());
        duplicate.setBody(selected.getBody());
        duplicate.setFilePrefixes(selected.getFilePrefixes());
        duplicate.setFileExtensions(selected.getFileExtensions());
        duplicate.setExcludeFilePattern(selected.getExcludeFilePattern());
        duplicate.setEnableCompression(selected.isEnableCompression());
        duplicate.setCompressionPassword(selected.getCompressionPassword());
        duplicate.setVariables(selected.getVariables());

        currentTemplate = duplicate;
        isEditing = true;
        
        loadTemplateToForm(duplicate);
        setFormEditable(true);
        updateButtonStates();
        updateSaveButtonState(); // 立即更新保存按钮状态

        templateNameField.requestFocus();
        templateNameField.selectAll();
        statusLabel.setText("状态：正在创建模板副本");
    }
    
    /**
     * 选择文件扩展名
     */
    private void selectFileExtensions() {
        List<String> commonExtensions = FileFilterUtil.getCommonFileExtensions();
        List<String> currentExtensions = getCurrentExtensionList();
        
        // 创建多选对话框
        Dialog<List<String>> dialog = new Dialog<>();
        dialog.setTitle("选择文件扩展名");
        dialog.setHeaderText("请选择要包含的文件扩展名：");
        
        // 创建复选框列表
        VBox vbox = new VBox(5);
        List<CheckBox> checkBoxes = new ArrayList<>();
        
        for (String ext : commonExtensions) {
            CheckBox checkBox = new CheckBox(ext);
            checkBox.setSelected(currentExtensions.contains(ext));
            checkBoxes.add(checkBox);
            vbox.getChildren().add(checkBox);
        }
        
        ScrollPane scrollPane = new ScrollPane(vbox);
        scrollPane.setPrefHeight(200);
        dialog.getDialogPane().setContent(scrollPane);
        
        // 添加按钮
        dialog.getDialogPane().getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);
        
        // 设置结果转换器
        dialog.setResultConverter(buttonType -> {
            if (buttonType == ButtonType.OK) {
                List<String> selectedExtensions = new ArrayList<>();
                for (CheckBox checkBox : checkBoxes) {
                    if (checkBox.isSelected()) {
                        selectedExtensions.add(checkBox.getText());
                    }
                }
                return selectedExtensions;
            }
            return null;
        });
        
        Optional<List<String>> result = dialog.showAndWait();
        if (result.isPresent()) {
            String extensionsText = String.join(",", result.get());
            fileExtensionsField.setText(extensionsText);
        }
    }
    
    /**
     * 获取当前扩展名列表
     */
    private List<String> getCurrentExtensionList() {
        String extensionsText = fileExtensionsField.getText();
        if (extensionsText == null || extensionsText.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return Arrays.asList(extensionsText.split(","));
    }
    
    /**
     * 测试模板过滤条件
     */
    private void testTemplateFilter() {
        // 选择测试目录
        DirectoryChooser directoryChooser = new DirectoryChooser();
        directoryChooser.setTitle("选择测试目录");
        
        Window ownerWindow = testTemplateButton.getScene().getWindow();
        File selectedDirectory = directoryChooser.showDialog(ownerWindow);
        
        if (selectedDirectory != null) {
            try {
                List<String> includePrefixes = parsePrefixes(filePrefixesField.getText());
                List<String> includeExtensions = parseExtensions(fileExtensionsField.getText());
                String excludePattern = excludeFilePatternField.getText().trim();

                // 验证正则表达式
                if (!excludePattern.isEmpty() && !FileFilterUtil.isValidRegexPattern(excludePattern)) {
                    showError("排除模式正则表达式语法错误，请检查后重试");
                    return;
                }

                FileFilterUtil.FilterResult result = FileFilterUtil.filterFiles(
                    selectedDirectory.getAbsolutePath(), includePrefixes, includeExtensions,
                    excludePattern, true);
                
                // 显示测试结果
                Alert resultAlert = new Alert(AlertType.INFORMATION);
                resultAlert.setTitle("过滤测试结果");
                resultAlert.setHeaderText("文件过滤测试完成");
                resultAlert.setContentText(result.getSummary());
                resultAlert.showAndWait();
                
                statusLabel.setText("状态：测试完成，找到 " + result.getFileCount() + " 个匹配文件");
                
            } catch (Exception e) {
                showError("测试过滤条件时发生错误: " + e.getMessage());
            }
        }
    }
    
    /**
     * 解析前缀字符串
     */
    private List<String> parsePrefixes(String prefixesText) {
        if (prefixesText == null || prefixesText.trim().isEmpty()) {
            return new ArrayList<>();
        }
        List<String> prefixes = new ArrayList<>();
        for (String prefix : prefixesText.split(",")) {
            String trimmed = prefix.trim();
            if (!trimmed.isEmpty()) {
                prefixes.add(trimmed);
            }
        }
        return prefixes;
    }
    
    /**
     * 解析扩展名字符串
     */
    private List<String> parseExtensions(String extensionsText) {
        if (extensionsText == null || extensionsText.trim().isEmpty()) {
            return new ArrayList<>();
        }
        List<String> extensions = new ArrayList<>();
        for (String extension : extensionsText.split(",")) {
            String trimmed = extension.trim();
            if (!trimmed.isEmpty()) {
                // 移除可能的点号前缀
                if (trimmed.startsWith(".")) {
                    trimmed = trimmed.substring(1);
                }
                extensions.add(trimmed);
            }
        }
        return extensions;
    }
    
    /**
     * 保存当前模板
     */
    private void saveCurrentTemplate() {
        if (!validateForm()) {
            return;
        }
        
        try {
            // 更新模板数据
            currentTemplate.setName(templateNameField.getText().trim());
            currentTemplate.setDescription(templateDescriptionField.getText().trim());
            currentTemplate.setRecipients(recipientsField.getText().trim());
            currentTemplate.setCcRecipients(ccRecipientsField.getText().trim());
            currentTemplate.setSubject(subjectField.getText().trim());
            currentTemplate.setBody(bodyField.getText().trim());
            currentTemplate.setFilePrefixes(filePrefixesField.getText().trim());
            currentTemplate.setFileExtensions(fileExtensionsField.getText().trim());
            currentTemplate.setExcludeFilePattern(excludeFilePatternField.getText().trim());
            currentTemplate.setEnableCompression(enableCompressionCheckBox.isSelected());
            currentTemplate.setCompressionPassword(compressionPasswordField.getText().trim().isEmpty() ?
                null : compressionPasswordField.getText().trim());
            currentTemplate.setVariables(TemplateVariableProcessor.serializeVariables(currentVariables));
            currentTemplate.setLastModified(LocalDateTime.now());

            boolean success = templateManager.saveTemplate(currentTemplate);
            if (success) {
                refreshTemplateListSafely();
                setFormEditable(false);
                isEditing = false;
                updateButtonStates();

                // 选中刚保存的模板（这时监听器已经重新启用，但不会触发清空表单）
                templateListView.getSelectionModel().select(currentTemplate);

                statusLabel.setText("状态：模板 \"" + currentTemplate.getName() + "\" 保存成功");
            } else {
                showError("保存模板失败");
            }
        } catch (Exception e) {
            showError("保存模板时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 验证表单数据
     */
    private boolean validateForm() {
        if (templateNameField.getText() == null || templateNameField.getText().trim().isEmpty()) {
            showWarning("请输入模板名称");
            templateNameField.requestFocus();
            return false;
        }
        
        if (subjectField.getText() == null || subjectField.getText().trim().isEmpty()) {
            showWarning("请输入邮件主题");
            subjectField.requestFocus();
            return false;
        }
        
        // 检查模板名称是否重复（编辑现有模板时除外）
        String templateName = templateNameField.getText().trim();
        EmailTemplate existingTemplate = templateManager.getTemplateByName(templateName);
        if (existingTemplate != null && !existingTemplate.equals(currentTemplate)) {
            showWarning("模板名称 \"" + templateName + "\" 已存在，请使用其他名称");
            templateNameField.requestFocus();
            return false;
        }
        
        return true;
    }
    
    /**
     * 清空表单字段（不改变编辑状态）
     */
    private void clearFormFields() {
        templateNameField.clear();
        templateDescriptionField.clear();
        recipientsField.clear();
        ccRecipientsField.clear();
        subjectField.clear();
        bodyField.clear();
        filePrefixesField.clear();
        fileExtensionsField.clear();

        // 清空排除模式
        excludeFilePatternField.clear();
        patternValidationLabel.setText("");

        // 清空压缩设置
        enableCompressionCheckBox.setSelected(false);
        compressionPasswordField.clear();
        compressionPasswordContainer.setVisible(false);
        compressionPasswordContainer.setManaged(false);

        // 清空变量
        currentVariables.clear();
        updateVariablePreview();
    }

    /**
     * 清空表单并重置状态
     */
    private void clearForm() {
        // 修复：如果正在编辑，先确认是否要清空
        if (isEditing) {
            Alert confirmAlert = new Alert(AlertType.CONFIRMATION);
            confirmAlert.setTitle("确认清空");
            confirmAlert.setHeaderText("清空表单");
            confirmAlert.setContentText("当前正在编辑模板，确定要清空表单吗？未保存的更改将丢失。");
            
            Optional<ButtonType> result = confirmAlert.showAndWait();
            if (!result.isPresent() || result.get() != ButtonType.OK) {
                return; // 用户取消清空操作
            }
        }

        clearFormFields();

        currentTemplate = null;
        isEditing = false;
        setFormEditable(false);
        updateButtonStates();

        templateListView.getSelectionModel().clearSelection();
        statusLabel.setText("状态：表单已清空");
    }
    
    /**
     * 设置表单是否可编辑
     */
    private void setFormEditable(boolean editable) {
        templateNameField.setEditable(editable);
        templateDescriptionField.setEditable(editable);
        recipientsField.setEditable(editable);
        ccRecipientsField.setEditable(editable);
        subjectField.setEditable(editable);
        bodyField.setEditable(editable);
        filePrefixesField.setEditable(editable);
        fileExtensionsField.setEditable(editable);

        // 控制排除模式的可编辑状态
        excludeFilePatternField.setEditable(editable);
        validatePatternButton.setDisable(!editable);
        selectPatternButton.setDisable(!editable);

        // 控制压缩设置的可编辑状态
        enableCompressionCheckBox.setDisable(!editable);
        compressionPasswordField.setEditable(editable);

        selectExtensionsButton.setDisable(!editable);
        testTemplateButton.setDisable(!editable);
    }
    
    /**
     * 更新按钮状态
     */
    private void updateButtonStates() {
        boolean hasSelection = templateListView.getSelectionModel().getSelectedItem() != null;

        editTemplateButton.setDisable(!hasSelection || isEditing);
        deleteTemplateButton.setDisable(!hasSelection || isEditing);
        duplicateTemplateButton.setDisable(!hasSelection || isEditing);

        // 保存按钮的状态由 updateSaveButtonState() 方法单独管理
        if (!isEditing) {
            saveTemplateButton.setDisable(true);
        }
        // 如果正在编辑，则由 updateSaveButtonState() 根据表单验证结果来决定

        // 修复：允许在编辑状态下清空表单
        clearFormButton.setDisable(false);
        newTemplateButton.setDisable(isEditing);
    }
    
    /**
     * 导入模板
     */
    private void importTemplates() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("选择要导入的模板文件");
        fileChooser.getExtensionFilters().addAll(
            new FileChooser.ExtensionFilter("XML文件", "*.xml"),
            new FileChooser.ExtensionFilter("所有文件", "*.*")
        );

        // 设置初始目录为用户主目录
        fileChooser.setInitialDirectory(new File(System.getProperty("user.home")));

        Window window = templateListView.getScene().getWindow();
        File selectedFile = fileChooser.showOpenDialog(window);

        if (selectedFile != null) {
            try {
                List<EmailTemplate> importedTemplates = templateManager.importTemplatesFromXML(selectedFile);

                if (importedTemplates == null || importedTemplates.isEmpty()) {
                    showError("导入失败：文件格式不正确或文件为空");
                    return;
                }

                // 显示导入确认对话框
                showImportConfirmationDialog(importedTemplates);

            } catch (Exception e) {
                showError("导入失败：" + e.getMessage());
            }
        }
    }

    /**
     * 显示导入确认对话框
     */
    private void showImportConfirmationDialog(List<EmailTemplate> templates) {
        Dialog<ButtonType> dialog = new Dialog<>();
        dialog.setTitle("确认导入");
        dialog.setHeaderText("发现 " + templates.size() + " 个模板");

        // 创建内容区域
        VBox content = new VBox(10);
        content.setPrefWidth(500);

        Label infoLabel = new Label("以下模板将被导入：");
        content.getChildren().add(infoLabel);

        // 模板列表
        ListView<String> templateListView = new ListView<>();
        templateListView.setPrefHeight(200);
        ObservableList<String> templateNames = FXCollections.observableArrayList();

        for (EmailTemplate template : templates) {
            String displayText = template.getName();
            if (template.getDescription() != null && !template.getDescription().trim().isEmpty()) {
                displayText += " - " + template.getDescription();
            }
            templateNames.add(displayText);
        }
        templateListView.setItems(templateNames);
        content.getChildren().add(templateListView);

        // 选项
        CheckBox overwriteCheckBox = new CheckBox("覆盖已存在的同名模板");
        overwriteCheckBox.setSelected(false);
        content.getChildren().add(overwriteCheckBox);

        ScrollPane scrollPane = new ScrollPane(content);
        scrollPane.setFitToWidth(true);
        scrollPane.setPrefHeight(300);
        dialog.getDialogPane().setContent(scrollPane);

        // 按钮
        dialog.getDialogPane().getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);

        Optional<ButtonType> result = dialog.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            performImport(templates, overwriteCheckBox.isSelected());
        }
    }

    /**
     * 执行导入操作
     */
    private void performImport(List<EmailTemplate> templates, boolean overwriteExisting) {
        try {
            int successCount = templateManager.batchSaveTemplates(templates, overwriteExisting);

            if (successCount > 0) {
                refreshTemplateListSafely();
                statusLabel.setText("状态：成功导入 " + successCount + " 个模板");
                showInfo("导入完成！成功导入 " + successCount + " 个模板。");
            } else {
                showWarning("没有模板被导入。可能所有模板都已存在，请选择覆盖选项重试。");
            }
        } catch (Exception e) {
            showError("导入过程中发生错误：" + e.getMessage());
        }
    }

    /**
     * 导出模板
     */
    private void exportTemplates() {
        List<EmailTemplate> allTemplates = templateManager.getAllTemplates();

        if (allTemplates.isEmpty()) {
            showWarning("没有可导出的模板");
            return;
        }

        // 显示导出选择对话框
        showExportSelectionDialog(allTemplates);
    }

    /**
     * 显示导出选择对话框
     */
    private void showExportSelectionDialog(List<EmailTemplate> allTemplates) {
        Dialog<ButtonType> dialog = new Dialog<>();
        dialog.setTitle("选择要导出的模板");
        dialog.setHeaderText("请选择要导出的模板");

        VBox content = new VBox(10);
        content.setPrefWidth(500);

        Label infoLabel = new Label("选择要导出的模板（可多选）：");
        content.getChildren().add(infoLabel);

        // 创建带复选框的列表
        ListView<CheckBox> templateCheckBoxList = new ListView<>();
        templateCheckBoxList.setPrefHeight(300);
        ObservableList<CheckBox> checkBoxes = FXCollections.observableArrayList();

        for (EmailTemplate template : allTemplates) {
            CheckBox checkBox = new CheckBox(template.getName());
            checkBox.setUserData(template);
            checkBox.setSelected(true); // 默认全选
            checkBoxes.add(checkBox);
        }
        templateCheckBoxList.setItems(checkBoxes);
        content.getChildren().add(templateCheckBoxList);

        // 全选/全不选按钮
        HBox buttonBox = new HBox(10);
        Button selectAllButton = new Button("全选");
        Button selectNoneButton = new Button("全不选");

        selectAllButton.setOnAction(e -> checkBoxes.forEach(cb -> cb.setSelected(true)));
        selectNoneButton.setOnAction(e -> checkBoxes.forEach(cb -> cb.setSelected(false)));

        buttonBox.getChildren().addAll(selectAllButton, selectNoneButton);
        content.getChildren().add(buttonBox);

        ScrollPane scrollPane = new ScrollPane(content);
        scrollPane.setFitToWidth(true);
        scrollPane.setPrefHeight(400);
        dialog.getDialogPane().setContent(scrollPane);

        dialog.getDialogPane().getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);

        Optional<ButtonType> result = dialog.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            List<EmailTemplate> selectedTemplates = new ArrayList<>();
            for (CheckBox checkBox : checkBoxes) {
                if (checkBox.isSelected()) {
                    selectedTemplates.add((EmailTemplate) checkBox.getUserData());
                }
            }

            if (selectedTemplates.isEmpty()) {
                showWarning("请至少选择一个模板进行导出");
                return;
            }

            performExport(selectedTemplates);
        }
    }

    /**
     * 执行导出操作
     */
    private void performExport(List<EmailTemplate> templates) {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("保存导出文件");
        fileChooser.getExtensionFilters().add(
            new FileChooser.ExtensionFilter("XML文件", "*.xml")
        );

        // 设置默认文件名
        String defaultFileName = "email_templates_" +
            java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) +
            ".xml";
        fileChooser.setInitialFileName(defaultFileName);
        fileChooser.setInitialDirectory(new File(System.getProperty("user.home")));

        Window window = templateListView.getScene().getWindow();
        File saveFile = fileChooser.showSaveDialog(window);

        if (saveFile != null) {
            try {
                boolean success = templateManager.exportTemplatesToXML(templates, saveFile);

                if (success) {
                    statusLabel.setText("状态：成功导出 " + templates.size() + " 个模板到 " + saveFile.getName());
                    showInfo("导出成功！\n文件保存位置：" + saveFile.getAbsolutePath());
                } else {
                    showError("导出失败，请检查文件路径和权限");
                }
            } catch (Exception e) {
                showError("导出过程中发生错误：" + e.getMessage());
            }
        }
    }

    /**
     * 关闭对话框
     */
    private void closeDialog() {
        if (isEditing) {
            Alert confirmAlert = new Alert(AlertType.CONFIRMATION);
            confirmAlert.setTitle("确认关闭");
            confirmAlert.setHeaderText("有未保存的更改");
            confirmAlert.setContentText("当前有未保存的模板更改，确定要关闭吗？");
            
            Optional<ButtonType> result = confirmAlert.showAndWait();
            if (!result.isPresent() || result.get() != ButtonType.OK) {
                return;
            }
        }
        
        Stage stage = (Stage) closeButton.getScene().getWindow();
        stage.close();
    }
    
    /**
     * 显示错误信息
     */
    private void showError(String message) {
        Alert alert = new Alert(AlertType.ERROR);
        alert.setTitle("错误");
        alert.setHeaderText("操作失败");
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    /**
     * 显示警告信息
     */
    private void showWarning(String message) {
        Alert alert = new Alert(AlertType.WARNING);
        alert.setTitle("警告");
        alert.setHeaderText("输入验证");
        alert.setContentText(message);
        alert.showAndWait();
    }
    


    /**
     * 显示信息
     */
    private void showInfo(String message) {
        Alert alert = new Alert(AlertType.INFORMATION);
        alert.setTitle("信息");
        alert.setHeaderText("提示");
        alert.setContentText(message);
        alert.showAndWait();
    }

    // ==================== 变量管理相关方法 ====================

    /**
     * 管理模板变量
     */
    private void manageVariables() {
        // 创建变量管理对话框
        Dialog<List<TemplateVariable>> dialog = new Dialog<>();
        dialog.setTitle("管理模板变量");
        dialog.setHeaderText("定义模板中可使用的变量");

        // 创建变量管理界面
        VBox content = createVariableManagementContent();
        dialog.getDialogPane().setContent(content);

        // 添加按钮
        ButtonType okButtonType = new ButtonType("确定", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelButtonType = new ButtonType("取消", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(okButtonType, cancelButtonType);

        // 设置对话框大小
        dialog.getDialogPane().setPrefWidth(600);
        dialog.getDialogPane().setPrefHeight(500);
        dialog.setResizable(true);

        // 设置结果转换器
        dialog.setResultConverter(buttonType -> {
            if (buttonType == okButtonType) {
                return new ArrayList<>(currentVariables);
            }
            return null;
        });

        Optional<List<TemplateVariable>> result = dialog.showAndWait();
        if (result.isPresent()) {
            currentVariables = result.get();
            updateVariablePreview();
            statusLabel.setText("状态：变量定义已更新");
        }
    }



    /**
     * 更新变量预览
     */
    private void updateVariablePreview() {
        variableCountLabel.setText("变量数量: " + currentVariables.size());

        if (currentVariables.isEmpty()) {
            variablePreviewArea.setText("暂无定义变量\n\n提示：点击<管理变量>按钮可以添加模板变量");
        } else {

            String preview = TemplateVariableProcessor.generateVariableUsageExample(currentVariables);

            variablePreviewArea.setText(preview);
        }
    }

    /**
     * 创建变量管理界面内容
     */
    private VBox createVariableManagementContent() {
        VBox content = new VBox(10);
        content.setPadding(new Insets(15));

        // 变量列表
        ListView<TemplateVariable> variableListView = new ListView<>();
        variableListView.getItems().addAll(currentVariables);
        variableListView.setPrefHeight(200);

        // 按钮区域
        HBox buttonBox = new HBox(10);
        Button addButton = new Button("添加变量");
        Button editButton = new Button("编辑变量");
        Button deleteButton = new Button("删除变量");

        buttonBox.getChildren().addAll(addButton, editButton, deleteButton);

        // 变量详情区域
        TextArea detailArea = new TextArea();
        detailArea.setEditable(false);
        detailArea.setPrefRowCount(5);
        detailArea.setPromptText("选择变量查看详情...");

        // 绑定事件
        addButton.setOnAction(e -> addNewVariable(variableListView));
        editButton.setOnAction(e -> editSelectedVariable(variableListView));
        deleteButton.setOnAction(e -> deleteSelectedVariable(variableListView));

        variableListView.getSelectionModel().selectedItemProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal != null) {
                showVariableDetails(newVal, detailArea);
            } else {
                detailArea.clear();
            }
        });

        content.getChildren().addAll(
            new Label("变量列表:"),
            variableListView,
            buttonBox,
            new Label("变量详情:"),
            detailArea
        );

        return content;
    }

    /**
     * 添加新变量
     */
    private void addNewVariable(ListView<TemplateVariable> listView) {
        TemplateVariable newVariable = showVariableEditDialog(null);
        if (newVariable != null) {
            currentVariables.add(newVariable);
            listView.getItems().add(newVariable);
        }
    }

    /**
     * 编辑选中的变量
     */
    private void editSelectedVariable(ListView<TemplateVariable> listView) {
        TemplateVariable selected = listView.getSelectionModel().getSelectedItem();
        if (selected == null) {
            showWarning("请先选择要编辑的变量");
            return;
        }

        TemplateVariable edited = showVariableEditDialog(selected);
        if (edited != null) {
            int index = currentVariables.indexOf(selected);
            currentVariables.set(index, edited);
            listView.getItems().set(index, edited);
        }
    }

    /**
     * 删除选中的变量
     */
    private void deleteSelectedVariable(ListView<TemplateVariable> listView) {
        TemplateVariable selected = listView.getSelectionModel().getSelectedItem();
        if (selected == null) {
            showWarning("请先选择要删除的变量");
            return;
        }

        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("确认删除");
        confirmAlert.setHeaderText("删除变量");
        confirmAlert.setContentText("确定要删除变量 \"" + selected.getLabel() + "\" 吗？");

        Optional<ButtonType> result = confirmAlert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            currentVariables.remove(selected);
            listView.getItems().remove(selected);
        }
    }

    /**
     * 显示变量详情
     */
    private void showVariableDetails(TemplateVariable variable, TextArea detailArea) {
        StringBuilder details = new StringBuilder();
        details.append("变量名: ").append(variable.getName()).append("\n");
        details.append("显示标签: ").append(variable.getLabel()).append("\n");
        details.append("类型: ").append(variable.getType().getDisplayName()).append("\n");
        details.append("必填: ").append(variable.isRequired() ? "是" : "否").append("\n");
        details.append("默认值: ").append(variable.getDefaultValue() != null ? variable.getDefaultValue() : "无").append("\n");
        details.append("占位符: ").append(variable.getPlaceholder()).append("\n");
        if (variable.getDescription() != null && !variable.getDescription().trim().isEmpty()) {
            details.append("描述: ").append(variable.getDescription()).append("\n");
        }

        detailArea.setText(details.toString());
    }

    /**
     * 显示变量编辑对话框
     */
    private TemplateVariable showVariableEditDialog(TemplateVariable variable) {
        Dialog<TemplateVariable> dialog = new Dialog<>();
        dialog.setTitle(variable == null ? "添加变量" : "编辑变量");
        dialog.setHeaderText(variable == null ? "创建新的模板变量" : "编辑模板变量");

        // 创建表单
        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));

        TextField nameField = new TextField();
        TextField labelField = new TextField();
        ComboBox<TemplateVariable.VariableType> typeComboBox = new ComboBox<>();
        typeComboBox.getItems().addAll(TemplateVariable.VariableType.values());
        TextField defaultValueField = new TextField();
        CheckBox requiredCheckBox = new CheckBox();
        TextArea descriptionArea = new TextArea();
        descriptionArea.setPrefRowCount(3);

        if (variable != null) {
            nameField.setText(variable.getName());
            labelField.setText(variable.getLabel());
            typeComboBox.setValue(variable.getType());
            defaultValueField.setText(variable.getDefaultValue() != null ? variable.getDefaultValue() : "");
            requiredCheckBox.setSelected(variable.isRequired());
            descriptionArea.setText(variable.getDescription() != null ? variable.getDescription() : "");
        } else {
            typeComboBox.setValue(TemplateVariable.VariableType.TEXT);
        }

        grid.add(new Label("变量名:"), 0, 0);
        grid.add(nameField, 1, 0);
        grid.add(new Label("显示标签:"), 0, 1);
        grid.add(labelField, 1, 1);
        grid.add(new Label("类型:"), 0, 2);
        grid.add(typeComboBox, 1, 2);
        grid.add(new Label("默认值:"), 0, 3);
        grid.add(defaultValueField, 1, 3);
        grid.add(new Label("必填:"), 0, 4);
        grid.add(requiredCheckBox, 1, 4);
        grid.add(new Label("描述:"), 0, 5);
        grid.add(descriptionArea, 1, 5);

        dialog.getDialogPane().setContent(grid);

        ButtonType okButtonType = new ButtonType("确定", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(okButtonType, ButtonType.CANCEL);

        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == okButtonType) {
                String name = nameField.getText().trim();
                String label = labelField.getText().trim();

                if (name.isEmpty() || label.isEmpty()) {
                    showError("变量名和显示标签不能为空");
                    return null;
                }

                if (!TemplateVariable.isValidVariableName(name)) {
                    showError("变量名格式不正确，只能包含字母、数字和下划线，且不能以数字开头");
                    return null;
                }

                TemplateVariable result = new TemplateVariable();
                result.setName(name);
                result.setLabel(label);
                result.setType(typeComboBox.getValue());
                result.setDefaultValue(defaultValueField.getText().trim());
                result.setRequired(requiredCheckBox.isSelected());
                result.setDescription(descriptionArea.getText().trim());

                return result;
            }
            return null;
        });

        Optional<TemplateVariable> result = dialog.showAndWait();
        return result.orElse(null);
    }
}
