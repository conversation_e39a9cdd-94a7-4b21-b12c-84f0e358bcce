package com.bboss.report.controller;

import com.bboss.report.model.EmailSettings;
import com.bboss.report.util.ConfigManager;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.CheckBox;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.control.PasswordField;
import javafx.scene.control.RadioButton;
import javafx.scene.control.TextField;
import javafx.stage.Stage;
import lombok.extern.slf4j.Slf4j;
import javax.mail.Authenticator;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import java.util.List;
import java.util.Properties;
import java.util.regex.Pattern;

/**
 * 邮件设置对话框控制器
 * 负责处理设置界面的用户交互和数据验证
 * 
 * <AUTHOR>
 * @create 2025/6/30
 * @since 1.0.0
 */
@Slf4j
public class SettingsController {
    
    // FXML控件注入
    @FXML private TextField smtpHostField;
    @FXML private TextField smtpPortField;
    @FXML private CheckBox enableSSLCheckBox;
    @FXML private ComboBox<String> senderEmailComboBoxSettings;
    @FXML private Button deleteSenderEmailButton;
    @FXML private PasswordField senderPasswordField;
    @FXML private TextField senderNameField;
    @FXML private RadioButton compressModeRadio;
    @FXML private RadioButton expandModeRadio;
    @FXML private TextField maxSizeField;
    @FXML private TextField maxIndividualSizeField;
    @FXML private Label tipLabel;
    @FXML private Label statusLabel;
    @FXML private Button testConnectionButton;
    @FXML private Button cancelButton;
    @FXML private Button saveButton;
    
    private ConfigManager configManager;
    private EmailSettings originalSettings;
    
    // 邮箱地址验证正则表达式
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$"
    );
    
    /**
     * FXML加载后自动调用的初始化方法
     */
    @FXML
    public void initialize() {
        configManager = ConfigManager.getInstance();
        
        // 绑定按钮事件
        saveButton.setOnAction(event -> saveSettings());
        cancelButton.setOnAction(event -> closeDialog());
        testConnectionButton.setOnAction(event -> testConnection());
        deleteSenderEmailButton.setOnAction(event -> deleteSenderEmail());

        // 初始化发件人邮箱ComboBox
        initializeSenderEmailComboBox();

        // 加载现有配置
        loadCurrentSettings();
        
        // 设置端口号字段只能输入数字
        smtpPortField.textProperty().addListener((observable, oldValue, newValue) -> {
            if (!newValue.matches("\\d*")) {
                smtpPortField.setText(newValue.replaceAll("[^\\d]", ""));
            }
        });

        // 设置最大附件大小字段只能输入数字
        maxSizeField.textProperty().addListener((observable, oldValue, newValue) -> {
            if (!newValue.matches("\\d*")) {
                maxSizeField.setText(newValue.replaceAll("[^\\d]", ""));
            }
        });

        // 根据SMTP服务器自动设置端口和SSL
        smtpHostField.textProperty().addListener((observable, oldValue, newValue) -> {
            autoConfigureByHost(newValue);
        });
        
        statusLabel.setText("请填写邮件服务器配置信息");
    }

    /**
     * 初始化发件人邮箱ComboBox
     */
    private void initializeSenderEmailComboBox() {
        // 加载保存的发件人邮箱列表
        List<String> savedEmails = configManager.getSavedSenderEmails();
        senderEmailComboBoxSettings.setItems(FXCollections.observableArrayList(savedEmails));

        // 监听ComboBox值变化，自动保存新输入的邮箱
        senderEmailComboBoxSettings.valueProperty().addListener((observable, oldValue, newValue) -> {
            if (newValue != null && !newValue.trim().isEmpty()) {
                String email = newValue.trim();
                if (!savedEmails.contains(email)) {
                    configManager.addSenderEmail(email);
                    // 刷新ComboBox列表
                    refreshSenderEmailComboBox();
                }
            }
        });

        // 监听编辑器文本变化（当用户直接输入时）
        senderEmailComboBoxSettings.getEditor().textProperty().addListener((observable, oldValue, newValue) -> {
            if (newValue != null && !newValue.trim().isEmpty()) {
                String email = newValue.trim();
                // 简单的邮箱格式验证
                if (email.contains("@") && email.contains(".")) {
                    if (!savedEmails.contains(email)) {
                        configManager.addSenderEmail(email);
                    }
                }
            }
        });
    }

    /**
     * 刷新发件人邮箱ComboBox列表
     */
    private void refreshSenderEmailComboBox() {
        List<String> savedEmails = configManager.getSavedSenderEmails();
        String currentValue = senderEmailComboBoxSettings.getValue();
        senderEmailComboBoxSettings.setItems(FXCollections.observableArrayList(savedEmails));
        senderEmailComboBoxSettings.setValue(currentValue);
    }

    /**
     * 删除选中的发件人邮箱
     */
    private void deleteSenderEmail() {
        String selectedEmail = senderEmailComboBoxSettings.getValue();
        if (selectedEmail == null || selectedEmail.trim().isEmpty()) {
            showError("请先选择要删除的邮箱地址！");
            return;
        }

        // 确认删除
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("确认删除");
        confirmAlert.setHeaderText("删除邮箱地址");
        confirmAlert.setContentText("确定要删除邮箱地址 \"" + selectedEmail + "\" 吗？");

        confirmAlert.showAndWait().ifPresent(response -> {
            if (response == javafx.scene.control.ButtonType.OK) {
                boolean success = configManager.removeSenderEmail(selectedEmail);
                if (success) {
                    refreshSenderEmailComboBox();
                    senderEmailComboBoxSettings.setValue("");
                    statusLabel.setText("邮箱地址已删除");
                } else {
                    showError("删除邮箱地址失败！");
                }
            }
        });
    }

    /**
     * 加载当前的邮件设置到界面
     */
    private void loadCurrentSettings() {
        originalSettings = configManager.getCurrentSettings();
        
        smtpHostField.setText(originalSettings.getSmtpHost());
        smtpPortField.setText(String.valueOf(originalSettings.getSmtpPort()));
        enableSSLCheckBox.setSelected(originalSettings.isEnableSSL());
        senderEmailComboBoxSettings.setValue(originalSettings.getSenderEmail());
        senderPasswordField.setText(originalSettings.getSenderPassword());
        senderNameField.setText(originalSettings.getSenderName());
        compressModeRadio.setSelected(originalSettings.isCompressDirectories());
        expandModeRadio.setSelected(!originalSettings.isCompressDirectories());
        maxSizeField.setText(String.valueOf(originalSettings.getMaxAttachmentSizeMB()));
    }
    
    /**
     * 根据SMTP服务器地址自动配置端口和SSL设置
     */
    private void autoConfigureByHost(String host) {
        if (host == null || host.trim().isEmpty()) {
            return;
        }
        
        host = host.toLowerCase().trim();
        
        // 常见邮件服务商的自动配置
        if (host.contains("qq.com")) {
            smtpPortField.setText("465");
            enableSSLCheckBox.setSelected(true);
            tipLabel.setText("提示：QQ邮箱请使用授权码，不是QQ密码。授权码可在QQ邮箱设置中生成。");
        } else if (host.contains("163.com")) {
            smtpPortField.setText("465");
            enableSSLCheckBox.setSelected(true);
            tipLabel.setText("提示：网易邮箱请使用授权码，可在邮箱设置中开启SMTP服务并生成授权码。");
        } else if (host.contains("126.com")) {
            smtpPortField.setText("465");
            enableSSLCheckBox.setSelected(true);
            tipLabel.setText("提示：网易邮箱请使用授权码，可在邮箱设置中开启SMTP服务并生成授权码。");
        } else if (host.contains("139.com")) {
            smtpPortField.setText("465");
            enableSSLCheckBox.setSelected(true);
            tipLabel.setText("提示：139邮箱请使用授权码，可在邮箱设置中开启SMTP服务并生成授权码。");
        } else if (host.contains("gmail.com")) {
            smtpPortField.setText("587");
            enableSSLCheckBox.setSelected(true);
            tipLabel.setText("提示：Gmail请使用应用专用密码，可在Google账户安全设置中生成。");
        } else if (host.contains("outlook.com") || host.contains("hotmail.com")) {
            smtpPortField.setText("587");
            enableSSLCheckBox.setSelected(true);
            tipLabel.setText("提示：Outlook邮箱请确保已开启SMTP认证。");
        } else {
            tipLabel.setText("提示：请根据您的邮件服务商要求配置端口号和SSL设置。");
        }
    }
    
    /**
     * 验证输入的有效性
     */
    private boolean validateInput() {
        // 验证SMTP服务器地址
        if (smtpHostField.getText() == null || smtpHostField.getText().trim().isEmpty()) {
            showError("SMTP服务器地址不能为空！");
            return false;
        }
        
        // 验证端口号
        String portText = smtpPortField.getText();
        if (portText == null || portText.trim().isEmpty()) {
            showError("端口号不能为空！");
            return false;
        }
        
        try {
            int port = Integer.parseInt(portText);
            if (port <= 0 || port > 65535) {
                showError("端口号必须在1-65535之间！");
                return false;
            }
        } catch (NumberFormatException e) {
            showError("端口号必须是有效的数字！");
            return false;
        }
        
        // 验证邮箱地址
        String email = senderEmailComboBoxSettings.getValue();
        if (email == null || email.trim().isEmpty()) {
            email = senderEmailComboBoxSettings.getEditor().getText();
        }
        if (email == null || email.trim().isEmpty()) {
            showError("发件人邮箱地址不能为空！");
            return false;
        }

        if (!EMAIL_PATTERN.matcher(email.trim()).matches()) {
            showError("请输入有效的邮箱地址！");
            return false;
        }
        
        // 验证密码
        if (senderPasswordField.getText() == null || senderPasswordField.getText().trim().isEmpty()) {
            showError("密码/授权码不能为空！");
            return false;
        }

        // 验证最大附件大小
        String maxSizeText = maxSizeField.getText();
        if (maxSizeText != null && !maxSizeText.trim().isEmpty()) {
            try {
                int maxSize = Integer.parseInt(maxSizeText);
                if (maxSize <= 0 || maxSize > 100) {
                    showError("附件大小限制必须在1-100MB之间！");
                    return false;
                }
            } catch (NumberFormatException e) {
                showError("附件大小限制必须是有效的数字！");
                return false;
            }
        }

        return true;
    }
    
    /**
     * 保存设置
     */
    private void saveSettings() {
        if (!validateInput()) {
            return;
        }
        
        try {
            EmailSettings settings = new EmailSettings();
            settings.setSmtpHost(smtpHostField.getText().trim());
            settings.setSmtpPort(Integer.parseInt(smtpPortField.getText().trim()));
            settings.setEnableSSL(enableSSLCheckBox.isSelected());
            String senderEmail = senderEmailComboBoxSettings.getValue();
            if (senderEmail == null || senderEmail.trim().isEmpty()) {
                senderEmail = senderEmailComboBoxSettings.getEditor().getText();
            }
            settings.setSenderEmail(senderEmail != null ? senderEmail.trim() : "");
            settings.setSenderPassword(senderPasswordField.getText());
            settings.setSenderName(senderNameField.getText().trim());
            settings.setCompressDirectories(compressModeRadio.isSelected());

            // 设置最大附件大小
            String maxSizeText = maxSizeField.getText().trim();
            if (!maxSizeText.isEmpty()) {
                settings.setMaxAttachmentSizeMB(Integer.parseInt(maxSizeText));
            }

            boolean saved = configManager.saveSettings(settings);
            if (saved) {
                statusLabel.setText("设置已保存成功！");
                statusLabel.setTextFill(javafx.scene.paint.Color.GREEN);
                
                // 延迟关闭对话框
                Platform.runLater(() -> {
                    try {
                        Thread.sleep(1000);
                        closeDialog();
                    } catch (InterruptedException e) {
                        closeDialog();
                    }
                });
            } else {
                showError("保存设置失败，请检查文件权限！");
            }
        } catch (Exception e) {
            showError("保存设置时发生错误：" + e.getMessage());
        }
    }

    /**
     * 测试邮件服务器连接
     */
    private void testConnection() {
        if (!validateInput()) {
            return;
        }

        // 禁用测试按钮，防止重复点击
        testConnectionButton.setDisable(true);
        statusLabel.setText("正在测试连接...");
        statusLabel.setTextFill(javafx.scene.paint.Color.BLUE);

        // 在后台线程中执行测试
        Task<Boolean> testTask = new Task<Boolean>() {
            @Override
            protected Boolean call() throws Exception {
                return performConnectionTest();
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    testConnectionButton.setDisable(false);
                    if (getValue()) {
                        statusLabel.setText("连接测试成功！");
                        statusLabel.setTextFill(javafx.scene.paint.Color.GREEN);
                        showInfo("连接测试成功！", "邮件服务器连接正常，配置有效。");
                    } else {
                        statusLabel.setText("连接测试失败！");
                        statusLabel.setTextFill(javafx.scene.paint.Color.RED);
                    }
                });
            }

            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    testConnectionButton.setDisable(false);
                    statusLabel.setText("连接测试失败！");
                    statusLabel.setTextFill(javafx.scene.paint.Color.RED);
                    Throwable exception = getException();
                    if (exception != null) {
                        showError("连接测试失败：" + exception.getMessage());
                    }
                });
            }
        };

        Thread testThread = new Thread(testTask);
        testThread.setDaemon(true);
        testThread.start();
    }

    /**
     * 执行实际的连接测试
     */
    private boolean performConnectionTest() {
        try {
            Properties props = new Properties();
            props.put("mail.smtp.host", smtpHostField.getText().trim());
            props.put("mail.smtp.port", smtpPortField.getText().trim());
            props.put("mail.smtp.auth", "true");

            if (enableSSLCheckBox.isSelected()) {
                props.put("mail.smtp.starttls.enable", "true");
                props.put("mail.smtp.ssl.enable", "true");
            }

            // 创建会话
            Session session = Session.getInstance(props, new Authenticator() {
                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    String email = senderEmailComboBoxSettings.getValue();
                    if (email == null || email.trim().isEmpty()) {
                        email = senderEmailComboBoxSettings.getEditor().getText();
                    }
                    return new PasswordAuthentication(
                        email != null ? email.trim() : "",
                        senderPasswordField.getText()
                    );
                }
            });

            // 尝试连接到SMTP服务器
            Transport transport = session.getTransport("smtp");
            transport.connect();
            transport.close();

            return true;
        } catch (Exception e) {
            System.err.println("连接测试失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 关闭对话框
     */
    private void closeDialog() {
        Stage stage = (Stage) cancelButton.getScene().getWindow();
        stage.close();
    }

    /**
     * 显示错误提示框
     */
    private void showError(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("错误");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * 显示信息提示框
     */
    private void showInfo(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
