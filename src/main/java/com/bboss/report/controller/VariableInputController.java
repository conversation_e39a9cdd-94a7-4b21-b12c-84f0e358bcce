package com.bboss.report.controller;

import com.bboss.report.model.EmailTemplate;
import com.bboss.report.model.TemplateVariable;
import com.bboss.report.util.DateTimePicker;
import com.bboss.report.util.TemplateVariableProcessor;
import javafx.fxml.FXML;
import javafx.geometry.Insets;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.Control;
import javafx.scene.control.DatePicker;
import javafx.scene.control.Label;
import javafx.scene.control.ScrollPane;
import javafx.scene.control.TextArea;
import javafx.scene.control.TextField;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;
import javafx.util.StringConverter;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 变量输入对话框控制器
 * 处理模板变量的用户输入和验证
 * 
 * <AUTHOR>
 * @create 2025/7/26
 * @since 1.0.0
 */
public class VariableInputController {
    
    @FXML private Label templateInfoLabel;
    @FXML private ScrollPane variableScrollPane;
    @FXML private VBox variableContainer;
    @FXML private TextArea presetVariablesInfo;
    @FXML private Label errorLabel;
    @FXML private Button previewButton;
    @FXML private Button resetButton;
    @FXML private Button confirmButton;
    @FXML private Button cancelButton;
    
    private EmailTemplate template;
    private List<TemplateVariable> variables;
    private Map<String, Control> inputControls;
    private Map<String, String> variableValues;
    private boolean confirmed = false;
    
    /**
     * FXML初始化方法
     */
    @FXML
    public void initialize() {
        inputControls = new HashMap<>();
        variableValues = new HashMap<>();
        
        // 绑定按钮事件
        previewButton.setOnAction(event -> showPreview());
        resetButton.setOnAction(event -> resetValues());
        confirmButton.setOnAction(event -> confirmInput());
        cancelButton.setOnAction(event -> cancelInput());
        
        // 初始化预设变量说明
        initializePresetVariablesInfo();
    }
    
    /**
     * 设置模板和变量信息
     */
    public void setTemplateAndVariables(EmailTemplate template, List<TemplateVariable> variables) {
        this.template = template;
        this.variables = variables;
        
        // 更新模板信息显示
        templateInfoLabel.setText("模板：" + template.getDisplayName());
        
        // 创建变量输入控件
        createVariableInputControls();
    }
    
    /**
     * 创建变量输入控件
     */
    private void createVariableInputControls() {
        variableContainer.getChildren().clear();
        inputControls.clear();
        
        if (variables.isEmpty()) {
            Label noVariablesLabel = new Label("此模板没有定义变量");
            noVariablesLabel.setStyle("-fx-text-fill: #666666;");
            variableContainer.getChildren().add(noVariablesLabel);
            return;
        }
        
        GridPane gridPane = new GridPane();
        gridPane.setHgap(15.0);
        gridPane.setVgap(15.0);
        gridPane.setPadding(new Insets(10));
        
        int row = 0;
        for (TemplateVariable variable : variables) {
            // 跳过预设变量
            if (TemplateVariableProcessor.isPresetVariable(variable.getName())) {
                continue;
            }
            
            // 变量标签
            Label label = new Label(variable.getLabel() + ":");
            if (variable.isRequired()) {
                label.setText(label.getText() + " *");
                label.setStyle("-fx-text-fill: #dc3545; -fx-font-weight: bold;");
            }
            gridPane.add(label, 0, row);
            
            // 输入控件
            Control inputControl = createInputControl(variable);
            inputControls.put(variable.getName(), inputControl);
            
            VBox inputBox = new VBox(5);
            inputBox.getChildren().add(inputControl);
            
            // 添加描述信息
            if (variable.getDescription() != null && !variable.getDescription().trim().isEmpty()) {
                Label descLabel = new Label(variable.getDescription());
                descLabel.setStyle("-fx-text-fill: #666666; -fx-font-size: 10px;");
                inputBox.getChildren().add(descLabel);
            }
            
            // 添加验证提示
            Label hintLabel = new Label(variable.getValidationHint());
            hintLabel.setStyle("-fx-text-fill: #6c757d; -fx-font-size: 9px;");
            inputBox.getChildren().add(hintLabel);
            
            gridPane.add(inputBox, 1, row);
            row++;
        }
        
        variableContainer.getChildren().add(gridPane);
    }
    
    /**
     * 根据变量类型创建输入控件
     */
    private Control createInputControl(TemplateVariable variable) {
        switch (variable.getType()) {
            case DATE:
                DatePicker datePicker = new DatePicker();

                // 设置日期格式为年月格式
                datePicker.setConverter(new StringConverter<LocalDate>() {
                    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");

                    @Override
                    public String toString(LocalDate date) {
                        return date != null ? date.format(formatter) : "";
                    }

                    @Override
                    public LocalDate fromString(String string) {
                        if (string != null && !string.trim().isEmpty()) {
                            try {
                                // 解析年月格式，日期设为1号
                                YearMonth yearMonth = YearMonth.parse(string, formatter);
                                return yearMonth.atDay(1);
                            } catch (Exception e) {
                                return null;
                            }
                        }
                        return null;
                    }
                });

                // 设置默认值
                if (variable.getDefaultValue() != null && !variable.getDefaultValue().trim().isEmpty()) {
                    try {
                        // 尝试解析默认值
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
                        if (variable.getDefaultValue().matches("\\d{4}-\\d{2}")) {
                            // 年月格式
                            YearMonth yearMonth = YearMonth.parse(variable.getDefaultValue(),formatter);
                            datePicker.setValue(yearMonth.atDay(1));
                        }else if (variable.getDefaultValue().matches("\\d{6}")) {
                            formatter =DateTimeFormatter.ofPattern("yyyyMM");
                            YearMonth yearMonth = YearMonth.parse(variable.getDefaultValue(),formatter);
                            datePicker.setValue(yearMonth.atDay(1));
                        }else {
                            // 完整日期格式
                            datePicker.setValue(LocalDate.parse(variable.getDefaultValue()));
                        }
                    } catch (Exception e) {
                        // 默认值解析失败，使用上个月
                        LocalDate lastMonth = LocalDate.now().minusMonths(1).withDayOfMonth(1);
                        datePicker.setValue(lastMonth);
                    }
                } else {
                    // 没有默认值，使用上个月
                    LocalDate lastMonth = LocalDate.now().minusMonths(1).withDayOfMonth(1);
                    datePicker.setValue(lastMonth);
                }

                return datePicker;

            case DATETIME:
                DateTimePicker dateTimePicker = new DateTimePicker();
                if (variable.getDefaultValue() != null && !variable.getDefaultValue().trim().isEmpty()) {
                    try {
                        dateTimePicker.setValue(variable.getDefaultValue());
                    } catch (Exception e) {
                        // 忽略默认值解析错误，使用当前时间
                        dateTimePicker.setToNow();
                    }
                } else {
                    // 如果没有默认值，设置为当前时间
                    dateTimePicker.setToNow();
                }
                return dateTimePicker;
            case TEXT:
            case EMAIL:
            default:
                TextField textField = new TextField();
                textField.setPromptText("请输入" + variable.getLabel());
                if (variable.getDefaultValue() != null) {
                    textField.setText(variable.getDefaultValue());
                }
                return textField;
        }
    }
    
    /**
     * 初始化预设变量说明
     */
    private void initializePresetVariablesInfo() {
        StringBuilder info = new StringBuilder();
        List<TemplateVariable> presetVars = TemplateVariableProcessor.getPresetVariables();
        
        for (TemplateVariable var : presetVars) {
            info.append("• ").append(var.getPlaceholder())
                .append(" - ").append(var.getLabel())
                .append(" (").append(var.getDescription()).append(")\n");
        }
        
        presetVariablesInfo.setText(info.toString());
    }
    
    /**
     * 收集用户输入的变量值
     */
    private Map<String, String> collectVariableValues() {
        Map<String, String> values = new HashMap<>();
        
        for (TemplateVariable variable : variables) {
            if (TemplateVariableProcessor.isPresetVariable(variable.getName())) {
                continue; // 跳过预设变量
            }
            
            Control control = inputControls.get(variable.getName());
            String value = "";
            
            if (control instanceof TextField) {
                value = ((TextField) control).getText();
            } else if (control instanceof DatePicker) {
                DatePicker datePicker = (DatePicker) control;
                if (datePicker.getValue() != null) {
                    // 对于日期类型的变量，输出年月格式
                    if (variable.getType() == TemplateVariable.VariableType.DATE) {
                        value = datePicker.getValue().format(DateTimeFormatter.ofPattern("yyyyMM"));
                    } else {
                        value = datePicker.getValue().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    }
                }
            }else if (control instanceof DateTimePicker) {
                value = ((DateTimePicker) control).getValue();
            }
            
            values.put(variable.getName(), value);
        }
        
        return values;
    }
    
    /**
     * 验证输入值
     */
    private boolean validateInput() {
        Map<String, String> values = collectVariableValues();
        TemplateVariableProcessor.ValidationResult result = 
            TemplateVariableProcessor.validateVariableValues(variables, values);
        
        if (!result.isValid()) {
            showError(result.getErrorMessage());
            return false;
        }
        
        hideError();
        return true;
    }
    
    /**
     * 显示预览效果
     */
    private void showPreview() {
        if (!validateInput()) {
            return;
        }
        
        Map<String, String> values = collectVariableValues();
        
        String previewSubject = TemplateVariableProcessor.replaceVariables(template.getSubject(), values);
        String previewBody = TemplateVariableProcessor.replaceVariables(template.getBody(), values);
        
        Alert previewAlert = new Alert(Alert.AlertType.INFORMATION);
        previewAlert.setTitle("预览效果");
        previewAlert.setHeaderText("变量替换后的效果预览");
        
        StringBuilder content = new StringBuilder();
        content.append("主题：\n").append(previewSubject).append("\n\n");
        content.append("正文：\n").append(previewBody);
        
        TextArea textArea = new TextArea(content.toString());
        textArea.setEditable(false);
        textArea.setWrapText(true);
        textArea.setPrefRowCount(15);
        textArea.setPrefColumnCount(60);
        
        previewAlert.getDialogPane().setContent(textArea);
        previewAlert.getDialogPane().setPrefWidth(600);
        previewAlert.getDialogPane().setPrefHeight(400);
        previewAlert.setResizable(true);
        
        previewAlert.showAndWait();
    }
    
    /**
     * 重置输入值
     */
    private void resetValues() {
        for (TemplateVariable variable : variables) {
            if (TemplateVariableProcessor.isPresetVariable(variable.getName())) {
                continue;
            }
            
            Control control = inputControls.get(variable.getName());
            if (control instanceof TextField) {
                TextField textField = (TextField) control;
                textField.setText(variable.getDefaultValue() != null ? variable.getDefaultValue() : "");
            } else if (control instanceof DatePicker) {
                DatePicker datePicker = (DatePicker) control;
                if (variable.getDefaultValue() != null && !variable.getDefaultValue().trim().isEmpty()) {
                    try {
                        // 尝试解析默认值
                        if (variable.getDefaultValue().matches("\\d{4}-\\d{2}")) {
                            // 年月格式
                            YearMonth yearMonth = YearMonth.parse(variable.getDefaultValue());
                            datePicker.setValue(yearMonth.atDay(1));
                        } else {
                            // 完整日期格式
                            datePicker.setValue(LocalDate.parse(variable.getDefaultValue()));
                        }
                    } catch (Exception e) {
                        // 默认值解析失败，使用上个月
                        LocalDate lastMonth = LocalDate.now().minusMonths(1).withDayOfMonth(1);
                        datePicker.setValue(lastMonth);
                    }
                } else {
                    // 没有默认值，使用上个月
                    LocalDate lastMonth = LocalDate.now().minusMonths(1).withDayOfMonth(1);
                    datePicker.setValue(lastMonth);
                }
            }
        }
        
        hideError();
    }
    
    /**
     * 确认输入
     */
    private void confirmInput() {
        if (!validateInput()) {
            return;
        }
        
        variableValues = collectVariableValues();
        confirmed = true;
        closeDialog();
    }
    
    /**
     * 取消输入
     */
    private void cancelInput() {
        confirmed = false;
        closeDialog();
    }
    
    /**
     * 关闭对话框
     */
    private void closeDialog() {
        Stage stage = (Stage) cancelButton.getScene().getWindow();
        stage.close();
    }
    
    /**
     * 显示错误信息
     */
    private void showError(String message) {
        errorLabel.setText(message);
        errorLabel.setVisible(true);
    }
    
    /**
     * 隐藏错误信息
     */
    private void hideError() {
        errorLabel.setVisible(false);
    }
    
    /**
     * 获取用户是否确认
     */
    public boolean isConfirmed() {
        return confirmed;
    }
    
    /**
     * 获取变量值
     */
    public Map<String, String> getVariableValues() {
        return new HashMap<>(variableValues);
    }
}
