package com.bboss.report.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;

import com.bboss.report.component.TextAreaOutputStream;
import com.bboss.report.component.TextAreaOutputStreamAppender;
import com.bboss.report.constants.Constants;
import com.bboss.report.factory.ReportAuditFactory;
import com.bboss.report.model.AuditResponse;
import com.bboss.report.model.ConfigData;
import com.bboss.report.model.ParamData;
import com.bboss.report.service.ReportAuditService;
import com.bboss.report.service.special.AggregateSumService;
import com.bboss.report.service.special.CapMoneyService;
import com.bboss.report.service.special.CheckPdfInfoService;
import com.bboss.report.service.special.EstimateService;
import de.felixroske.jfxsupport.FXMLController;
import javafx.application.Platform;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.Pane;
import javafx.stage.DirectoryChooser;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.stage.Window;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.ResourceBundle;

/**
 * 主界面控制器
 */
@Slf4j
@FXMLController
public class MainController implements Initializable {

    // 主容器
    @FXML
    public Pane rootPane;
    @FXML
    public Button btnChooseFile;
    @FXML
    public TextField settleMonth;
    @FXML
    public TextArea runLog;
    @FXML
    public Button btnConfirm;
    @FXML
    public Button btnSendEmail;
    @FXML
    public Label versionLabel;
    private String path;


    @Autowired
    AggregateSumService aggregateSumService;
    @Autowired
    EstimateService estimateService;
    @Autowired
    CapMoneyService capMoneyService;
    @Autowired
    CheckPdfInfoService checkPdfInfoService;
    @Value("${maven.package.time}")
    private String buildTime;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        log.info("initialize: {}", location.getPath());
        OutputStream os = new TextAreaOutputStream(runLog);
        TextAreaOutputStreamAppender.setStaticOutputStream(os);
        String month = DateUtil.format(DateUtil.lastMonth(), "yyyyMM");
        settleMonth.setText(month);

        // 设置版本号（从配置中获取打包时间）
        String versionNumber = (buildTime != null && !buildTime.trim().isEmpty()) ? buildTime : "20250701150000";
        versionLabel.setText("v" + versionNumber);
        log.info("应用版本: v{}", versionNumber);
    }

//    /**
//     * 弹出框按钮单击事件
//     * @param actionEvent
//     */
//    public void onBtnAlertClick(ActionEvent actionEvent) {
//        Alert alert = new Alert(Alert.AlertType.INFORMATION);
//        alert.setContentText("当前时间：" + DateUtil.now());
//        alert.show();
//    }

    /**
     * 选择文件按钮单机事件
     *
     * @param actionEvent
     */
    public void onBtnChooseFileClick(ActionEvent actionEvent) {
        Window window = rootPane.getScene().getWindow();
        DirectoryChooser directoryChooser = new DirectoryChooser();
        File directory = directoryChooser.showDialog(window);
        String directoryAbsolutePath = directory == null ? "" : directory.getAbsolutePath();
        log.info("选择的目录: {}", directoryAbsolutePath);
        if (directory != null) {
            Alert alert = getAlert(Alert.AlertType.CONFIRMATION, "确认报表稽核目录是否正确？目录：" + directoryAbsolutePath);
            ButtonType result = alert.showAndWait().orElse(ButtonType.CANCEL);
            // 检查用户的响应
            if (result == ButtonType.OK) {
                path = directoryAbsolutePath;
            } else {
                log.info("用户点击了取消按钮或关闭对话框");
            }
        }
    }

    /**
     * 确认
     */
    public void onBtnConfirmClick(ActionEvent actionEvent) {

        String month = settleMonth.getText();
        if (StrUtil.isBlank(path)) {
            Alert alert = getAlert(Alert.AlertType.ERROR, "请确认是否选择了报表目录！");
            alert.show();
            return;
        }
        if (StrUtil.isBlank(month)) {
            Alert alert = getAlert(Alert.AlertType.ERROR, "请确认是否填入了账期！");
            alert.show();
            return;
        }
        btnConfirm.setDisable(true);
        new Thread(() -> {
            String ret = execAudit(path, month);
            Platform.runLater(() -> {
                Alert alert = getAlert(Alert.AlertType.INFORMATION, ret);
                alert.show();
                btnConfirm.setDisable(false);
            });
        }).start();
    }

    /**
     * 邮件发送按钮点击事件
     * 
     * @param actionEvent
     */
    public void onBtnSendEmailClick(ActionEvent actionEvent) {
        try {
            // 加载邮件发送界面
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/EmailSenderView.fxml"));
            Parent root = loader.load();

            // 创建新的舞台（窗口）
            Stage emailStage = new Stage();
            emailStage.setTitle("邮件发送工具");
            emailStage.setScene(new Scene(root, 800, 900));

            // 设置为模态对话框
            emailStage.initModality(Modality.APPLICATION_MODAL);

            // 设置父窗口
            Window parentWindow = rootPane.getScene().getWindow();
            emailStage.initOwner(parentWindow);

            // 设置窗口属性
            emailStage.setResizable(true);

            // 显示邮件发送窗口
            emailStage.show();

            log.info("邮件发送窗口已打开");

        } catch (IOException e) {
            log.error("无法打开邮件发送窗口", e);
            Alert alert = getAlert(Alert.AlertType.ERROR, "无法打开邮件发送窗口：" + e.getMessage());
            alert.show();
        }
    }

    //开始执行稽
    private String execAudit(String directoryAbsolutePath, String settleMonth) {
        log.info("读取目录 path:{}下文件", directoryAbsolutePath);
        String configFileName = directoryAbsolutePath + File.separator + Constants.CONFIG_NAME;
        File configFile = new File(configFileName);//读取配置文件
        if (!configFile.exists()) {
            configFileName = directoryAbsolutePath + File.separator + Constants.TSE_CONFIG_NAME;
            configFile = new File(configFileName);
        }
        if (configFile.exists()) {
            List<ConfigData> list = EasyExcel.read(configFileName).head(ConfigData.class).sheet().doReadSync();//配置文件的数据
            List<AuditResponse> responseList = new ArrayList<>();
            for (ConfigData data : list) {//轮询配置文件的一条条配置
                //添加 分表和总表的 匹配 多文件对多文件  特殊处理
                if (data.getAuditEntry().contains("79")) {
                    responseList = aggregateSumService.searchSubtableMsg(directoryAbsolutePath, settleMonth, data, responseList);
                    continue;
                }
                if (data.getAuditEntry().contains("101")) {
                    responseList = aggregateSumService.fileCheckDoubleInfo(directoryAbsolutePath, settleMonth, data, responseList);
                    continue;
                }
                if (data.getAuditEntry().contains("83")) {
                    responseList = estimateService.searchSubtableMsg(directoryAbsolutePath, settleMonth, data, responseList);
                    continue;
                }
                if (data.getAuditEntry().contains("100")) {
                    responseList = estimateService.compareProvisionalAndReport(directoryAbsolutePath, settleMonth, data, responseList);
                    continue;
                }

                if (data.getAuditEntry().contains("84")) {
                    responseList = capMoneyService.capMoneyJudget(directoryAbsolutePath, settleMonth, data, responseList);
                    continue;
                }
                if (data.getRKey().equals("PDF01")) {
                    responseList = checkPdfInfoService.checkPdfInfo(directoryAbsolutePath, settleMonth, data, responseList);
                    continue;
                }
                if (data.getRKey().equals("PDF02")) {
                    responseList = checkPdfInfoService.checkPdfAndExcelInfo(directoryAbsolutePath, settleMonth, data, responseList);
                    continue;
                }
                String fileNames = data.getFileName();//取到配置中文件名字
                String[] fileNameArr = fileNames.split(",");//名字有多组
                for (String fileName : fileNameArr) {//依次便利
                    responseList = processMultipleFile(directoryAbsolutePath, settleMonth, data, fileName, responseList);
                }
            }
            // 轮询 文件夹所有文件   是否存在于比对responseList的fileName  不存在则增加一条数据  说明该文件没有被校验到
            if (CollectionUtil.isNotEmpty(responseList)) {
                File directory = new File(directoryAbsolutePath);
                searchFile(directory, responseList);
            }
            String outFile = directoryAbsolutePath + File.separator + Constants.OUT_NAME + "_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN) + Constants.OUT_FILE_SUFFIX;
            log.info("写稽核结果文件:{}", outFile);
            EasyExcel.write(outFile, AuditResponse.class).sheet().doWrite(responseList);
            log.info("稽核完成！");
            return "稽核完成";
        } else {
            log.info("配置文件：{}不存在", Constants.CONFIG_NAME + "或" + Constants.TSE_CONFIG_NAME);
            return "配置文件：" + Constants.CONFIG_NAME + "或" + Constants.TSE_CONFIG_NAME + "不存在";
        }
    }


    private List<AuditResponse> processMultipleFile(String directoryAbsolutePath, String month, ConfigData data, String fileName, List<AuditResponse> responseList) {
        if (data.getTaxRate() == null || !fileName.contains("TAXRATE")) {//文件名字不同替换费率  直接找文件
            processMultipleFile(directoryAbsolutePath, month, data, fileName, responseList, null);
        } else {//有费率 那么1个文件名字 对应多个文件 依次遍历
            String[] taxRates = data.getTaxRate().split(",");
            for (String taxRate : taxRates) {
                processMultipleFile(directoryAbsolutePath, month, data, fileName, responseList, taxRate);

            }
        }
        return responseList;
    }

    private void processMultipleFile(String directoryAbsolutePath, String month, ConfigData data, String fileName, List<AuditResponse> responseList, String taxRate) {
        if (data.getSignSubject() == null || !fileName.contains("SIGN")) {//与费率一样 有则替换 无责给nul
            processMultipleFile(directoryAbsolutePath, month, data, fileName, responseList, taxRate, null);
        } else {
            String[] signSubjects = data.getSignSubject().split(",");
            for (String signSubject : signSubjects) {
                processMultipleFile(directoryAbsolutePath, month, data, fileName, responseList, taxRate, signSubject);
            }
        }
    }

    //开始找文件  执行稽核
    private void processMultipleFile(String directoryAbsolutePath, String month, ConfigData data, String fileName, List<AuditResponse> responseList, String taxRate, String sign) {
        String rFileName = fileName.replace("TAXRATE", taxRate == null ? "" : taxRate).replace("YYYYMM", month).replace("SIGN", sign == null ? "" : sign);
//        String path = directoryAbsolutePath + data.getDirectory();
//        path = path.replaceAll("YYYYMM", month);
//        String pathName = path + File.separator + rFileName;
//        log.info("读取文件名为:{}", pathName);
        File directory = new File(directoryAbsolutePath);
        File file = searchFile(directory, rFileName);//遍历主目录下的所有文件  并根据文件名称是否存在该文件
        if (file != null && file.exists()) {
            ParamData paramData = new ParamData();
            BeanUtil.copyProperties(data, paramData);
            paramData.setFileName(rFileName);
            paramData.setTaxRate(taxRate == null ? data.getTaxRate() : taxRate);
            paramData.setDirectory(file.getPath());
            paramData.setSettleMonth(month);
            paramData.setPathName(file.getAbsolutePath());
            paramData.setSignSubject(sign == null ? "" : sign);
            ReportAuditService reportAuditService = ReportAuditFactory.getReportAudiService(data.getRKey());//applicationContext 从spring中获取到对应的执行类 根据id找到的？
            List<AuditResponse> retList = reportAuditService.execute(paramData);
            responseList.addAll(retList);
        } else {
            log.info("报表{},费率：{},文件：{} 不存在", data.getReportName(), (taxRate == null ? "" : taxRate), rFileName);
//                AuditResponse response = new AuditResponse();
//                BeanUtil.copyProperties(paramData, response);
//                response.setRspCode(AuditResponse.NOTFIND);
//                response.setRspDesc("报表：" + data.getReportName() + ",费率：" + (taxRate == null ? "" : taxRate) + ",文件：" + rFileName + "不存在");
//                responseList.add(response);
        }
    }

    private Alert getAlert(Alert.AlertType alertType, String contentText) {
        Alert alert = new Alert(alertType);
        alert.setHeaderText(null);
        alert.setContentText(contentText);
        return alert;
    }

    public File searchFile(File directory, String targetFileName) {
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile() && file.getName().equals(targetFileName)) {
                        return file;
                    } else if (file.isDirectory()) {
                        File found = searchFile(file, targetFileName); // 递归进入子目录
                        if (found != null) {
                            return found; // 如果找到文件，返回文件对象
                        }
                    }
                }
            }
        }
        return null;
    }

    public void searchFile(File directory, List<AuditResponse> responseList) {
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile()) {
                        //多个分表的金额相加综合等于总表金额 就是这个稽核不参与文件校验 因为该文件的fileName是多文件累加的。不能equal判断
                        boolean containsFile = responseList.stream()
                                .anyMatch(auditResponse -> file.getName().equals(auditResponse.getFileName())
                                        || file.getName().contains("配置")
                                        || file.getName().contains("稽核结果")
                                        || "多个分表的金额相加综合等于总表金额".equals(auditResponse.getAuditEntryStr())
                                        || "暂估与销暂估的数据金额对比".equals(auditResponse.getAuditEntryStr())
                                        || "暂估与正常报表估的数据金额对比".equals(auditResponse.getAuditEntryStr())
                                        || "指定产品是否突破封顶金额的判断".equals(auditResponse.getAuditEntryStr()));
                        if (!containsFile && getFileExtension(file.getName())) {
                            AuditResponse auditResponse = new AuditResponse();
                            auditResponse.setSettleMonth(responseList.get(0).getSettleMonth());
                            auditResponse.setDirectory(file.getPath());
                            auditResponse.setFileName(file.getName());
                            auditResponse.setRspCode(AuditResponse.FAIL);
                            auditResponse.setRspDesc("文件没有找到对应的配置!");
                            responseList.add(auditResponse);
                        }
                    } else if (file.isDirectory()) {
                        searchFile(file, responseList); // 递归进入子目录
                    }
                }
            }
        }

    }

    public static Boolean getFileExtension(String fileName) {
        int dotIndex = fileName.lastIndexOf(".");
        if (dotIndex > 0 && dotIndex < fileName.length() - 1) {
            String fileSuffix = fileName.substring(dotIndex + 1).toLowerCase();
            return fileSuffix.equals("xls");
        } else {
            return true; // 文件没有后缀
        }
    }
}
