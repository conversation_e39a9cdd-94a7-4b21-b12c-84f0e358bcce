package com.bboss.report.listener;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.Cell;
import com.alibaba.excel.metadata.data.FormulaData;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
public class ExcelListenerWithColumn extends AnalysisEventListener<Map<Integer, String>> {
    private List<Map<String, Object>> dataList = new LinkedList<>();
    private Map<String, Object> headerMap = new LinkedHashMap<>();

    private List<Map<String, Object>> dataFxList = new LinkedList<>();
    /**
     * 读取文件的开始行
     */
    private Integer startRow;
    /**
     * 读取文件的结束行
     */
    private Integer endRow;

    private Integer[] columns;

    public ExcelListenerWithColumn(Integer startRow, Integer endRow , Integer[] columns) {
        this.startRow = startRow ;
        this.endRow = endRow;
        this.columns=columns;
    }

    @Override
    public void invoke(Map<Integer, String> rowData, AnalysisContext context) {
        Integer currentRow = context.readRowHolder().getRowIndex();
        String sheetName=context.readSheetHolder().getSheetName();
        ReadRowHolder readRowHolder = context.readRowHolder();
        Map<Integer, ReadCellData<?>> cellDataMap = (Map)readRowHolder.getCellMap();
        Map<String, Object> dataMap = new LinkedHashMap<>();
        Map<String, Object> dataFxMap = new LinkedHashMap<>();
        Integer minRow=null;
        Integer maxRow=null;
        if(startRow==null&&endRow!=null){
            minRow=context.getTotalCount()-endRow;
            maxRow=minRow+1;
        }else if(startRow!=null&&endRow==null){
            minRow=startRow;
            maxRow=minRow+1;
        }else if (startRow!=null&&endRow!=null) {
            minRow=startRow;
            maxRow=context.getTotalCount()-endRow;
        }
        if (minRow<=currentRow  && currentRow<maxRow) {
            for (Map.Entry<Integer, String> entry : rowData.entrySet()) {
                    if (columns == null) {
                        String columnName = sheetName + "&" + currentRow + "&" + entry.getKey(); // 默认列名，可以根据需要修改
                        dataMap.put(columnName, entry.getValue());
                        //获取单元格中FX公式里的值
                        ReadCellData<?> readCellData = cellDataMap.get(entry.getKey());
                        if (ObjectUtil.isNotNull(readCellData)){
                            FormulaData formulaData = readCellData.getFormulaData();
                            BigDecimal numberValue = readCellData.getNumberValue();
                            if (ObjectUtil.isNotNull(numberValue)){
                                boolean matches = numberValue.toString().matches("-?\\d+(\\.\\d+)?");
                                dataFxMap.put(columnName, ObjectUtil.isNull(formulaData)?matches==true?numberValue:entry.getValue():formulaData.getFormulaValue());
                            }else
                                dataFxMap.put(columnName,readCellData.getStringValue());}
                    } else {
                        for (Integer column : columns) {
                            if (column == entry.getKey()) {
                                String columnName = sheetName + "&" + currentRow + "&" + entry.getKey(); // 默认列名，可以根据需要修改
                                dataMap.put(columnName, entry.getValue());
                                //获取单元格中FX公式里的值
                                ReadCellData<?> readCellData = cellDataMap.get(entry.getKey());
                                if (ObjectUtil.isNotNull(readCellData)){
                                    FormulaData formulaData = readCellData.getFormulaData();
                                    BigDecimal numberValue = readCellData.getNumberValue();
                                    if (ObjectUtil.isNotNull(numberValue)){
                                        boolean matches = numberValue.toString().matches("-?\\d+(\\.\\d+)?");
                                        dataFxMap.put(columnName, ObjectUtil.isNull(formulaData)?matches==true?numberValue:entry.getValue():formulaData.getFormulaValue());
                                    }else
                                        dataFxMap.put(columnName,readCellData.getStringValue());

                                }
                                break;
                            }
                        }
                    }
            }
            dataList.add(dataMap);
            dataFxList.add(dataFxMap);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
    }

    public List<Map<String, Object>> getDataList() {
        return dataList;
    }

    public Map<String, Object> getHeaderMap() {
        return headerMap;
    }

    public List<Map<String, Object>> getDataFxList() {
        return dataFxList;
    }

    public void setDataFxList(List<Map<String, Object>> dataFxList) {
        this.dataFxList = dataFxList;
    }

    @Override
    public void invokeHeadMap(Map headMap, AnalysisContext context) {
        String sheetName=context.readSheetHolder().getSheetName();
        headerMap.put(sheetName,headMap);
    }
}

