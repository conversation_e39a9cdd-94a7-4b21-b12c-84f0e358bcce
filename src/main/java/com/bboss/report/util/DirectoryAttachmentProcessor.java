package com.bboss.report.util;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.FileVisitResult;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.SimpleFileVisitor;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 目录附件处理工具类
 * 提供目录压缩和文件递归遍历功能
 * 
 * <AUTHOR>
 * @create 2025/6/30
 * @since 1.0.0
 */
public class DirectoryAttachmentProcessor {
    
    /**
     * 附件信息类
     */
    public static class AttachmentInfo {
        private final File file;
        private final String displayName;
        private final long size;
        private final boolean isCompressed;
        
        public AttachmentInfo(File file, String displayName, long size, boolean isCompressed) {
            this.file = file;
            this.displayName = displayName;
            this.size = size;
            this.isCompressed = isCompressed;
        }
        
        public File getFile() { return file; }
        public String getDisplayName() { return displayName; }
        public long getSize() { return size; }
        public boolean isCompressed() { return isCompressed; }
        
        public String getSizeString() {
            if (size < 1024) return size + " B";
            if (size < 1024 * 1024) return String.format("%.1f KB", size / 1024.0);
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        }
    }
    
    /**
     * 处理结果类
     */
    public static class ProcessResult {
        private final List<AttachmentInfo> attachments;
        private final long totalSize;
        private final int fileCount;
        private final String summary;
        
        public ProcessResult(List<AttachmentInfo> attachments, long totalSize, int fileCount, String summary) {
            this.attachments = attachments;
            this.totalSize = totalSize;
            this.fileCount = fileCount;
            this.summary = summary;
        }
        
        public List<AttachmentInfo> getAttachments() { return attachments; }
        public long getTotalSize() { return totalSize; }
        public int getFileCount() { return fileCount; }
        public String getSummary() { return summary; }
    }
    
    /**
     * 处理目录附件
     * @param directoryPath 目录路径
     * @param compressMode 是否使用压缩模式
     * @param maxSizeMB 最大大小限制（MB）
     * @return 处理结果
     */
    public static ProcessResult processDirectory(String directoryPath, boolean compressMode, int maxSizeMB) 
            throws IOException {
        File directory = new File(directoryPath);
        if (!directory.exists() || !directory.isDirectory()) {
            throw new IllegalArgumentException("指定的路径不是有效的目录: " + directoryPath);
        }
        
        if (compressMode) {
            return compressDirectory(directory, maxSizeMB);
        } else {
            return expandDirectory(directory, maxSizeMB);
        }
    }
    
    /**
     * 压缩模式：将目录压缩为ZIP文件
     */
    private static ProcessResult compressDirectory(File directory, int maxSizeMB) throws IOException {
        // 创建临时ZIP文件
        File tempZipFile = File.createTempFile("email_attachment_", ".zip");
        tempZipFile.deleteOnExit();
        
        long totalSize = 0;
        int fileCount = 0;
        
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(tempZipFile))) {
            Path basePath = directory.toPath();
            
            Files.walkFileTree(basePath, new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                    if (attrs.isRegularFile()) {
                        // 计算相对路径
                        Path relativePath = basePath.relativize(file);
                        String entryName = relativePath.toString().replace('\\', '/');
                        
                        // 添加ZIP条目
                        ZipEntry entry = new ZipEntry(entryName);
                        entry.setTime(attrs.lastModifiedTime().toMillis());
                        zos.putNextEntry(entry);
                        
                        // 复制文件内容
                        Files.copy(file, zos);
                        zos.closeEntry();
                        
                        return FileVisitResult.CONTINUE;
                    }
                    return FileVisitResult.CONTINUE;
                }
                
                @Override
                public FileVisitResult preVisitDirectory(Path dir, BasicFileAttributes attrs) throws IOException {
                    if (!dir.equals(basePath)) {
                        // 为目录创建ZIP条目
                        Path relativePath = basePath.relativize(dir);
                        String entryName = relativePath.toString().replace('\\', '/') + '/';
                        ZipEntry entry = new ZipEntry(entryName);
                        entry.setTime(attrs.lastModifiedTime().toMillis());
                        zos.putNextEntry(entry);
                        zos.closeEntry();
                    }
                    return FileVisitResult.CONTINUE;
                }
            });
        }
        
        // 计算文件数量
        fileCount = countFilesInDirectory(directory);
        totalSize = tempZipFile.length();
        
        // 检查大小限制
        if (totalSize > (long) maxSizeMB * 1024 * 1024) {
            tempZipFile.delete();
            throw new IOException(String.format("压缩后的文件大小 (%.1f MB) 超过限制 (%d MB)", 
                    totalSize / (1024.0 * 1024.0), maxSizeMB));
        }
        
        List<AttachmentInfo> attachments = new ArrayList<>();
        String displayName = directory.getName() + ".zip";
        attachments.add(new AttachmentInfo(tempZipFile, displayName, totalSize, true));
        
        String summary = String.format("目录 '%s' 已压缩为ZIP文件 (包含 %d 个文件)", 
                directory.getName(), fileCount);
        
        return new ProcessResult(attachments, totalSize, fileCount, summary);
    }
    
    /**
     * 展开模式：递归遍历目录中的所有文件
     */
    private static ProcessResult expandDirectory(File directory, int maxSizeMB) throws IOException {
        List<AttachmentInfo> attachments = new ArrayList<>();
        long totalSize = 0;
        int fileCount = 0;
        long maxSizeBytes = maxSizeMB * 1024L * 1024L;
        
        Path basePath = directory.toPath();
        
        Files.walkFileTree(basePath, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                if (attrs.isRegularFile()) {
                    File actualFile = file.toFile();
                    long fileSize = attrs.size();
                    
                    // 检查单个文件大小
                    if (fileSize > maxSizeBytes) {
                        System.out.println("跳过过大的文件: " + file.getFileName() + 
                                " (" + String.format("%.1f MB", fileSize / (1024.0 * 1024.0)) + ")");
                        return FileVisitResult.CONTINUE;
                    }
                    
                    // 计算相对路径作为显示名称
                    Path relativePath = basePath.relativize(file);
                    String displayName = relativePath.toString();
                    
                    attachments.add(new AttachmentInfo(actualFile, displayName, fileSize, false));
                    
                    return FileVisitResult.CONTINUE;
                }
                return FileVisitResult.CONTINUE;
            }
        });
        
        // 计算总大小和文件数量
        totalSize = attachments.stream().mapToLong(AttachmentInfo::getSize).sum();
        fileCount = attachments.size();
        
        // 检查总大小限制
        if (totalSize > maxSizeBytes) {
            throw new IOException(String.format("目录中所有文件的总大小 (%.1f MB) 超过限制 (%d MB)", 
                    totalSize / (1024.0 * 1024.0), maxSizeMB));
        }
        
        String summary = String.format("目录 '%s' 中的 %d 个文件将作为独立附件发送", 
                directory.getName(), fileCount);
        
        return new ProcessResult(attachments, totalSize, fileCount, summary);
    }
    
    /**
     * 计算目录中的文件数量
     */
    private static int countFilesInDirectory(File directory) throws IOException {
        final int[] count = {0};
        
        Files.walkFileTree(directory.toPath(), new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                if (attrs.isRegularFile()) {
                    count[0]++;
                }
                return FileVisitResult.CONTINUE;
            }
        });
        
        return count[0];
    }
    
    /**
     * 格式化文件大小
     */
    public static String formatFileSize(long size) {
        if (size < 1024) return size + " B";
        if (size < 1024 * 1024) return String.format("%.1f KB", size / 1024.0);
        return String.format("%.1f MB", size / (1024.0 * 1024.0));
    }
    
    /**
     * 清理临时文件
     */
    public static void cleanupTempFiles(List<AttachmentInfo> attachments) {
        for (AttachmentInfo attachment : attachments) {
            if (attachment.isCompressed() && attachment.getFile().getName().startsWith("email_attachment_")) {
                try {
                    attachment.getFile().delete();
                } catch (Exception e) {
                    System.err.println("清理临时文件失败: " + e.getMessage());
                }
            }
        }
    }
}
