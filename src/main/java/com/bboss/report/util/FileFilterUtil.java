package com.bboss.report.util;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 文件过滤工具类
 * 提供基于文件名前缀和扩展名的文件过滤功能，支持递归目录扫描
 * 
 * <AUTHOR>
 * @create 2025/7/2
 * @since 1.0.0
 */
public class FileFilterUtil {

    // 正则表达式编译缓存，提高性能
    private static final Map<String, Pattern> PATTERN_CACHE = new ConcurrentHashMap<>();

    /**
     * 文件过滤结果类
     */
    public static class FilterResult {
        private final List<File> matchedFiles;
        private final long totalSize;
        private final String summary;
        
        public FilterResult(List<File> matchedFiles, long totalSize, String summary) {
            this.matchedFiles = matchedFiles;
            this.totalSize = totalSize;
            this.summary = summary;
        }
        
        public List<File> getMatchedFiles() {
            return matchedFiles;
        }
        
        public long getTotalSize() {
            return totalSize;
        }
        
        public String getSummary() {
            return summary;
        }
        
        public int getFileCount() {
            return matchedFiles.size();
        }
        
        public String getFormattedSize() {
            return formatFileSize(totalSize);
        }
    }
    
    /**
     * 根据前缀和扩展名过滤目录中的文件
     *
     * @param directoryPath 目录路径
     * @param prefixes 文件名前缀列表（为空则不过滤前缀）
     * @param extensions 文件扩展名列表（为空则不过滤扩展名）
     * @param recursive 是否递归扫描子目录
     * @return 过滤结果
     */
    public static FilterResult filterFiles(String directoryPath, List<String> prefixes,
                                         List<String> extensions, boolean recursive) {
        return filterFiles(directoryPath, prefixes, extensions, null, recursive);
    }

    /**
     * 根据包含条件和排除正则表达式过滤目录中的文件
     *
     * @param directoryPath 目录路径
     * @param includePrefixes 包含的文件名前缀列表（为空则不过滤前缀）
     * @param includeExtensions 包含的文件扩展名列表（为空则不过滤扩展名）
     * @param excludePattern 排除文件的正则表达式模式（为空则不排除）
     * @param recursive 是否递归扫描子目录
     * @return 过滤结果
     */
    public static FilterResult filterFiles(String directoryPath, List<String> includePrefixes,
                                         List<String> includeExtensions, String excludePattern,
                                         boolean recursive) {
        if (directoryPath == null || directoryPath.trim().isEmpty()) {
            return new FilterResult(new ArrayList<>(), 0, "目录路径为空");
        }
        
        File directory = new File(directoryPath);
        if (!directory.exists() || !directory.isDirectory()) {
            return new FilterResult(new ArrayList<>(), 0, "指定路径不是有效目录: " + directoryPath);
        }
        
        try {
            List<File> matchedFiles = new ArrayList<>();
            Path dirPath = Paths.get(directoryPath);
            
            // 使用Files.walk进行递归或非递归扫描
            try (Stream<Path> pathStream = recursive ? 
                    Files.walk(dirPath) : 
                    Files.list(dirPath)) {
                
                List<Path> filePaths = pathStream
                    .filter(Files::isRegularFile)
                    .filter(path -> matchesFilter(path.getFileName().toString(),
                        includePrefixes, includeExtensions, excludePattern))
                    .collect(Collectors.toList());
                
                for (Path path : filePaths) {
                    matchedFiles.add(path.toFile());
                }
            }
            
            // 计算总大小
            long totalSize = matchedFiles.stream()
                .mapToLong(File::length)
                .sum();
            
            // 生成摘要信息
            String summary = generateSummary(directory.getName(), matchedFiles.size(),
                                           totalSize, includePrefixes, includeExtensions,
                                           excludePattern, recursive);
            
            return new FilterResult(matchedFiles, totalSize, summary);
            
        } catch (IOException e) {
            String errorMsg = "扫描目录时发生错误: " + e.getMessage();
            System.err.println(errorMsg);
            return new FilterResult(new ArrayList<>(), 0, errorMsg);
        }
    }
    
    /**
     * 检查文件名是否匹配过滤条件（包含AND逻辑，排除正则表达式）
     *
     * @param fileName 文件名
     * @param includePrefixes 包含前缀列表
     * @param includeExtensions 包含扩展名列表
     * @param excludePattern 排除正则表达式模式
     * @return 是否匹配
     */
    private static boolean matchesFilter(String fileName, List<String> includePrefixes,
                                       List<String> includeExtensions, String excludePattern) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return false;
        }

        // 第一步：检查包含条件（AND逻辑）
        boolean includeMatch = checkIncludeConditions(fileName, includePrefixes, includeExtensions);

        // 如果包含条件不满足，直接返回false
        if (!includeMatch) {
            return false;
        }

        // 第二步：检查排除条件（正则表达式）
        boolean excludeMatch = checkExcludePattern(fileName, excludePattern);

        // 最终结果：包含条件满足 AND 排除条件不满足
        return includeMatch && !excludeMatch;
    }

    /**
     * 检查包含条件（AND逻辑）
     * @param fileName 文件名
     * @param includePrefixes 包含前缀列表
     * @param includeExtensions 包含扩展名列表
     * @return 是否满足包含条件
     */
    private static boolean checkIncludeConditions(String fileName, List<String> includePrefixes,
                                                 List<String> includeExtensions) {
        // 检查前缀条件
        boolean prefixMatch = true;
        if (includePrefixes != null && !includePrefixes.isEmpty()) {
            prefixMatch = includePrefixes.stream()
                .filter(prefix -> prefix != null && !prefix.trim().isEmpty())
                .anyMatch(prefix -> fileName.toLowerCase().startsWith(prefix.toLowerCase().trim()));
        }

        // 检查扩展名条件
        boolean extensionMatch = true;
        if (includeExtensions != null && !includeExtensions.isEmpty()) {
            String fileExtension = getFileExtension(fileName);
            extensionMatch = includeExtensions.stream()
                .filter(ext -> ext != null && !ext.trim().isEmpty())
                .anyMatch(ext -> {
                    String cleanExt = cleanExtension(ext);
                    return fileExtension.equals(cleanExt);
                });
        }

        // AND逻辑：前缀和扩展名条件都必须满足
        return prefixMatch && extensionMatch;
    }

    /**
     * 检查排除条件（正则表达式）
     * @param fileName 文件名
     * @param excludePattern 排除正则表达式模式
     * @return 是否满足排除条件
     */
    private static boolean checkExcludePattern(String fileName, String excludePattern) {
        if (excludePattern == null || excludePattern.trim().isEmpty()) {
            return false; // 没有排除模式，不排除任何文件
        }

        try {
            Pattern pattern = getCompiledPattern(excludePattern);
            return pattern.matcher(fileName).matches();
        } catch (PatternSyntaxException e) {
            // 正则表达式语法错误，记录错误但不排除文件
            System.err.println("排除模式正则表达式语法错误: " + e.getMessage());
            return false;
        }
    }

    /**
     * 获取编译后的正则表达式模式（带缓存）
     * @param patternString 正则表达式字符串
     * @return 编译后的Pattern对象
     */
    private static Pattern getCompiledPattern(String patternString) {
        return PATTERN_CACHE.computeIfAbsent(patternString, Pattern::compile);
    }

    /**
     * 验证正则表达式语法
     * @param patternString 正则表达式字符串
     * @return 如果语法有效返回true，否则返回false
     */
    public static boolean isValidRegexPattern(String patternString) {
        if (patternString == null || patternString.trim().isEmpty()) {
            return true; // 空模式视为有效
        }

        try {
            Pattern.compile(patternString);
            return true;
        } catch (PatternSyntaxException e) {
            return false;
        }
    }

    /**
     * 获取正则表达式验证错误信息
     * @param patternString 正则表达式字符串
     * @return 错误信息，如果模式有效则返回null
     */
    public static String getRegexValidationError(String patternString) {
        if (patternString == null || patternString.trim().isEmpty()) {
            return null;
        }

        try {
            Pattern.compile(patternString);
            return null;
        } catch (PatternSyntaxException e) {
            return "正则表达式语法错误: " + e.getMessage();
        }
    }

    /**
     * 清理扩展名格式
     */
    private static String cleanExtension(String ext) {
        String cleanExt = ext.trim().toLowerCase();
        // 移除可能的点号前缀
        if (cleanExt.startsWith(".")) {
            cleanExt = cleanExt.substring(1);
        }
        return cleanExt;
    }

    /**
     * 获取文件扩展名（不包含点号）
     */
    private static String getFileExtension(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        
        return "";
    }
    
    /**
     * 生成过滤结果摘要
     */
    private static String generateSummary(String directoryName, int fileCount, long totalSize,
                                        List<String> includePrefixes, List<String> includeExtensions,
                                        String excludePattern, boolean recursive) {
        StringBuilder summary = new StringBuilder();
        
        summary.append(String.format("在目录 '%s' 中", directoryName));
        if (recursive) {
            summary.append("（包含子目录）");
        }
        
        summary.append(String.format("找到 %d 个匹配文件", fileCount));
        
        if (fileCount > 0) {
            summary.append(String.format("，总大小: %s", formatFileSize(totalSize)));
        }
        
        // 添加包含条件说明
        List<String> includeConditions = new ArrayList<>();
        if (includePrefixes != null && !includePrefixes.isEmpty()) {
            includeConditions.add("前缀: " + String.join(", ", includePrefixes));
        }
        if (includeExtensions != null && !includeExtensions.isEmpty()) {
            includeConditions.add("扩展名: " + String.join(", ", includeExtensions));
        }

        if (!includeConditions.isEmpty()) {
            summary.append("\n包含条件: ").append(String.join(" 且 ", includeConditions));
        }

        // 添加排除条件说明
        if (excludePattern != null && !excludePattern.trim().isEmpty()) {
            summary.append("\n排除模式: ").append(excludePattern);
        }
        
        return summary.toString();
    }
    
    /**
     * 格式化文件大小显示
     */
    public static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    /**
     * 验证目录路径是否有效
     */
    public static boolean isValidDirectory(String directoryPath) {
        if (directoryPath == null || directoryPath.trim().isEmpty()) {
            return false;
        }
        
        File directory = new File(directoryPath);
        return directory.exists() && directory.isDirectory() && directory.canRead();
    }
    
    /**
     * 获取目录中所有文件的预览信息（不进行过滤）
     */
    public static FilterResult getDirectoryPreview(String directoryPath, boolean recursive) {
        return filterFiles(directoryPath, null, null, recursive);
    }
    
    /**
     * 检查文件列表是否超过大小限制
     */
    public static boolean exceedsSizeLimit(List<File> files, long maxSizeBytes) {
        if (files == null || files.isEmpty()) {
            return false;
        }
        
        long totalSize = files.stream().mapToLong(File::length).sum();
        return totalSize > maxSizeBytes;
    }
    
    /**
     * 获取常用文件扩展名列表
     */
    public static List<String> getCommonFileExtensions() {
        List<String> extensions = new ArrayList<>();
        extensions.add("pdf");
        extensions.add("doc");
        extensions.add("docx");
        extensions.add("xls");
        extensions.add("xlsx");
        extensions.add("ppt");
        extensions.add("pptx");
        extensions.add("txt");
        extensions.add("csv");
        extensions.add("zip");
        extensions.add("rar");
        extensions.add("7z");
        extensions.add("jpg");
        extensions.add("jpeg");
        extensions.add("png");
        extensions.add("gif");
        extensions.add("bmp");
        return extensions;
    }
    
    /**
     * 获取常用文件前缀列表
     */
    public static List<String> getCommonFilePrefixes() {
        List<String> prefixes = new ArrayList<>();
        prefixes.add("report_");
        prefixes.add("audit_");
        prefixes.add("summary_");
        prefixes.add("analysis_");
        prefixes.add("data_");
        prefixes.add("export_");
        prefixes.add("backup_");
        prefixes.add("temp_");
        return prefixes;
    }

    /**
     * 获取常用排除模式列表
     */
    public static List<String> getCommonExcludePatterns() {
        List<String> patterns = new ArrayList<>();
        patterns.add("^temp_.*");                    // 临时文件前缀
        patterns.add("^backup_.*");                  // 备份文件前缀
        patterns.add("^\\.");                        // 隐藏文件
        patterns.add(".*\\.tmp$");                   // 临时文件扩展名
        patterns.add(".*\\.bak$");                   // 备份文件扩展名
        patterns.add(".*\\.log$");                   // 日志文件
        patterns.add(".*\\.cache$");                 // 缓存文件
        patterns.add("^~.*");                        // 以~开头的文件
        patterns.add(".*~$");                        // 以~结尾的文件
        patterns.add("^\\$.*");                      // 以$开头的文件
        return patterns;
    }

    /**
     * 获取排除模式示例和说明
     */
    public static Map<String, String> getExcludePatternExamples() {
        Map<String, String> examples = new java.util.LinkedHashMap<>();
        examples.put("^temp_.*", "排除以 temp_ 开头的文件");
        examples.put(".*\\.tmp$", "排除 .tmp 扩展名文件");
        examples.put("^\\.", "排除隐藏文件（以.开头）");
        examples.put("^(temp_|backup_).*", "排除以 temp_ 或 backup_ 开头的文件");
        examples.put(".*\\.(tmp|bak|log)$", "排除 .tmp、.bak、.log 扩展名文件");
        examples.put("^~.*|.*~$", "排除以~开头或结尾的文件");
        examples.put("(?i).*test.*", "排除包含 test 的文件（忽略大小写）");
        examples.put("^[0-9]+\\.txt$", "排除纯数字名称的txt文件");
        return examples;
    }

    /**
     * 清空正则表达式缓存
     */
    public static void clearPatternCache() {
        PATTERN_CACHE.clear();
    }
}
