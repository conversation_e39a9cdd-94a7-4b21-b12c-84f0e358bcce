package com.bboss.report.util;

import com.bboss.report.model.EmailSettings;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * 配置管理器
 * 负责邮件配置的保存和加载
 * 
 * <AUTHOR>
 * @create 2025/6/30
 * @since 1.0.0
 */
public class ConfigManager {
    
    private static final String CONFIG_DIR = System.getProperty("user.home") + File.separator + ".email-sender";
    private static final String CONFIG_FILE = CONFIG_DIR + File.separator + "email-settings.properties";
    private static final String SENDER_EMAILS_FILE = CONFIG_DIR + File.separator + "sender-emails.properties";
    
    // 单例模式
    private static ConfigManager instance;
    private EmailSettings currentSettings;
    
    private ConfigManager() {
        // 确保配置目录存在
        createConfigDirectoryIfNotExists();
        // 加载配置
        loadSettings();
    }
    
    /**
     * 获取ConfigManager实例
     */
    public static synchronized ConfigManager getInstance() {
        if (instance == null) {
            instance = new ConfigManager();
        }
        return instance;
    }
    
    /**
     * 创建配置目录（如果不存在）
     */
    private void createConfigDirectoryIfNotExists() {
        try {
            File configDir = new File(CONFIG_DIR);
            if (!configDir.exists()) {
                configDir.mkdirs();
            }
        } catch (Exception e) {
            System.err.println("创建配置目录失败: " + e.getMessage());
        }
    }
    
    /**
     * 保存邮件设置到配置文件
     * @param settings 要保存的邮件设置
     * @return 保存是否成功
     */
    public boolean saveSettings(EmailSettings settings) {
        try {
            Properties props = new Properties();
            props.setProperty("smtp.host", settings.getSmtpHost() != null ? settings.getSmtpHost() : "");
            props.setProperty("smtp.port", String.valueOf(settings.getSmtpPort()));
            props.setProperty("sender.email", settings.getSenderEmail() != null ? settings.getSenderEmail() : "");
            props.setProperty("sender.password", settings.getSenderPassword() != null ? settings.getSenderPassword() : "");
            props.setProperty("enable.ssl", String.valueOf(settings.isEnableSSL()));
            props.setProperty("sender.name", settings.getSenderName() != null ? settings.getSenderName() : "");
            props.setProperty("compress.directories", String.valueOf(settings.isCompressDirectories()));
            props.setProperty("max.attachment.size.mb", String.valueOf(settings.getMaxAttachmentSizeMB()));
            props.setProperty("max.individual.file.size.mb", String.valueOf(settings.getMaxIndividualFileSizeMB()));

            try (FileOutputStream fos = new FileOutputStream(CONFIG_FILE)) {
                props.store(fos, "Email Settings Configuration");
            }

            this.currentSettings = settings;
            System.out.println("邮件配置已保存到: " + CONFIG_FILE);
            return true;
        } catch (IOException e) {
            System.err.println("保存邮件配置失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 从配置文件加载邮件设置
     * @return 加载的邮件设置，如果加载失败则返回默认设置
     */
    public EmailSettings loadSettings() {
        try {
            File configFile = new File(CONFIG_FILE);
            if (configFile.exists()) {
                Properties props = new Properties();
                try (FileInputStream fis = new FileInputStream(configFile)) {
                    props.load(fis);
                }

                currentSettings = new EmailSettings();
                currentSettings.setSmtpHost(props.getProperty("smtp.host", "smtp.qq.com"));
                currentSettings.setSmtpPort(Integer.parseInt(props.getProperty("smtp.port", "587")));
                currentSettings.setSenderEmail(props.getProperty("sender.email", ""));
                currentSettings.setSenderPassword(props.getProperty("sender.password", ""));
                currentSettings.setEnableSSL(Boolean.parseBoolean(props.getProperty("enable.ssl", "true")));
                currentSettings.setSenderName(props.getProperty("sender.name", ""));
                currentSettings.setCompressDirectories(Boolean.parseBoolean(props.getProperty("compress.directories", "true")));
                currentSettings.setMaxAttachmentSizeMB(Integer.parseInt(props.getProperty("max.attachment.size.mb", "25")));
                currentSettings.setMaxIndividualFileSizeMB(Integer.parseInt(props.getProperty("max.individual.file.size.mb", "10")));

                System.out.println("邮件配置已从文件加载: " + CONFIG_FILE);
            } else {
                // 如果配置文件不存在，创建默认配置
                currentSettings = new EmailSettings();
                System.out.println("配置文件不存在，使用默认配置");
            }
        } catch (Exception e) {
            System.err.println("加载邮件配置失败，使用默认配置: " + e.getMessage());
            currentSettings = new EmailSettings();
        }
        return currentSettings;
    }
    
    /**
     * 获取当前的邮件设置
     * @return 当前的邮件设置
     */
    public EmailSettings getCurrentSettings() {
        if (currentSettings == null) {
            loadSettings();
        }
        return currentSettings;
    }
    
    /**
     * 检查配置文件是否存在
     * @return 如果配置文件存在返回true，否则返回false
     */
    public boolean configFileExists() {
        return Files.exists(Paths.get(CONFIG_FILE));
    }
    
    /**
     * 删除配置文件
     * @return 删除是否成功
     */
    public boolean deleteConfigFile() {
        try {
            File configFile = new File(CONFIG_FILE);
            if (configFile.exists()) {
                boolean deleted = configFile.delete();
                if (deleted) {
                    currentSettings = new EmailSettings(); // 重置为默认配置
                    System.out.println("配置文件已删除");
                }
                return deleted;
            }
            return true; // 文件不存在，认为删除成功
        } catch (Exception e) {
            System.err.println("删除配置文件失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取配置文件路径
     * @return 配置文件的完整路径
     */
    public String getConfigFilePath() {
        return CONFIG_FILE;
    }

    /**
     * 获取保存的发件人邮箱列表
     * @return 发件人邮箱列表
     */
    public List<String> getSavedSenderEmails() {
        List<String> emails = new ArrayList<>();
        try {
            File emailsFile = new File(SENDER_EMAILS_FILE);
            if (emailsFile.exists()) {
                Properties props = new Properties();
                try (FileInputStream fis = new FileInputStream(emailsFile)) {
                    props.load(fis);
                }

                // 读取所有保存的邮箱地址
                int index = 0;
                while (true) {
                    String email = props.getProperty("sender.email." + index);
                    if (email == null || email.trim().isEmpty()) {
                        break;
                    }
                    emails.add(email.trim());
                    index++;
                }
            }
        } catch (Exception e) {
            System.err.println("加载发件人邮箱列表失败: " + e.getMessage());
        }
        return emails;
    }

    /**
     * 保存发件人邮箱列表
     * @param emails 发件人邮箱列表
     * @return 保存是否成功
     */
    public boolean saveSenderEmails(List<String> emails) {
        try {
            Properties props = new Properties();

            // 保存所有邮箱地址
            for (int i = 0; i < emails.size(); i++) {
                props.setProperty("sender.email." + i, emails.get(i));
            }

            // 写入文件
            try (FileOutputStream fos = new FileOutputStream(SENDER_EMAILS_FILE)) {
                props.store(fos, "Saved Sender Email Addresses");
            }

            System.out.println("发件人邮箱列表已保存到: " + SENDER_EMAILS_FILE);
            return true;
        } catch (Exception e) {
            System.err.println("保存发件人邮箱列表失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 添加新的发件人邮箱地址
     * @param email 邮箱地址
     * @return 添加是否成功
     */
    public boolean addSenderEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }

        List<String> emails = getSavedSenderEmails();
        String trimmedEmail = email.trim();

        // 检查是否已存在
        if (!emails.contains(trimmedEmail)) {
            emails.add(trimmedEmail);
            return saveSenderEmails(emails);
        }

        return true; // 已存在，视为成功
    }

    /**
     * 删除发件人邮箱地址
     * @param email 要删除的邮箱地址
     * @return 删除是否成功
     */
    public boolean removeSenderEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }

        List<String> emails = getSavedSenderEmails();
        boolean removed = emails.remove(email.trim());

        if (removed) {
            return saveSenderEmails(emails);
        }

        return true; // 不存在，视为成功
    }
}
