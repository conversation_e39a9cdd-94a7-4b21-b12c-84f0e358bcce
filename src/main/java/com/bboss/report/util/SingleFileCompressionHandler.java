package com.bboss.report.util;

import javafx.application.Platform;
import javafx.concurrent.Task;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 单文件压缩处理器
 * 专门处理邮件模板中指定单个文件的压缩加密功能
 */
public class SingleFileCompressionHandler {
    
    private static final int BUFFER_SIZE = 8192;
    
    /**
     * 单文件压缩结果类
     */
    public static class CompressionResult {
        private final boolean success;
        private final String originalFilePath;
        private final String compressedFilePath;
        private final String errorMessage;
        private final long originalSize;
        private final long compressedSize;
        
        public CompressionResult(boolean success, String originalFilePath, String compressedFilePath, 
                               String errorMessage, long originalSize, long compressedSize) {
            this.success = success;
            this.originalFilePath = originalFilePath;
            this.compressedFilePath = compressedFilePath;
            this.errorMessage = errorMessage;
            this.originalSize = originalSize;
            this.compressedSize = compressedSize;
        }
        
        // Getters
        public boolean isSuccess() { return success; }
        public String getOriginalFilePath() { return originalFilePath; }
        public String getCompressedFilePath() { return compressedFilePath; }
        public String getErrorMessage() { return errorMessage; }
        public long getOriginalSize() { return originalSize; }
        public long getCompressedSize() { return compressedSize; }
        
        public double getCompressionRatio() {
            if (originalSize == 0) return 0;
            return (double) compressedSize / originalSize;
        }
    }
    
    /**
     * 查找匹配目标文件名模式的文件
     * @param attachmentFiles 附件文件列表
     * @param targetFileName 目标文件名模式（支持通配符和正则表达式）
     * @return 匹配的文件，如果没有找到返回null
     */
    public static File findTargetFile(List<File> attachmentFiles, String targetFileName) {
        if (targetFileName == null || targetFileName.trim().isEmpty()) {
            return null;
        }
        
        String pattern = targetFileName.trim();
        
        // 检查是否是精确匹配
        for (File file : attachmentFiles) {
            if (file.getName().equals(pattern)) {
                return file;
            }
        }
        
        // 检查是否是通配符模式
        if (pattern.contains("*") || pattern.contains("?")) {
            String regexPattern = convertWildcardToRegex(pattern);
            Pattern compiledPattern = Pattern.compile(regexPattern, Pattern.CASE_INSENSITIVE);
            
            for (File file : attachmentFiles) {
                if (compiledPattern.matcher(file.getName()).matches()) {
                    return file;
                }
            }
        }
        
        // 检查是否是正则表达式模式（以^开头或$结尾）
        if (pattern.startsWith("^") || pattern.endsWith("$") || pattern.contains("\\")) {
            try {
                Pattern compiledPattern = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                for (File file : attachmentFiles) {
                    if (compiledPattern.matcher(file.getName()).matches()) {
                        return file;
                    }
                }
            } catch (Exception e) {
                // 正则表达式语法错误，忽略
            }
        }
        
        return null;
    }
    
    /**
     * 将通配符模式转换为正则表达式
     */
    private static String convertWildcardToRegex(String wildcard) {
        StringBuilder sb = new StringBuilder();
        sb.append("^");
        
        for (char c : wildcard.toCharArray()) {
            switch (c) {
                case '*':
                    sb.append(".*");
                    break;
                case '?':
                    sb.append(".");
                    break;
                case '.':
                    sb.append("\\.");
                    break;
                case '\\':
                    sb.append("\\\\");
                    break;
                case '^':
                case '$':
                case '(':
                case ')':
                case '[':
                case ']':
                case '{':
                case '}':
                case '+':
                case '|':
                    sb.append("\\").append(c);
                    break;
                default:
                    sb.append(c);
                    break;
            }
        }
        
        sb.append("$");
        return sb.toString();
    }
    
    /**
     * 生成压缩文件路径
     * @param originalFile 原始文件
     * @return 压缩文件路径
     */
    public static String generateCompressedFilePath(File originalFile) {
        String originalPath = originalFile.getAbsolutePath();
        String nameWithoutExt = getFileNameWithoutExtension(originalFile.getName());
        String directory = originalFile.getParent();
        
        return new File(directory, nameWithoutExt + ".zip").getAbsolutePath();
    }
    
    /**
     * 获取不含扩展名的文件名
     */
    private static String getFileNameWithoutExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return fileName.substring(0, lastDotIndex);
        }
        return fileName;
    }
    
    /**
     * 检查压缩文件是否已存在
     * @param compressedFilePath 压缩文件路径
     * @return 如果存在返回true
     */
    public static boolean isCompressedFileExists(String compressedFilePath) {
        return Files.exists(Paths.get(compressedFilePath));
    }
    
    /**
     * 同步压缩单个文件
     * @param sourceFile 源文件
     * @param password 压缩密码（可为null）
     * @return 压缩结果
     */
    public static CompressionResult compressSingleFile(File sourceFile, String password) {
        if (!sourceFile.exists() || !sourceFile.isFile()) {
            return new CompressionResult(false, sourceFile.getAbsolutePath(), null, 
                "源文件不存在或不是文件", 0, 0);
        }
        
        String compressedFilePath = generateCompressedFilePath(sourceFile);
        long originalSize = sourceFile.length();
        
        try {
            // 创建ZIP文件
            try (FileOutputStream fos = new FileOutputStream(compressedFilePath);
                 ZipOutputStream zos = new ZipOutputStream(fos);
                 FileInputStream fis = new FileInputStream(sourceFile)) {
                
                // 添加文件到ZIP
                ZipEntry zipEntry = new ZipEntry(sourceFile.getName());
                zos.putNextEntry(zipEntry);
                
                byte[] buffer = new byte[BUFFER_SIZE];
                int length;
                while ((length = fis.read(buffer)) >= 0) {
                    zos.write(buffer, 0, length);
                }
                
                zos.closeEntry();
            }
            
            File compressedFile = new File(compressedFilePath);
            long compressedSize = compressedFile.length();
            
            return new CompressionResult(true, sourceFile.getAbsolutePath(), compressedFilePath, 
                null, originalSize, compressedSize);
            
        } catch (IOException e) {
            return new CompressionResult(false, sourceFile.getAbsolutePath(), compressedFilePath, 
                "压缩失败: " + e.getMessage(), originalSize, 0);
        }
    }
    
    /**
     * 异步压缩单个文件
     * @param sourceFile 源文件
     * @param password 压缩密码（可为null）
     * @param onSuccess 成功回调
     * @param onError 错误回调
     * @param onProgress 进度回调（可为null）
     */
    public static void compressSingleFileAsync(File sourceFile, String password,
                                             java.util.function.Consumer<CompressionResult> onSuccess,
                                             java.util.function.Consumer<String> onError,
                                             java.util.function.Consumer<Double> onProgress) {
        
        Task<CompressionResult> compressionTask = new Task<CompressionResult>() {
            @Override
            protected CompressionResult call() throws Exception {
                if (onProgress != null) {
                    Platform.runLater(() -> onProgress.accept(0.1));
                }
                
                CompressionResult result = compressSingleFile(sourceFile, password);
                
                if (onProgress != null) {
                    Platform.runLater(() -> onProgress.accept(1.0));
                }
                
                return result;
            }
            
            @Override
            protected void succeeded() {
                CompressionResult result = getValue();
                Platform.runLater(() -> onSuccess.accept(result));
            }
            
            @Override
            protected void failed() {
                Throwable exception = getException();
                String errorMessage = exception != null ? exception.getMessage() : "压缩过程中发生未知错误";
                Platform.runLater(() -> onError.accept(errorMessage));
            }
        };
        
        Thread compressionThread = new Thread(compressionTask);
        compressionThread.setDaemon(true);
        compressionThread.start();
    }
    
    /**
     * 格式化文件大小显示
     */
    public static String formatFileSize(long size) {
        return AttachmentCompressionHandler.formatFileSize(size);
    }
}
