package com.bboss.report.util;

import javafx.scene.Node;
import javafx.scene.control.Control;
import javafx.scene.control.DatePicker;
import javafx.scene.control.Label;
import javafx.scene.control.Skin;
import javafx.scene.control.Spinner;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.util.converter.IntegerStringConverter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class DateTimePicker extends Control {
    private final DatePicker datePicker;
    private final Spinner<Integer> hourSpinner;
    private final Spinner<Integer> minuteSpinner;
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    public DateTimePicker() {
        // 日期选择器, 默认时间是 工作日的 18:30
        LocalDate now = LocalDate.now();
        if (now.getDayOfWeek().getValue() >= 6) {
            now = now.plusDays(8 - now.getDayOfWeek().getValue());
        }
        datePicker = new DatePicker(now);

        // 时间选择器
        hourSpinner = new Spinner<>(0, 23, LocalDateTime.now().getHour());
        minuteSpinner = new Spinner<>(0, 59, LocalDateTime.now().getMinute());

        // 设置为可编辑
        hourSpinner.setEditable(true);
        minuteSpinner.setEditable(true);

        // 设置宽度
        hourSpinner.setPrefWidth(60);
        minuteSpinner.setPrefWidth(60);

        // 设置转换器
        hourSpinner.getValueFactory().setConverter(new IntegerStringConverter());
        minuteSpinner.getValueFactory().setConverter(new IntegerStringConverter());

        // 时间布局
        HBox timeBox = new HBox(5);
        timeBox.getChildren().addAll(
                new Label("时:"), hourSpinner,
                new Label("分:"), minuteSpinner
        );

        // 主布局
        VBox container = new VBox(10);
        container.getChildren().addAll(datePicker, timeBox);

        // 设置皮肤
        setSkin(new DateTimePickerSkin(this, container));
    }

    // 获取yyyy-MM-dd HH:mm格式的字符串
    public String getValue() {
        if (datePicker.getValue() == null) {
            return "";
        }
        LocalDate date = datePicker.getValue();
        LocalDateTime dateTime = LocalDateTime.of(
                date.getYear(),
                date.getMonthValue(),
                date.getDayOfMonth(),
                hourSpinner.getValue(),
                minuteSpinner.getValue()
        );
        return dateTime.format(formatter);
    }

    // 设置值
    public void setValue(String dateTimeString) {
        if (dateTimeString != null) {
            try {
                LocalDateTime dateTime = LocalDateTime.parse(dateTimeString, formatter);
                datePicker.setValue(dateTime.toLocalDate());
                hourSpinner.getValueFactory().setValue(dateTime.getHour());
                minuteSpinner.getValueFactory().setValue(dateTime.getMinute());
            } catch (Exception e) {
                System.err.println("日期时间格式错误: " + dateTimeString);
            }
        }
    }

    // 获取LocalDateTime对象
    public LocalDateTime getDateTime() {
        if (datePicker.getValue() == null) {
            return null;
        }
        LocalDate date = datePicker.getValue();
        return LocalDateTime.of(
                date.getYear(),
                date.getMonthValue(),
                date.getDayOfMonth(),
                hourSpinner.getValue(),
                minuteSpinner.getValue()
        );
    }

    // 设置当前时间
    public void setToNow() {
        LocalDate now = LocalDate.now();
        if (now.getDayOfWeek().getValue() >= 6) {
            now = now.plusDays(8 - now.getDayOfWeek().getValue());
        }
        datePicker.setValue(now);
        hourSpinner.getValueFactory().setValue(18);
        minuteSpinner.getValueFactory().setValue(30);
    }
    class DateTimePickerSkin implements Skin<DateTimePicker> {
        private final DateTimePicker control;
        private final Node node;

        public DateTimePickerSkin(DateTimePicker control, Node node) {
            this.control = control;
            this.node = node;
        }

        @Override
        public DateTimePicker getSkinnable() {
            return control;
        }

        @Override
        public Node getNode() {
            return node;
        }

        @Override
        public void dispose() {
            // 清理资源
        }
    }
}