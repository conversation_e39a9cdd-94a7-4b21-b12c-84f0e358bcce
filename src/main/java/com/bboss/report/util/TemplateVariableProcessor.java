package com.bboss.report.util;

import cn.hutool.core.date.DateUtil;
import com.bboss.report.model.TemplateVariable;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 模板变量处理工具类
 * 负责变量的解析、替换和预设变量的处理
 *
 * <AUTHOR>
 * @create 2025/7/26
 * @since 1.0.0
 */
public class TemplateVariableProcessor {

    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\{\\{([a-zA-Z_][a-zA-Z0-9_]*)\\}\\}");
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 预设变量名称常量
     */
    public static final String PRESET_CURRENT_DATE = "current_date";
    public static final String PRESET_CURRENT_TIME = "current_time";
    public static final String PRESET_CURRENT_DATETIME = "current_datetime";
    public static final String PRESET_ACCT_MONTH = "acct_month";

    /**
     * 从文本中提取所有变量名称
     *
     * @param text 包含变量占位符的文本
     * @return 变量名称集合
     */
    public static Set<String> extractVariableNames(String text) {
        Set<String> variableNames = new HashSet<>();
        if (text == null || text.trim().isEmpty()) {
            return variableNames;
        }

        Matcher matcher = VARIABLE_PATTERN.matcher(text);
        while (matcher.find()) {
            variableNames.add(matcher.group(1));
        }

        return variableNames;
    }

    /**
     * 从主题和正文中提取所有变量名称
     *
     * @param subject 邮件主题
     * @param body    邮件正文
     * @return 变量名称集合
     */
    public static Set<String> extractVariableNames(String subject, String body) {
        Set<String> variableNames = new HashSet<>();
        variableNames.addAll(extractVariableNames(subject));
        variableNames.addAll(extractVariableNames(body));
        return variableNames;
    }

    /**
     * 替换文本中的变量占位符
     *
     * @param text           包含变量占位符的文本
     * @param variableValues 变量值映射
     * @return 替换后的文本
     */
    public static String replaceVariables(String text, Map<String, String> variableValues) {
        if (text == null || text.trim().isEmpty()) {
            return text;
        }

        // 添加预设变量值
        Map<String, String> allValues = new HashMap<>(variableValues);
        allValues.putAll(getPresetVariableValues());

        String result = text;
        for (Map.Entry<String, String> entry : allValues.entrySet()) {
            String placeholder = "{{" + entry.getKey() + "}}";
            String value = entry.getValue() != null ? entry.getValue() : "";
            result = result.replace(placeholder, value);
        }

        return result;
    }

    /**
     * 获取预设变量的值
     *
     * @return 预设变量值映射
     */
    public static Map<String, String> getPresetVariableValues() {
        Map<String, String> presetValues = new HashMap<>();
        LocalDateTime now = LocalDateTime.now();

        presetValues.put(PRESET_CURRENT_DATE, now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        presetValues.put(PRESET_CURRENT_TIME, now.format(DateTimeFormatter.ofPattern("HH:mm:ss")));
        presetValues.put(PRESET_CURRENT_DATETIME, now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        presetValues.put(PRESET_ACCT_MONTH,DateUtil.format( DateUtil.lastMonth(),"yyyyMM"));

        return presetValues;

    }

    /**
     * 获取预设变量定义列表
     *
     * @return 预设变量列表
     */
    public static List<TemplateVariable> getPresetVariables() {
        List<TemplateVariable> presetVariables = new ArrayList<>();

        presetVariables.add(new TemplateVariable(
                PRESET_CURRENT_DATE, "当前日期", TemplateVariable.VariableType.DATE,
                "", false, "自动填入当前日期 (yyyy-MM-dd)"
        ));

        presetVariables.add(new TemplateVariable(
                PRESET_CURRENT_TIME, "当前时间", TemplateVariable.VariableType.TEXT,
                "", false, "自动填入当前时间 (HH:mm:ss)"
        ));

        presetVariables.add(new TemplateVariable(
                PRESET_CURRENT_DATETIME, "当前日期时间", TemplateVariable.VariableType.TEXT,
                "", false, "自动填入当前日期和时间 (yyyy-MM-dd HH:mm:ss)"
        ));

        presetVariables.add(new TemplateVariable(
                PRESET_ACCT_MONTH, "账期", TemplateVariable.VariableType.TEXT,
                "", false, "自动填入上个月日期（yyyyMM）"
        ));


        return presetVariables;
    }

    /**
     * 检查变量名是否为预设变量
     *
     * @param variableName 变量名
     * @return 是否为预设变量
     */
    public static boolean isPresetVariable(String variableName) {
        return PRESET_CURRENT_DATE.equals(variableName) ||
                PRESET_CURRENT_TIME.equals(variableName) ||
                PRESET_CURRENT_DATETIME.equals(variableName) ||
                PRESET_ACCT_MONTH.equals(variableName);
    }

    /**
     * 将变量列表序列化为JSON字符串
     *
     * @param variables 变量列表
     * @return JSON字符串
     */
    public static String serializeVariables(List<TemplateVariable> variables) {
        try {
            return objectMapper.writeValueAsString(variables);
        } catch (Exception e) {
            System.err.println("序列化变量列表失败: " + e.getMessage());
            return "[]";
        }
    }

    /**
     * 从JSON字符串反序列化变量列表
     *
     * @param json JSON字符串
     * @return 变量列表
     */
    public static List<TemplateVariable> deserializeVariables(String json) {
        try {
            if (json == null || json.trim().isEmpty()) {
                return new ArrayList<>();
            }
            return objectMapper.readValue(json, new TypeReference<List<TemplateVariable>>() {
            });
        } catch (Exception e) {
            System.err.println("反序列化变量列表失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 验证变量值映射是否完整
     *
     * @param variables 变量定义列表
     * @param values    变量值映射
     * @return 验证结果，包含错误信息
     */
    public static ValidationResult validateVariableValues(List<TemplateVariable> variables, Map<String, String> values) {
        List<String> errors = new ArrayList<>();

        for (TemplateVariable variable : variables) {
            String value = values.get(variable.getName());

            // 检查必填字段
            if (variable.isRequired() && (value == null || value.trim().isEmpty())) {
                errors.add("变量 \"" + variable.getLabel() + "\" 为必填项");
                continue;
            }

            // 检查值格式
            if (value != null && !value.trim().isEmpty() && !variable.validateValue(value)) {
                errors.add("变量 \"" + variable.getLabel() + "\" 格式不正确：" + variable.getValidationHint());
            }
        }

        return new ValidationResult(errors.isEmpty(), errors);
    }

    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private final boolean valid;
        private final List<String> errors;

        public ValidationResult(boolean valid, List<String> errors) {
            this.valid = valid;
            this.errors = errors;
        }

        public boolean isValid() {
            return valid;
        }

        public List<String> getErrors() {
            return errors;
        }

        public String getErrorMessage() {
            return String.join("\n", errors);
        }
    }

    /**
     * 生成变量使用示例文本
     *
     * @param variables 变量列表
     * @return 示例文本
     */
    public static String generateVariableUsageExample(List<TemplateVariable> variables) {
        if (variables.isEmpty()) {
            return "暂无自定义变量";
        }

        StringBuilder example = new StringBuilder();
        example.append("使用说明：在主题或正文中手动输入以下变量占位符\n");
        example.append("自定义变量：\n");
        for (TemplateVariable variable : variables) {
            example.append("• ").append(variable.getPlaceholder())
                    .append(" - ").append(variable.getLabel());
            if (variable.getDescription() != null && !variable.getDescription().trim().isEmpty()) {
                example.append(" (").append(variable.getDescription()).append(")");
            }
            example.append("\n");
        }

        // 添加预设变量示例
        example.append("\n预设变量：\n");
        for (TemplateVariable preset : getPresetVariables()) {
            example.append("• ").append(preset.getPlaceholder())
                    .append(" - ").append(preset.getLabel())
                    .append(" (").append(preset.getDescription()).append(")\n");
        }

        return example.toString();
    }
}
