package com.bboss.report.util;

import javafx.application.Platform;
import javafx.scene.control.Alert;
import javafx.scene.control.ButtonType;
import javafx.stage.Modality;
import javafx.stage.Window;

import javax.mail.MessagingException;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * SMTP错误处理器
 * 专门处理邮件发送过程中的各种SMTP错误，特别是450错误码
 * 
 * <AUTHOR>
 * @create 2025/7/1
 * @since 1.0.0
 */
public class SmtpErrorHandler {
    
    // SMTP错误码常量
    public static final int ERROR_CODE_450 = 450; // Mail rejected, please try again
    public static final int ERROR_CODE_451 = 451; // Requested action aborted: local error in processing
    public static final int ERROR_CODE_452 = 452; // Requested action not taken: insufficient system storage
    public static final int ERROR_CODE_550 = 550; // Requested action not taken: mailbox unavailable
    public static final int ERROR_CODE_551 = 551; // User not local; please try
    public static final int ERROR_CODE_552 = 552; // Requested mail action aborted: exceeded storage allocation
    public static final int ERROR_CODE_553 = 553; // Requested action not taken: mailbox name not allowed
    public static final int ERROR_CODE_554 = 554; // Transaction failed
    
    // 重试配置
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final int INITIAL_RETRY_DELAY_SECONDS = 5;
    private static final int MAX_RETRY_DELAY_SECONDS = 60;
    
    /**
     * 错误处理结果枚举
     */
    public enum ErrorHandlingResult {
        RETRY,      // 用户选择重试
        CANCEL,     // 用户取消
        IGNORE      // 忽略错误（对于非关键错误）
    }
    
    /**
     * 处理SMTP异常
     * @param exception 发生的异常
     * @param parentWindow 父窗口
     * @return 错误处理结果
     */
    public static ErrorHandlingResult handleSmtpException(Exception exception, Window parentWindow) {
        if (exception instanceof MessagingException) {
            MessagingException msgEx = (MessagingException) exception;
            
            // 检查是否是450错误
            if (isError450(msgEx)) {
                return handle450Error(msgEx, parentWindow);
            }
            
            // 检查是否是其他可重试的错误
            if (isRetryableError(msgEx)) {
                return handleRetryableError(msgEx, parentWindow);
            }
            
            // 处理其他SMTP错误
            return handleGeneralSmtpError(msgEx, parentWindow);
        }
        
        // 处理非SMTP异常
        return handleGeneralError(exception, parentWindow);
    }
    
    /**
     * 检查是否是450错误码
     */
    private static boolean isError450(MessagingException exception) {
        String message = exception.getMessage();
        if (message != null) {
            return message.contains("450") || 
                   message.toLowerCase().contains("mail rejected") ||
                   message.toLowerCase().contains("please try again");
        }
        return false;
    }
    
    /**
     * 检查是否是可重试的错误
     */
    private static boolean isRetryableError(MessagingException exception) {
        String message = exception.getMessage();
        if (message != null) {
            String lowerMessage = message.toLowerCase();
            return lowerMessage.contains("451") || 
                   lowerMessage.contains("452") ||
                   lowerMessage.contains("temporary") ||
                   lowerMessage.contains("try again") ||
                   lowerMessage.contains("busy");
        }
        return false;
    }
    
    /**
     * 处理450错误
     */
    private static ErrorHandlingResult handle450Error(MessagingException exception, Window parentWindow) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("邮件发送临时失败");
        alert.setHeaderText("SMTP 450 错误 - 邮件被临时拒绝");
        
        String content = "邮件服务器临时拒绝了您的邮件发送请求。这通常是由于：\n\n" +
                        "• 服务器当前负载过高\n" +
                        "• 临时的反垃圾邮件策略限制\n" +
                        "• 网络连接不稳定\n\n" +
                        "建议稍后重试发送。\n\n" +
                        "错误详情：" + exception.getMessage();
        
        alert.setContentText(content);
        
        // 添加重试和取消按钮
        ButtonType retryButton = new ButtonType("重试");
        ButtonType cancelButton = new ButtonType("取消");
        alert.getButtonTypes().setAll(retryButton, cancelButton);
        
        // 设置父窗口
        if (parentWindow != null) {
            alert.initOwner(parentWindow);
            alert.initModality(Modality.WINDOW_MODAL);
        }
        
        Optional<ButtonType> result = alert.showAndWait();
        
        if (result.isPresent() && result.get() == retryButton) {
            return ErrorHandlingResult.RETRY;
        } else {
            return ErrorHandlingResult.CANCEL;
        }
    }
    
    /**
     * 处理其他可重试的错误
     */
    private static ErrorHandlingResult handleRetryableError(MessagingException exception, Window parentWindow) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("邮件发送临时失败");
        alert.setHeaderText("SMTP 临时错误");
        
        String content = "邮件发送遇到临时性错误，可能是由于网络或服务器问题。\n\n" +
                        "建议稍后重试发送。\n\n" +
                        "错误详情：" + exception.getMessage();
        
        alert.setContentText(content);
        
        // 添加重试和取消按钮
        ButtonType retryButton = new ButtonType("重试");
        ButtonType cancelButton = new ButtonType("取消");
        alert.getButtonTypes().setAll(retryButton, cancelButton);
        
        // 设置父窗口
        if (parentWindow != null) {
            alert.initOwner(parentWindow);
            alert.initModality(Modality.WINDOW_MODAL);
        }
        
        Optional<ButtonType> result = alert.showAndWait();
        
        if (result.isPresent() && result.get() == retryButton) {
            return ErrorHandlingResult.RETRY;
        } else {
            return ErrorHandlingResult.CANCEL;
        }
    }
    
    /**
     * 处理一般SMTP错误
     */
    private static ErrorHandlingResult handleGeneralSmtpError(MessagingException exception, Window parentWindow) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("邮件发送失败");
        alert.setHeaderText("SMTP 错误");
        
        String content = "邮件发送失败，请检查以下设置：\n\n" +
                        "• SMTP服务器地址和端口\n" +
                        "• 用户名和密码\n" +
                        "• SSL/TLS设置\n" +
                        "• 网络连接\n\n" +
                        "错误详情：" + exception.getMessage();
        
        alert.setContentText(content);
        
        // 设置父窗口
        if (parentWindow != null) {
            alert.initOwner(parentWindow);
            alert.initModality(Modality.WINDOW_MODAL);
        }
        
        alert.showAndWait();
        return ErrorHandlingResult.CANCEL;
    }
    
    /**
     * 处理一般错误
     */
    private static ErrorHandlingResult handleGeneralError(Exception exception, Window parentWindow) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("邮件发送失败");
        alert.setHeaderText("发送错误");
        
        String content = "邮件发送过程中发生错误：\n\n" + exception.getMessage();
        alert.setContentText(content);
        
        // 设置父窗口
        if (parentWindow != null) {
            alert.initOwner(parentWindow);
            alert.initModality(Modality.WINDOW_MODAL);
        }
        
        alert.showAndWait();
        return ErrorHandlingResult.CANCEL;
    }
    
    /**
     * 执行带重试的邮件发送
     * @param sendAction 发送动作
     * @param parentWindow 父窗口
     * @param statusUpdater 状态更新回调
     * @return 发送是否成功
     */
    public static CompletableFuture<Boolean> executeWithRetry(
            ThrowingSupplier<Void> sendAction, 
            Window parentWindow,
            StatusUpdater statusUpdater) {
        
        return CompletableFuture.supplyAsync(() -> {
            AtomicInteger attempts = new AtomicInteger(0);
            int delaySeconds = INITIAL_RETRY_DELAY_SECONDS;
            
            while (attempts.get() < MAX_RETRY_ATTEMPTS) {
                try {
                    int incremented = attempts.incrementAndGet();

                    if (incremented > 1) {
                        Platform.runLater(() -> statusUpdater.updateStatus(
                            "正在重试发送邮件... (第 " + incremented + " 次尝试)"));
                        
                        // 等待重试延迟
                        Thread.sleep(delaySeconds * 1000);
                        delaySeconds = Math.min(delaySeconds * 2, MAX_RETRY_DELAY_SECONDS);
                    }
                    
                    sendAction.get();
                    return true; // 发送成功
                    
                } catch (Exception e) {
                    if (attempts.get() >= MAX_RETRY_ATTEMPTS) {
                        // 最后一次尝试失败，显示错误对话框
                        Platform.runLater(() -> {
                            ErrorHandlingResult result = handleSmtpException(e, parentWindow);
                            if (result == ErrorHandlingResult.RETRY) {
                                // 用户选择重试，重置尝试次数
                                // 注意：这里需要在UI线程中处理，实际实现可能需要调整
                            }
                        });
                        return false;
                    }
                    
                    // 检查是否是可重试的错误
                    if (e instanceof MessagingException) {
                        MessagingException msgEx = (MessagingException) e;
                        if (!isError450(msgEx) && !isRetryableError(msgEx)) {
                            // 不可重试的错误，直接失败
                            Platform.runLater(() -> handleSmtpException(e, parentWindow));
                            return false;
                        }
                    }
                }
            }
            
            return false;
        });
    }
    
    /**
     * 函数式接口：可抛出异常的供应商
     */
    @FunctionalInterface
    public interface ThrowingSupplier<T> {
        T get() throws Exception;
    }
    
    /**
     * 状态更新回调接口
     */
    @FunctionalInterface
    public interface StatusUpdater {
        void updateStatus(String status);
    }
}
