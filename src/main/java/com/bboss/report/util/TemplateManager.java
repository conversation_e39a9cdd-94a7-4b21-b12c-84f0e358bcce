package com.bboss.report.util;

import com.bboss.report.model.EmailTemplate;
import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.File;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 邮件模板管理器
 * 负责邮件模板的数据库操作，包括创建、读取、更新、删除等功能
 * 
 * <AUTHOR>
 * @create 2025/7/2
 * @since 1.0.0
 */
@Slf4j
public class TemplateManager {
    
    private static final String CONFIG_DIR = System.getProperty("user.home") + File.separator + ".email-sender";
    private static final String DATABASE_FILE = CONFIG_DIR + File.separator + "templates.db3";
    private static final String JDBC_URL = "jdbc:sqlite:" + DATABASE_FILE;
    
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private static TemplateManager instance;
    
    private TemplateManager() {
        initializeDatabase();
    }
    
    public static synchronized TemplateManager getInstance() {
        if (instance == null) {
            instance = new TemplateManager();
        }
        return instance;
    }
    
    /**
     * 初始化数据库，创建必要的表结构
     */
    private void initializeDatabase() {
        // 确保配置目录存在
        File configDir = new File(CONFIG_DIR);
        if (!configDir.exists()) {
            configDir.mkdirs();
        }
        
        // 创建基础表结构（不包含可能需要迁移的列）
        String createTableSQL = "CREATE TABLE IF NOT EXISTS email_templates (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
                "name TEXT NOT NULL UNIQUE, " +
                "recipients TEXT, " +
                "cc_recipients TEXT, " +
                "subject TEXT, " +
                "body TEXT, " +
                "file_prefixes TEXT, " +
                "file_extensions TEXT, " +
                "description TEXT, " +
                "created_time TEXT NOT NULL, " +
                "last_modified TEXT NOT NULL, " +
                "use_count INTEGER DEFAULT 0, " +
                "last_used TEXT, " +
                "enabled INTEGER DEFAULT 1" +
                ")";

        try (Connection conn = DriverManager.getConnection(JDBC_URL);
             Statement stmt = conn.createStatement()) {

            stmt.execute(createTableSQL);

            // 执行数据库迁移检查
            performDatabaseMigration(conn);

            System.out.println("邮件模板数据库初始化完成: " + DATABASE_FILE);

        } catch (SQLException e) {
            System.err.println("初始化邮件模板数据库失败: " + e.getMessage());
            log.error("初始化邮件模板数据库失败: ", e);
        }
    }

    /**
     * 执行数据库迁移，处理版本升级时的表结构变更
     */
    private void performDatabaseMigration(Connection conn) throws SQLException {
        // 检查并添加 variables 列（用于模板变量功能）
        if (!columnExists(conn, "email_templates", "variables")) {
            addVariablesColumn(conn);
        }

        // 检查并添加文件压缩相关列
        if (!columnExists(conn, "email_templates", "enable_compression")) {
            addCompressionColumns(conn);
        }

        // 检查并添加正则表达式排除模式列
        if (!columnExists(conn, "email_templates", "exclude_file_pattern")) {
            addExcludePatternColumn(conn);
        }

        // 检查并添加单文件压缩相关列
        if (!columnExists(conn, "email_templates", "single_file_compression_enabled")) {
            addSingleFileCompressionColumns(conn);
        }

        // 未来可以在这里添加其他迁移逻辑
        // 例如：添加新列、修改列类型、创建索引等
    }

    /**
     * 检查指定表中是否存在指定列
     */
    private boolean columnExists(Connection conn, String tableName, String columnName) throws SQLException {
        String sql = "PRAGMA table_info(" + tableName + ")";

        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                String existingColumnName = rs.getString("name");
                if (columnName.equalsIgnoreCase(existingColumnName)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 添加 variables 列到 email_templates 表
     */
    private void addVariablesColumn(Connection conn) throws SQLException {
        String alterTableSQL = "ALTER TABLE email_templates ADD COLUMN variables TEXT";

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(alterTableSQL);
            log.info("数据库迁移：成功添加 variables 列到 email_templates 表");

            // 为现有记录设置默认值（空字符串表示没有变量定义）
            String updateSQL = "UPDATE email_templates SET variables = '' WHERE variables IS NULL";
            stmt.execute(updateSQL);
            log.info("数据库迁移：为现有模板记录设置默认变量值");

        } catch (SQLException e) {
            System.err.println("添加 variables 列失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 添加文件压缩相关列到 email_templates 表
     */
    private void addCompressionColumns(Connection conn) throws SQLException {
        try (Statement stmt = conn.createStatement()) {
            // 添加 enable_compression 列
            String alterTableSQL1 = "ALTER TABLE email_templates ADD COLUMN enable_compression INTEGER DEFAULT 0";
            stmt.execute(alterTableSQL1);
            log.info("数据库迁移：成功添加 enable_compression 列到 email_templates 表");

            // 添加 compression_password 列
            String alterTableSQL2 = "ALTER TABLE email_templates ADD COLUMN compression_password TEXT";
            stmt.execute(alterTableSQL2);
            log.info("数据库迁移：成功添加 compression_password 列到 email_templates 表");

            // 为现有记录设置默认值
            String updateSQL = "UPDATE email_templates SET enable_compression = 0, compression_password = NULL WHERE enable_compression IS NULL";
            stmt.execute(updateSQL);
            log.info("数据库迁移：为现有模板记录设置默认压缩配置值");

        } catch (SQLException e) {
            System.err.println("添加文件压缩列失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 添加正则表达式排除模式列到 email_templates 表
     */
    private void addExcludePatternColumn(Connection conn) throws SQLException {
        try (Statement stmt = conn.createStatement()) {
            // 添加 exclude_file_pattern 列
            String alterTableSQL = "ALTER TABLE email_templates ADD COLUMN exclude_file_pattern TEXT";
            stmt.execute(alterTableSQL);
            log.info("数据库迁移：成功添加 exclude_file_pattern 列到 email_templates 表");

            // 为现有记录设置默认值
            String updateSQL = "UPDATE email_templates SET exclude_file_pattern = '' WHERE exclude_file_pattern IS NULL";
            stmt.execute(updateSQL);
            log.info("数据库迁移：为现有模板记录设置默认排除模式值");

        } catch (SQLException e) {
            System.err.println("添加排除模式列失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 添加单文件压缩相关列到 email_templates 表
     */
    private void addSingleFileCompressionColumns(Connection conn) throws SQLException {
        try (Statement stmt = conn.createStatement()) {
            // 添加 single_file_compression_enabled 列
            String alterTableSQL1 = "ALTER TABLE email_templates ADD COLUMN single_file_compression_enabled INTEGER DEFAULT 0";
            stmt.execute(alterTableSQL1);
            log.info("数据库迁移：成功添加 single_file_compression_enabled 列到 email_templates 表");

            // 添加 target_file_name 列
            String alterTableSQL2 = "ALTER TABLE email_templates ADD COLUMN target_file_name TEXT";
            stmt.execute(alterTableSQL2);
            log.info("数据库迁移：成功添加 target_file_name 列到 email_templates 表");

            // 添加 single_file_compression_password 列
            String alterTableSQL3 = "ALTER TABLE email_templates ADD COLUMN single_file_compression_password TEXT";
            stmt.execute(alterTableSQL3);
            log.info("数据库迁移：成功添加 single_file_compression_password 列到 email_templates 表");

            // 为现有记录设置默认值
            String updateSQL = "UPDATE email_templates SET single_file_compression_enabled = 0, target_file_name = '', single_file_compression_password = NULL WHERE single_file_compression_enabled IS NULL";
            stmt.execute(updateSQL);
            log.info("数据库迁移：为现有模板记录设置默认单文件压缩配置值");

        } catch (SQLException e) {
            System.err.println("添加单文件压缩列失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 保存邮件模板
     */
    public boolean saveTemplate(EmailTemplate template) {
        if (template == null || !template.isValid()) {
            return false;
        }
        
        String sql;
        if (template.getId() == null) {
            // 新增模板
            sql = "INSERT INTO email_templates (name, recipients, cc_recipients, subject, body, " +
                  "file_prefixes, file_extensions, exclude_file_pattern, " +
                  "description, variables, enable_compression, compression_password, " +
                  "single_file_compression_enabled, target_file_name, single_file_compression_password, " +
                  "created_time, last_modified, use_count, enabled) " +
                  "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        } else {
            // 更新模板
            sql = "UPDATE email_templates SET name = ?, recipients = ?, cc_recipients = ?, " +
                  "subject = ?, body = ?, file_prefixes = ?, " +
                  "file_extensions = ?, exclude_file_pattern = ?, " +
                  "description = ?, variables = ?, enable_compression = ?, compression_password = ?, " +
                  "single_file_compression_enabled = ?, target_file_name = ?, single_file_compression_password = ?, " +
                  "last_modified = ?, enabled = ? " +
                  "WHERE id = ?";
        }
        
        try (Connection conn = DriverManager.getConnection(JDBC_URL);
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            
            pstmt.setString(1, template.getName());
            pstmt.setString(2, template.getRecipients());
            pstmt.setString(3, template.getCcRecipients());
            pstmt.setString(4, template.getSubject());
            pstmt.setString(5, template.getBody());
            pstmt.setString(6, template.getFilePrefixes());
            pstmt.setString(7, template.getFileExtensions());
            pstmt.setString(8, template.getExcludeFilePattern() != null ? template.getExcludeFilePattern() : "");
            pstmt.setString(9, template.getDescription());
            pstmt.setString(10, template.getVariables() != null ? template.getVariables() : "");
            pstmt.setInt(11, template.isEnableCompression() ? 1 : 0);
            pstmt.setString(12, template.getCompressionPassword());
            pstmt.setInt(13, template.isSingleFileCompressionEnabled() ? 1 : 0);
            pstmt.setString(14, template.getTargetFileName() != null ? template.getTargetFileName() : "");
            pstmt.setString(15, template.getSingleFileCompressionPassword());

            if (template.getId() == null) {
                // 新增模板
                pstmt.setString(16, template.getCreatedTime().format(DATETIME_FORMATTER));
                pstmt.setString(17, template.getLastModified().format(DATETIME_FORMATTER));
                pstmt.setInt(18, template.getUseCount());
                pstmt.setInt(19, template.isEnabled() ? 1 : 0);
            } else {
                // 更新模板
                pstmt.setString(16, template.getLastModified().format(DATETIME_FORMATTER));
                pstmt.setInt(17, template.isEnabled() ? 1 : 0);
                pstmt.setLong(18, template.getId());
            }
            
            int affectedRows = pstmt.executeUpdate();
            
            if (affectedRows > 0 && template.getId() == null) {
                // 获取生成的ID
                try (ResultSet generatedKeys = pstmt.getGeneratedKeys()) {
                    if (generatedKeys.next()) {
                        template.setId(generatedKeys.getLong(1));
                    }
                }
            }
            log.info("保存邮件模板成功: " + template.getName());
            return affectedRows > 0;
            
        } catch (SQLException e) {
            System.err.println("保存邮件模板失败: " + e.getMessage());
            log.error("保存邮件模板失败: ", e);
            return false;
        }
    }
    
    /**
     * 根据ID获取邮件模板
     */
    public EmailTemplate getTemplate(Long id) {
        if (id == null) {
            return null;
        }
        
        String sql = "SELECT * FROM email_templates WHERE id = ?";
        
        try (Connection conn = DriverManager.getConnection(JDBC_URL);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setLong(1, id);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToTemplate(rs);
                }
            }
            
        } catch (SQLException e) {
            System.err.println("获取邮件模板失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * 根据名称获取邮件模板
     */
    public EmailTemplate getTemplateByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }
        
        String sql = "SELECT * FROM email_templates WHERE name = ?";
        
        try (Connection conn = DriverManager.getConnection(JDBC_URL);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, name.trim());
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToTemplate(rs);
                }
            }
            
        } catch (SQLException e) {
            System.err.println("根据名称获取邮件模板失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * 获取所有邮件模板
     */
    public List<EmailTemplate> getAllTemplates() {
        return getAllTemplates(true);
    }
    
    /**
     * 获取所有邮件模板
     * @param enabledOnly 是否只获取启用的模板
     */
    public List<EmailTemplate> getAllTemplates(boolean enabledOnly) {
        List<EmailTemplate> templates = new ArrayList<>();
        
        String sql = enabledOnly ? 
            "SELECT * FROM email_templates WHERE enabled = 1 ORDER BY name" :
            "SELECT * FROM email_templates ORDER BY name";
        
        try (Connection conn = DriverManager.getConnection(JDBC_URL);
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            while (rs.next()) {
                templates.add(mapResultSetToTemplate(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("获取邮件模板列表失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return templates;
    }
    
    /**
     * 删除邮件模板
     */
    public boolean deleteTemplate(Long id) {
        if (id == null) {
            return false;
        }
        
        String sql = "DELETE FROM email_templates WHERE id = ?";
        
        try (Connection conn = DriverManager.getConnection(JDBC_URL);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setLong(1, id);
            int affectedRows = pstmt.executeUpdate();
            
            return affectedRows > 0;
            
        } catch (SQLException e) {
            System.err.println("删除邮件模板失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 更新模板使用统计
     */
    public boolean updateTemplateUsage(Long id) {
        if (id == null) {
            return false;
        }
        
        String sql = "UPDATE email_templates " +
                     "SET use_count = use_count + 1, " +
                     "last_used = ?, " +
                     "last_modified = ? " +
                     "WHERE id = ?";
        
        try (Connection conn = DriverManager.getConnection(JDBC_URL);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            String now = LocalDateTime.now().format(DATETIME_FORMATTER);
            pstmt.setString(1, now);
            pstmt.setString(2, now);
            pstmt.setLong(3, id);
            
            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;
            
        } catch (SQLException e) {
            System.err.println("更新模板使用统计失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 将ResultSet映射为EmailTemplate对象
     */
    private EmailTemplate mapResultSetToTemplate(ResultSet rs) throws SQLException {
        EmailTemplate template = new EmailTemplate();
        
        template.setId(rs.getLong("id"));
        template.setName(rs.getString("name"));
        template.setRecipients(rs.getString("recipients"));
        template.setCcRecipients(rs.getString("cc_recipients"));
        template.setSubject(rs.getString("subject"));
        template.setBody(rs.getString("body"));
        template.setFilePrefixes(rs.getString("file_prefixes"));
        template.setFileExtensions(rs.getString("file_extensions"));

        // 处理排除模式字段（可能不存在于旧数据库中）
        try {
            template.setExcludeFilePattern(rs.getString("exclude_file_pattern"));
        } catch (SQLException e) {
            template.setExcludeFilePattern(""); // 默认为空
        }

        template.setDescription(rs.getString("description"));
        template.setVariables(rs.getString("variables"));
        template.setUseCount(rs.getInt("use_count"));
        template.setEnabled(rs.getInt("enabled") == 1);

        // 处理压缩相关字段（可能不存在于旧数据库中）
        try {
            template.setEnableCompression(rs.getInt("enable_compression") == 1);
        } catch (SQLException e) {
            template.setEnableCompression(false); // 默认不启用压缩
        }

        try {
            template.setCompressionPassword(rs.getString("compression_password"));
        } catch (SQLException e) {
            template.setCompressionPassword(null); // 默认无密码
        }

        // 处理单文件压缩相关字段（可能不存在于旧数据库中）
        try {
            template.setSingleFileCompressionEnabled(rs.getInt("single_file_compression_enabled") == 1);
        } catch (SQLException e) {
            template.setSingleFileCompressionEnabled(false); // 默认不启用单文件压缩
        }

        try {
            template.setTargetFileName(rs.getString("target_file_name"));
        } catch (SQLException e) {
            template.setTargetFileName(""); // 默认无目标文件名
        }

        try {
            template.setSingleFileCompressionPassword(rs.getString("single_file_compression_password"));
        } catch (SQLException e) {
            template.setSingleFileCompressionPassword(null); // 默认无单文件压缩密码
        }
        
        // 解析时间字段
        String createdTimeStr = rs.getString("created_time");
        if (createdTimeStr != null) {
            template.setCreatedTime(LocalDateTime.parse(createdTimeStr, DATETIME_FORMATTER));
        }
        
        String lastModifiedStr = rs.getString("last_modified");
        if (lastModifiedStr != null) {
            template.setLastModified(LocalDateTime.parse(lastModifiedStr, DATETIME_FORMATTER));
        }
        
        String lastUsedStr = rs.getString("last_used");
        if (lastUsedStr != null) {
            template.setLastUsed(LocalDateTime.parse(lastUsedStr, DATETIME_FORMATTER));
        }
        
        return template;
    }

    /**
     * 导出模板到XML文件
     * @param templates 要导出的模板列表
     * @param file 目标文件
     * @return 是否导出成功
     */
    public boolean exportTemplatesToXML(List<EmailTemplate> templates, File file) {
        try {
            DocumentBuilderFactory docFactory = DocumentBuilderFactory.newInstance();
            DocumentBuilder docBuilder = docFactory.newDocumentBuilder();
            Document doc = docBuilder.newDocument();

            // 根元素
            Element rootElement = doc.createElement("emailTemplates");
            doc.appendChild(rootElement);

            // 添加版本信息
            rootElement.setAttribute("version", "1.1");
            rootElement.setAttribute("exportTime", LocalDateTime.now().format(DATETIME_FORMATTER));

            for (EmailTemplate template : templates) {
                Element templateElement = doc.createElement("template");
                rootElement.appendChild(templateElement);

                addElementWithText(doc, templateElement, "name", template.getName());
                addElementWithText(doc, templateElement, "description", template.getDescription());
                addElementWithText(doc, templateElement, "recipients", template.getRecipients());
                addElementWithText(doc, templateElement, "ccRecipients", template.getCcRecipients());
                addElementWithText(doc, templateElement, "subject", template.getSubject());
                addElementWithText(doc, templateElement, "body", template.getBody());
                addElementWithText(doc, templateElement, "filePrefixes", template.getFilePrefixes());
                addElementWithText(doc, templateElement, "fileExtensions", template.getFileExtensions());
                addElementWithText(doc, templateElement, "excludeFilePattern", template.getExcludeFilePattern());
                addElementWithText(doc, templateElement, "variables", template.getVariables());
                addElementWithText(doc, templateElement, "enabled", String.valueOf(template.isEnabled()));
                addElementWithText(doc, templateElement, "enableCompression", String.valueOf(template.isEnableCompression()));
                addElementWithText(doc, templateElement, "compressionPassword", template.getCompressionPassword());
                addElementWithText(doc, templateElement, "singleFileCompressionEnabled", String.valueOf(template.isSingleFileCompressionEnabled()));
                addElementWithText(doc, templateElement, "targetFileName", template.getTargetFileName());
                addElementWithText(doc, templateElement, "singleFileCompressionPassword", template.getSingleFileCompressionPassword());

                if (template.getCreatedTime() != null) {
                    addElementWithText(doc, templateElement, "createdTime",
                        template.getCreatedTime().format(DATETIME_FORMATTER));
                }
                if (template.getLastModified() != null) {
                    addElementWithText(doc, templateElement, "lastModified",
                        template.getLastModified().format(DATETIME_FORMATTER));
                }
            }

            // 写入文件
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            Transformer transformer = transformerFactory.newTransformer();
            transformer.setOutputProperty(OutputKeys.INDENT, "yes");
            transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");

            DOMSource source = new DOMSource(doc);
            StreamResult result = new StreamResult(file);
            transformer.transform(source, result);

            return true;
        } catch (Exception e) {
            log.error("导出模板到XML文件时发生错误: ", e);
            return false;
        }
    }

    /**
     * 从XML文件导入模板
     * @param file 源文件
     * @return 导入的模板列表，失败时返回null
     */
    public List<EmailTemplate> importTemplatesFromXML(File file) {
        try {
            DocumentBuilderFactory docFactory = DocumentBuilderFactory.newInstance();
            DocumentBuilder docBuilder = docFactory.newDocumentBuilder();
            Document doc = docBuilder.parse(file);

            doc.getDocumentElement().normalize();

            NodeList templateNodes = doc.getElementsByTagName("template");
            List<EmailTemplate> templates = new ArrayList<>();

            for (int i = 0; i < templateNodes.getLength(); i++) {
                Node templateNode = templateNodes.item(i);

                if (templateNode.getNodeType() == Node.ELEMENT_NODE) {
                    Element templateElement = (Element) templateNode;
                    EmailTemplate template = new EmailTemplate();

                    template.setName(getElementText(templateElement, "name"));
                    template.setDescription(getElementText(templateElement, "description"));
                    template.setRecipients(getElementText(templateElement, "recipients"));
                    template.setCcRecipients(getElementText(templateElement, "ccRecipients"));
                    template.setSubject(getElementText(templateElement, "subject"));
                    template.setBody(getElementText(templateElement, "body"));
                    template.setFilePrefixes(getElementText(templateElement, "filePrefixes"));
                    template.setFileExtensions(getElementText(templateElement, "fileExtensions"));
                    template.setExcludeFilePattern(getElementText(templateElement, "excludeFilePattern"));
                    template.setVariables(getElementText(templateElement, "variables"));

                    String enabledStr = getElementText(templateElement, "enabled");
                    if (enabledStr != null && !enabledStr.isEmpty()) {
                        template.setEnabled(Boolean.parseBoolean(enabledStr));
                    }

                    String enableCompressionStr = getElementText(templateElement, "enableCompression");
                    if (enableCompressionStr != null && !enableCompressionStr.isEmpty()) {
                        template.setEnableCompression(Boolean.parseBoolean(enableCompressionStr));
                    }

                    String compressionPasswordStr = getElementText(templateElement, "compressionPassword");
                    template.setCompressionPassword(compressionPasswordStr);

                    String singleFileCompressionEnabledStr = getElementText(templateElement, "singleFileCompressionEnabled");
                    if (singleFileCompressionEnabledStr != null && !singleFileCompressionEnabledStr.isEmpty()) {
                        template.setSingleFileCompressionEnabled(Boolean.parseBoolean(singleFileCompressionEnabledStr));
                    }

                    String targetFileNameStr = getElementText(templateElement, "targetFileName");
                    template.setTargetFileName(targetFileNameStr);

                    String singleFileCompressionPasswordStr = getElementText(templateElement, "singleFileCompressionPassword");
                    template.setSingleFileCompressionPassword(singleFileCompressionPasswordStr);

                    String createdTimeStr = getElementText(templateElement, "createdTime");
                    if (createdTimeStr != null && !createdTimeStr.isEmpty()) {
                        template.setCreatedTime(LocalDateTime.parse(createdTimeStr, DATETIME_FORMATTER));
                    }

                    String lastModifiedStr = getElementText(templateElement, "lastModified");
                    if (lastModifiedStr != null && !lastModifiedStr.isEmpty()) {
                        template.setLastModified(LocalDateTime.parse(lastModifiedStr, DATETIME_FORMATTER));
                    }

                    templates.add(template);
                }
            }

            return templates;
        } catch (Exception e) {
            log.error("从XML文件导入模板时发生错误: ", e);
            return null;
        }
    }

    /**
     * 批量保存模板（用于导入）
     * @param templates 要保存的模板列表
     * @param overwriteExisting 是否覆盖已存在的模板
     * @return 成功保存的模板数量
     */
    public int batchSaveTemplates(List<EmailTemplate> templates, boolean overwriteExisting) {
        int successCount = 0;

        for (EmailTemplate template : templates) {
            // 检查是否已存在同名模板
            EmailTemplate existing = getTemplateByName(template.getName());

            if (existing != null && !overwriteExisting) {
                continue; // 跳过已存在的模板
            }

            if (existing != null && overwriteExisting) {
                template.setId(existing.getId()); // 保持原有ID进行更新
            }

            if (saveTemplate(template)) {
                successCount++;
            }
        }

        return successCount;
    }

    /**
     * 添加带文本内容的XML元素
     */
    private void addElementWithText(Document doc, Element parent, String tagName, String textContent) {
        Element element = doc.createElement(tagName);
        if (textContent != null) {
            element.appendChild(doc.createTextNode(textContent));
        }
        parent.appendChild(element);
    }

    /**
     * 获取XML元素的文本内容
     */
    private String getElementText(Element parent, String tagName) {
        NodeList nodeList = parent.getElementsByTagName(tagName);
        if (nodeList.getLength() > 0) {
            Node node = nodeList.item(0);
            if (node != null) {
                return node.getTextContent();
            }
        }
        return null;
    }
}
