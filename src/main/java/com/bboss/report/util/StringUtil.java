package com.bboss.report.util;

import java.util.Map;

public class StringUtil {
    public static boolean mapContainsString(Map<String, Object> map, String searchString) {
        for (Object value : map.values()) {
            if (value instanceof String && ((String) value).contains(searchString)) {
                return true;
            }
        }
        return false;
    }
    public static boolean StringContains(String value, String searchString) {
        if (value.contains(searchString)) {
            return true;
        }
        return false;
    }
}
