package com.bboss.report.util;

import org.springframework.util.CollectionUtils;

import java.io.*;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjuster;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 
 * 工具类
 * <AUTHOR>
 * 
 */
public class CommonUtil {

	public static <T> boolean checkList(List<T> list) {
		if(list != null && !list.isEmpty()) {
			return true;
		}else {
			return false;
		}
		
	}

	/**
	 * 校验list
	 * @param list
	 * @return
	 */
	public static <T> boolean isNotNullList(List<T> list) {
		if (list != null && !list.isEmpty()) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 校验list
	 * @param list
	 * @return
	 */
	public static <T> boolean isNullList(List<T> list) {
		return !isNotNullList(list);
	}
	
	public static <T> boolean checkSet(Set<T> set) {
		if(set != null && !set.isEmpty()){
			return true;
		}else{
			return false;
		}
		
	}
	
	public static <T,E> boolean checkMap(Map<T,E> map){
		if(map != null && !map.isEmpty()){
			return true;
		}else{
			return false;
		}
		
	}

	public static boolean isNotNull(Object object) {
		boolean flag = true;
		
		if(object==null){
			flag = false;
		}else if( object instanceof String){
			if(((String)object).trim().length() == 0){
				flag = false;
			}else{
				flag =true;
			}
		}
		else if(object instanceof List){
			if(((List)object).size()==0){
				flag = false;
			}else{
				flag = true;
			}
		}else if(object instanceof Map){
			if( ((Map) object).size() == 0 ){
				flag = false;
			}else{
				flag = true;
			}
		}else if(object instanceof Set){
			if( ((Set) object).size() == 0){
				flag = false;
			}else{
				flag = true;
			}
		}
		return flag;
	}
	
	public static List<String> toList(String src,String separator){
		List<String> destLst = new ArrayList<String>();
		if(CommonUtil.isNotNull(  src )){
			destLst = Arrays.asList(src.split(separator));//根据逗号分隔转化为list
		}
		return destLst;
	}




}
