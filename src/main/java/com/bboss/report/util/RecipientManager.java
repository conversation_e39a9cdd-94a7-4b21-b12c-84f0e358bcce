package com.bboss.report.util;

import com.bboss.report.model.RecipientInfo;

import java.io.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 收件人管理器
 * 负责收件人信息的保存、加载和管理
 * 
 * <AUTHOR>
 * @create 2025/7/1
 * @since 1.0.0
 */
public class RecipientManager {
    
    private static final String CONFIG_DIR = System.getProperty("user.home") + File.separator + ".email-sender";
    private static final String RECIPIENTS_FILE = CONFIG_DIR + File.separator + "recipients.properties";
    private static final String TAGS_FILE = CONFIG_DIR + File.separator + "recipient-tags.properties";
    
    private static RecipientManager instance;
    private Map<String, RecipientInfo> recipients;
    private Set<String> availableTags;
    
    private RecipientManager() {
        this.recipients = new HashMap<>();
        this.availableTags = new HashSet<>();
        loadRecipients();
        loadTags();
    }
    
    public static synchronized RecipientManager getInstance() {
        if (instance == null) {
            instance = new RecipientManager();
        }
        return instance;
    }
    
    /**
     * 添加或更新收件人信息
     */
    public void addOrUpdateRecipient(String email, String displayName, String type) {
        if (email == null || email.trim().isEmpty()) {
            return;
        }
        
        email = email.trim().toLowerCase();
        RecipientInfo recipient = recipients.get(email);
        
        if (recipient == null) {
            recipient = new RecipientInfo(email, displayName != null ? displayName : email);
            recipient.setType(type);
            recipients.put(email, recipient);
        } else {
            // 更新现有收件人信息
            if (displayName != null && !displayName.trim().isEmpty()) {
                recipient.setDisplayName(displayName.trim());
            }
            recipient.setType(type);
        }
        
        recipient.incrementUseCount();
        saveRecipients();
    }
    
    /**
     * 批量添加收件人（从邮件发送成功后调用）
     */
    public void addRecipientsFromEmail(String recipients, String ccRecipients) {
        // 处理主收件人
        if (recipients != null && !recipients.trim().isEmpty()) {
            String[] recipientArray = recipients.split("[;,]");
            for (String recipient : recipientArray) {
                String email = extractEmail(recipient.trim());
                String displayName = extractDisplayName(recipient.trim());
                addOrUpdateRecipient(email, displayName, "TO");
            }
        }
        
        // 处理抄送收件人
        if (ccRecipients != null && !ccRecipients.trim().isEmpty()) {
            String[] ccArray = ccRecipients.split("[;,]");
            for (String cc : ccArray) {
                String email = extractEmail(cc.trim());
                String displayName = extractDisplayName(cc.trim());
                addOrUpdateRecipient(email, displayName, "CC");
            }
        }
    }
    
    /**
     * 从字符串中提取邮箱地址
     */
    private String extractEmail(String input) {
        if (input == null || input.trim().isEmpty()) {
            return "";
        }
        
        // 处理格式：Name <<EMAIL>> 或直接的 <EMAIL>
        if (input.contains("<") && input.contains(">")) {
            int start = input.indexOf("<") + 1;
            int end = input.indexOf(">");
            return input.substring(start, end).trim();
        }
        
        return input.trim();
    }
    
    /**
     * 从字符串中提取显示名称
     */
    private String extractDisplayName(String input) {
        if (input == null || input.trim().isEmpty()) {
            return "";
        }
        
        // 处理格式：Name <<EMAIL>>
        if (input.contains("<")) {
            return input.substring(0, input.indexOf("<")).trim();
        }
        
        return input.trim();
    }
    
    /**
     * 获取所有收件人
     */
    public List<RecipientInfo> getAllRecipients() {
        return new ArrayList<>(recipients.values());
    }
    
    /**
     * 根据标签筛选收件人
     */
    public List<RecipientInfo> getRecipientsByTag(String tag) {
        return recipients.values().stream()
                .filter(r -> r.hasTag(tag))
                .collect(Collectors.toList());
    }
    
    /**
     * 根据类型筛选收件人
     */
    public List<RecipientInfo> getRecipientsByType(String type) {
        return recipients.values().stream()
                .filter(r -> type.equals(r.getType()))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取最常用的收件人（按使用频次排序）
     */
    public List<RecipientInfo> getMostUsedRecipients(int limit) {
        return recipients.values().stream()
                .sorted((r1, r2) -> Integer.compare(r2.getUseCount(), r1.getUseCount()))
                .limit(limit)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取最近使用的收件人（按最后使用时间排序）
     */
    public List<RecipientInfo> getRecentlyUsedRecipients(int limit) {
        try {
            if (recipients == null || recipients.isEmpty()) {
                return new ArrayList<>();
            }

            return recipients.values().stream()
                    .filter(r -> r != null && r.getLastUsed() != null)
                    .sorted((r1, r2) -> {
                        try {
                            return r2.getLastUsed().compareTo(r1.getLastUsed());
                        } catch (Exception e) {
                            return 0; // 如果比较失败，认为相等
                        }
                    })
                    .limit(limit)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            System.err.println("获取最近使用的收件人时发生错误: " + e.getMessage());
            return new ArrayList<>();
        }
    }
    
    /**
     * 删除收件人
     */
    public boolean removeRecipient(String email) {
        if (email != null) {
            RecipientInfo removed = recipients.remove(email.trim().toLowerCase());
            if (removed != null) {
                saveRecipients();
                return true;
            }
        }
        return false;
    }
    
    /**
     * 获取收件人信息
     */
    public RecipientInfo getRecipient(String email) {
        if (email != null) {
            return recipients.get(email.trim().toLowerCase());
        }
        return null;
    }
    
    /**
     * 添加标签
     */
    public void addTag(String tag) {
        if (tag != null && !tag.trim().isEmpty()) {
            availableTags.add(tag.trim());
            saveTags();
        }
    }
    
    /**
     * 删除标签
     */
    public boolean removeTag(String tag) {
        if (availableTags.remove(tag)) {
            // 从所有收件人中移除该标签
            recipients.values().forEach(r -> r.removeTag(tag));
            saveTags();
            saveRecipients();
            return true;
        }
        return false;
    }
    
    /**
     * 获取所有可用标签
     */
    public Set<String> getAvailableTags() {
        return new HashSet<>(availableTags);
    }
    
    /**
     * 为收件人添加标签
     */
    public void addTagToRecipient(String email, String tag) {
        RecipientInfo recipient = getRecipient(email);
        if (recipient != null && tag != null && !tag.trim().isEmpty()) {
            recipient.addTag(tag.trim());
            availableTags.add(tag.trim());
            saveRecipients();
            saveTags();
        }
    }
    
    /**
     * 从收件人移除标签
     */
    public void removeTagFromRecipient(String email, String tag) {
        RecipientInfo recipient = getRecipient(email);
        if (recipient != null) {
            recipient.removeTag(tag);
            saveRecipients();
        }
    }
    
    /**
     * 保存收件人信息到文件
     */
    private void saveRecipients() {
        try {
            // 确保目录存在
            File configDir = new File(CONFIG_DIR);
            if (!configDir.exists()) {
                configDir.mkdirs();
            }
            
            Properties props = new Properties();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            
            for (RecipientInfo recipient : recipients.values()) {
                String prefix = "recipient." + recipient.getEmail();
                props.setProperty(prefix + ".displayName", recipient.getDisplayName());
                props.setProperty(prefix + ".tags", recipient.getTagsAsString());
                props.setProperty(prefix + ".useCount", String.valueOf(recipient.getUseCount()));
                props.setProperty(prefix + ".lastUsed", recipient.getLastUsed().format(formatter));
                props.setProperty(prefix + ".type", recipient.getType());
            }
            
            try (FileOutputStream fos = new FileOutputStream(RECIPIENTS_FILE)) {
                props.store(fos, "Recipient Information");
            }
            
        } catch (Exception e) {
            System.err.println("保存收件人信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 从文件加载收件人信息
     */
    private void loadRecipients() {
        try {
            File file = new File(RECIPIENTS_FILE);
            if (!file.exists()) {
                return;
            }
            
            Properties props = new Properties();
            try (FileInputStream fis = new FileInputStream(file)) {
                props.load(fis);
            }
            
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            Set<String> emails = new HashSet<>();
            
            // 提取所有邮箱地址
            for (String key : props.stringPropertyNames()) {
                if (key.startsWith("recipient.") && key.endsWith(".displayName")) {
                    String email = key.substring("recipient.".length(), key.length() - ".displayName".length());
                    emails.add(email);
                }
            }
            
            // 加载每个收件人的信息
            for (String email : emails) {
                String prefix = "recipient." + email;
                String displayName = props.getProperty(prefix + ".displayName", email);
                String tagsString = props.getProperty(prefix + ".tags", "");
                int useCount = Integer.parseInt(props.getProperty(prefix + ".useCount", "0"));
                String lastUsedString = props.getProperty(prefix + ".lastUsed", LocalDateTime.now().format(formatter));
                String type = props.getProperty(prefix + ".type", "TO");
                
                RecipientInfo recipient = new RecipientInfo(email, displayName);
                recipient.setTagsFromString(tagsString);
                recipient.setUseCount(useCount);
                recipient.setLastUsed(LocalDateTime.parse(lastUsedString, formatter));
                recipient.setType(type);
                
                recipients.put(email, recipient);
                
                // 添加标签到可用标签集合
                availableTags.addAll(recipient.getTags());
            }
            
        } catch (Exception e) {
            System.err.println("加载收件人信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 保存标签信息到文件
     */
    private void saveTags() {
        try {
            Properties props = new Properties();
            int index = 0;
            for (String tag : availableTags) {
                props.setProperty("tag." + index, tag);
                index++;
            }
            
            try (FileOutputStream fos = new FileOutputStream(TAGS_FILE)) {
                props.store(fos, "Available Tags");
            }
            
        } catch (Exception e) {
            System.err.println("保存标签信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 从文件加载标签信息
     */
    private void loadTags() {
        try {
            File file = new File(TAGS_FILE);
            if (!file.exists()) {
                // 添加一些默认标签
                availableTags.add("客户");
                availableTags.add("同事");
                availableTags.add("朋友");
                availableTags.add("家人");
                saveTags();
                return;
            }
            
            Properties props = new Properties();
            try (FileInputStream fis = new FileInputStream(file)) {
                props.load(fis);
            }
            
            for (String key : props.stringPropertyNames()) {
                if (key.startsWith("tag.")) {
                    availableTags.add(props.getProperty(key));
                }
            }
            
        } catch (Exception e) {
            System.err.println("加载标签信息失败: " + e.getMessage());
        }
    }
}
