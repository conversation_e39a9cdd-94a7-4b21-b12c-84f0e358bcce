package com.bboss.report.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class ExcelUtil {
    /**
     * 读取excel,放入List<Map<String, String>>
     * @param fileName 读取excel的文件名称
     * @param  sheetName sheetName
     * @return datalist
     */
    public static List<Map<String, Object>> readExcelToMap(String fileName, String sheetName) {
        List<Map<String, Object>> dataList = new ArrayList<>();
        EasyExcel.read(fileName, new AnalysisEventListener<Map<String, Object>>() {
            //用于存储表头的信息
            private Map<Integer, String> headMap;

            //读取excel表头信息
            @Override
            public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                this.headMap = headMap;
                log.info("Excel读取完成,文件名:" + fileName + ",sheet:" + sheetName +"表头信息：" + headMap);
            }

            //直接使用Map来保存数据
            @Override
            public void invoke(Map<String, Object> valueData, AnalysisContext context) {
                //把表头和值放入Map
                HashMap<String, Object> paramsMap = new HashMap<>();
                for (int i = 0; i < valueData.size(); i++) {
                    String key = headMap.get(i);
                    Object value = valueData.get(i);
                    //将表头作为map的key，每行每个单元格的数据作为map的value
                    paramsMap.put(key, value);
                }
                dataList.add(paramsMap);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                log.info("Excel读取完成,文件名:" + fileName + ",sheet:" + sheetName + ",行数：" + dataList.size());
            }
        }).sheet(sheetName).doRead();

        return dataList;
    }
}
