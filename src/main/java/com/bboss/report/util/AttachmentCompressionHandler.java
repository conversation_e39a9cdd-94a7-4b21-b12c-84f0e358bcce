package com.bboss.report.util;

import javafx.application.Platform;
import javafx.concurrent.Task;
import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.model.enums.AesKeyStrength;
import net.lingala.zip4j.model.enums.CompressionLevel;
import net.lingala.zip4j.model.enums.EncryptionMethod;

import javax.activation.DataHandler;
import javax.activation.FileDataSource;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class AttachmentCompressionHandler {

    private static final long MAX_ATTACHMENT_SIZE = 25 * 1024 * 1024; // 25MB
    private static final int BUFFER_SIZE = 8192;

    /**
     * 附件验证结果类
     */
    public static class AttachmentValidationResult {
        private final List<File> validFiles;
        private final List<File> oversizedFiles;
        private final long totalSize;
        private final boolean exceedsTotalLimit;

        public AttachmentValidationResult(List<File> validFiles, List<File> oversizedFiles,
                                        long totalSize, boolean exceedsTotalLimit) {
            this.validFiles = validFiles;
            this.oversizedFiles = oversizedFiles;
            this.totalSize = totalSize;
            this.exceedsTotalLimit = exceedsTotalLimit;
        }

        public List<File> getValidFiles() { return validFiles; }
        public List<File> getOversizedFiles() { return oversizedFiles; }
        public long getTotalSize() { return totalSize; }
        public boolean exceedsTotalLimit() { return exceedsTotalLimit; }
        public boolean hasOversizedFiles() { return !oversizedFiles.isEmpty(); }
    }

    /**
     * 智能处理选项枚举
     */
    public enum ProcessingOption {
        COMPRESS_ATTACHMENTS,
        SEND_IN_BATCHES,
        CANCEL
    }
    
    /**
     * 验证附件大小限制
     * @param files 要验证的文件数组
     * @param maxIndividualSizeMB 单个文件最大大小（MB）
     * @param maxTotalSizeMB 总大小限制（MB）
     * @return 验证结果
     */
    public static AttachmentValidationResult validateAttachments(File[] files,
                                                               int maxIndividualSizeMB,
                                                               int maxTotalSizeMB) {
        List<File> validFiles = new ArrayList<>();
        List<File> oversizedFiles = new ArrayList<>();
        long totalSize = 0;

        long maxIndividualSizeBytes = maxIndividualSizeMB * 1024L * 1024L;
        long maxTotalSizeBytes = maxTotalSizeMB * 1024L * 1024L;

        for (File file : files) {
            if (file.exists()) {
                long fileSize = file.length();
                if (fileSize > maxIndividualSizeBytes) {
                    oversizedFiles.add(file);
                } else {
                    validFiles.add(file);
                    totalSize += fileSize;
                }
            }
        }

        boolean exceedsTotalLimit = totalSize > maxTotalSizeBytes;

        return new AttachmentValidationResult(validFiles, oversizedFiles, totalSize, exceedsTotalLimit);
    }

    /**
     * 压缩多个文件为ZIP
     */
    public File compressFiles(File[] files, String zipFileName) throws IOException {
        return compressFiles(files, zipFileName, null);
    }

    /**
     * 压缩多个文件为ZIP（支持密码保护）
     * @param files 要压缩的文件数组
     * @param zipFileName ZIP文件名
     * @param password 压缩密码（可为null表示无密码）
     * @return 压缩后的ZIP文件
     */
    public File compressFiles(File[] files, String zipFileName, String password) throws IOException {
        File zipFile = new File(zipFileName);

        // 注意：标准Java ZIP不支持密码保护，这里先实现基础压缩
        // 如果需要密码保护，需要使用第三方库如zip4j
        try (FileOutputStream fos = new FileOutputStream(zipFile);
             ZipOutputStream zos = new ZipOutputStream(fos)) {

            for (File file : files) {
                if (file.exists() && file.isFile()) {
                    addFileToZip(file, zos);
                }
            }
        }

        return zipFile;
    }
    
    /**
     * 添加文件到ZIP
     */
    private void addFileToZip(File file, ZipOutputStream zos) throws IOException {
        try (FileInputStream fis = new FileInputStream(file)) {
            ZipEntry zipEntry = new ZipEntry(file.getName());
            zos.putNextEntry(zipEntry);
            
            byte[] buffer = new byte[BUFFER_SIZE];
            int length;
            while ((length = fis.read(buffer)) >= 0) {
                zos.write(buffer, 0, length);
            }
            
            zos.closeEntry();
        }
    }
    
    /**
     * 分割大文件
     */
    public List<File> splitLargeFile(File file, long maxChunkSize) throws IOException {
        List<File> chunks = new ArrayList<>();
        
        if (file.length() <= maxChunkSize) {
            chunks.add(file);
            return chunks;
        }
        
        String fileName = file.getName();
        String baseName = fileName.substring(0, fileName.lastIndexOf('.'));
        String extension = fileName.substring(fileName.lastIndexOf('.'));
        
        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] buffer = new byte[BUFFER_SIZE];
            int chunkNumber = 1;
            long totalBytesRead = 0;
            
            while (totalBytesRead < file.length()) {
                String chunkFileName = baseName + "_part" + chunkNumber + extension;
                File chunkFile = new File(file.getParent(), chunkFileName);
                
                try (FileOutputStream fos = new FileOutputStream(chunkFile)) {
                    long bytesWritten = 0;
                    int bytesRead;
                    
                    while (bytesWritten < maxChunkSize && 
                           (bytesRead = fis.read(buffer)) != -1) {
                        
                        long bytesToWrite = Math.min(bytesRead, maxChunkSize - bytesWritten);
                        fos.write(buffer, 0, (int) bytesToWrite);
                        bytesWritten += bytesToWrite;
                        totalBytesRead += bytesToWrite;
                        
                        // 如果没有完全读取缓冲区，需要调整文件指针
                        if (bytesToWrite < bytesRead) {
                            fis.getChannel().position(totalBytesRead);
                            break;
                        }
                    }
                }
                
                chunks.add(chunkFile);
                chunkNumber++;
            }
        }
        
        return chunks;
    }
    
    /**
     * 智能处理附件（压缩或分割）
     */
    public void sendEmailWithSmartAttachments(String[] recipients, String subject, 
                                            String content, File[] attachments,
                                            Session session) throws MessagingException, IOException {
        
        if (attachments == null || attachments.length == 0) {
            sendSimpleEmail(recipients, subject, content, session);
            return;
        }
        
        // 计算总大小
        long totalSize = 0;
        for (File file : attachments) {
            totalSize += file.length();
        }
        
        if (totalSize <= MAX_ATTACHMENT_SIZE) {
            // 直接发送
            sendEmailWithAttachments(recipients, subject, content, attachments, session);
        } else if (attachments.length > 1) {
            // 多个文件，尝试压缩
            String zipFileName = "attachments_" + System.currentTimeMillis() + ".zip";
            File zipFile = compressFiles(attachments, zipFileName);
            
            if (zipFile.length() <= MAX_ATTACHMENT_SIZE) {
                // 压缩后符合大小要求
                sendEmailWithAttachments(recipients, subject + " (压缩文件)", 
                                       content, new File[]{zipFile}, session);
                zipFile.delete(); // 清理临时文件
            } else {
                // 压缩后仍然过大，分批发送
                sendEmailInBatches(recipients, subject, content, attachments, session);
            }
        } else {
            // 单个大文件，分割发送
            File largeFile = attachments[0];
            List<File> chunks = splitLargeFile(largeFile, MAX_ATTACHMENT_SIZE);
            
            for (int i = 0; i < chunks.size(); i++) {
                String chunkSubject = subject + " (第 " + (i + 1) + " 部分，共 " + chunks.size() + " 部分)";
                sendEmailWithAttachments(recipients, chunkSubject, content, 
                                       new File[]{chunks.get(i)}, session);
            }
            
            // 清理临时文件
            for (File chunk : chunks) {
                if (!chunk.equals(largeFile)) {
                    chunk.delete();
                }
            }
        }
    }
    
    /**
     * 分批发送邮件
     */
    private void sendEmailInBatches(String[] recipients, String subject, 
                                  String content, File[] attachments, 
                                  Session session) throws MessagingException, IOException {
        
        List<List<File>> batches = new ArrayList<>();
        List<File> currentBatch = new ArrayList<>();
        long currentBatchSize = 0;
        
        for (File file : attachments) {
            long fileSize = file.length();
            
            if (currentBatchSize + fileSize > MAX_ATTACHMENT_SIZE && !currentBatch.isEmpty()) {
                batches.add(new ArrayList<>(currentBatch));
                currentBatch.clear();
                currentBatchSize = 0;
            }
            
            currentBatch.add(file);
            currentBatchSize += fileSize;
        }
        
        if (!currentBatch.isEmpty()) {
            batches.add(currentBatch);
        }
        
        // 发送每个批次
        for (int i = 0; i < batches.size(); i++) {
            String batchSubject = subject + " (第 " + (i + 1) + " 批，共 " + batches.size() + " 批)";
            File[] batchFiles = batches.get(i).toArray(new File[0]);
            sendEmailWithAttachments(recipients, batchSubject, content, batchFiles, session);
        }
    }
    
    /**
     * 发送带附件的邮件
     */
    private void sendEmailWithAttachments(String[] recipients, String subject, 
                                        String content, File[] attachments, 
                                        Session session) throws MessagingException {
        
        MimeMessage message = new MimeMessage(session);
        message.setFrom(new InternetAddress("<EMAIL>"));
        
        InternetAddress[] addressTo = new InternetAddress[recipients.length];
        for (int i = 0; i < recipients.length; i++) {
            addressTo[i] = new InternetAddress(recipients[i]);
        }
        message.setRecipients(Message.RecipientType.TO, addressTo);
        message.setSubject(subject);
        
        Multipart multipart = new MimeMultipart();
        
        // 添加正文
        MimeBodyPart textPart = new MimeBodyPart();
        textPart.setText(content);
        multipart.addBodyPart(textPart);
        
        // 添加附件
        for (File attachment : attachments) {
            MimeBodyPart attachmentPart = new MimeBodyPart();
            try {
                FileDataSource fileDataSource = new FileDataSource(attachment);
                attachmentPart.setDataHandler(new DataHandler(fileDataSource));
                attachmentPart.setFileName(attachment.getName());
            } catch (Exception e) {
                throw new MessagingException("无法添加附件: " + attachment.getName(), e);
            }
            multipart.addBodyPart(attachmentPart);
        }
        
        message.setContent(multipart);
        Transport.send(message);
    }
    
    /**
     * 发送简单邮件（无附件）
     */
    private void sendSimpleEmail(String[] recipients, String subject, 
                               String content, Session session) throws MessagingException {
        
        MimeMessage message = new MimeMessage(session);
        message.setFrom(new InternetAddress("<EMAIL>"));
        
        InternetAddress[] addressTo = new InternetAddress[recipients.length];
        for (int i = 0; i < recipients.length; i++) {
            addressTo[i] = new InternetAddress(recipients[i]);
        }
        message.setRecipients(Message.RecipientType.TO, addressTo);
        message.setSubject(subject);
        message.setText(content);
        
        Transport.send(message);
    }

    /**
     * 异步压缩文件（用于模板压缩功能）
     * @param files 要压缩的文件数组
     * @param zipFileName ZIP文件名
     * @param password 压缩密码（可为null）
     * @param onSuccess 成功回调
     * @param onError 错误回调
     * @param onProgress 进度回调（可为null）
     */
    public static void compressFilesAsync(File[] files, String zipFileName, String password,
                                        Runnable onSuccess,
                                        java.util.function.Consumer<String> onError,
                                        java.util.function.Consumer<Double> onProgress) {

        Task<File> compressionTask = new Task<File>() {
            @Override
            protected File call() throws Exception {
                File zipFile = new File(zipFileName);
                long totalSize = 0;
                long processedSize = 0;

                // 计算总大小
                for (File file : files) {
                    if (file.exists() && file.isFile()) {
                        totalSize += file.length();
                    }
                }

                // 使用 zip4j 创建带密码的压缩文件
                try (ZipFile zip = new ZipFile(zipFile)) {

                    // 设置压缩参数
                    ZipParameters zipParameters = new ZipParameters();
                    zipParameters.setCompressionLevel(CompressionLevel.ULTRA); // 最高压缩级别

                    // 如果提供了密码，则启用加密
                    if (password != null && !password.isEmpty()) {
                        zipParameters.setEncryptFiles(true);
                        zipParameters.setEncryptionMethod(EncryptionMethod.AES);
                        zipParameters.setAesKeyStrength(AesKeyStrength.KEY_STRENGTH_256);
                        zip.setPassword(password.toCharArray());
                    }

                    // 添加文件到压缩包
                    for (int i = 0; i < files.length; i++) {
                        File file = files[i];
                        if (file.exists() && file.isFile()) {
                            // 添加文件
                            zip.addFile(file, zipParameters);
                            processedSize += file.length();

                            // 更新进度
                            if (onProgress != null && totalSize > 0) {
                                double progress = (double) processedSize / totalSize;
                                Platform.runLater(() -> onProgress.accept(progress));
                            }
                        }
                    }
                }

                return zipFile;
            }

            @Override
            protected void succeeded() {
                Platform.runLater(onSuccess);
            }

            @Override
            protected void failed() {
                Throwable exception = getException();
                String errorMessage = exception != null ? exception.getMessage() : "压缩过程中发生未知错误";
                Platform.runLater(() -> onError.accept(errorMessage));
            }
        };

        Thread compressionThread = new Thread(compressionTask);
        compressionThread.setDaemon(true);
        compressionThread.start();
    }

    /**
     * 添加文件到ZIP（带进度支持）
     */
    private static void addFileToZipWithProgress(File file, ZipOutputStream zos) throws IOException {
        try (FileInputStream fis = new FileInputStream(file)) {
            ZipEntry zipEntry = new ZipEntry(file.getName());
            zos.putNextEntry(zipEntry);

            byte[] buffer = new byte[BUFFER_SIZE];
            int length;
            while ((length = fis.read(buffer)) >= 0) {
                zos.write(buffer, 0, length);
            }

            zos.closeEntry();
        }
    }

    /**
     * 格式化文件大小显示
     */
    public static String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }
}