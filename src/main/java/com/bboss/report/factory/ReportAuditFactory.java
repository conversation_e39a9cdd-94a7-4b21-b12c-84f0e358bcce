package com.bboss.report.factory;

import cn.hutool.core.util.ObjectUtil;
import com.bboss.report.service.ReportAuditService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ReportAuditFactory implements ApplicationContextAware {
    public static Map<String, ReportAuditService> serviceMap = new HashMap<String, ReportAuditService>();
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, ReportAuditService> map = applicationContext.getBeansOfType(ReportAuditService.class);
        for (Map.Entry<String, ReportAuditService> data : map.entrySet()) {
            List<String> reportKeyList = data.getValue().getReportKey();
            if (CollectionUtils.isNotEmpty(reportKeyList)) {
                for (String rKey : reportKeyList) {
                    serviceMap.put(rKey, data.getValue());
                }
            }
        }
    }

    public static ReportAuditService getReportAudiService(String rKey) {
        ReportAuditService service = serviceMap.get(rKey);
        if (ObjectUtil.isNotNull(service)) {
            return service;
        } else {
            return null;
        }
    }
}
