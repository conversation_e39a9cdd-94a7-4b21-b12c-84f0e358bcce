<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<Pane fx:id="rootPane" maxHeight="-Infinity" maxWidth="-Infinity" prefHeight="600" prefWidth="800" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.bboss.report.controller.MainController">
    <Label layoutX="50.0" layoutY="50.0" text="账期：" />
    <TextField fx:id="settleMonth" layoutX="125.0" layoutY="50.0" prefHeight="23.0" prefWidth="154.0" />
    <Label layoutX="50.0" layoutY="100.0" text="报表目录：" />
    <Button fx:id="btnChooseFile" layoutX="125.0" layoutY="100.0" mnemonicParsing="false" onAction="#onBtnChooseFileClick" text="选择文件夹" />
    <Label layoutX="50.0" layoutY="150.0" text="运行日志:" />
    <TextArea fx:id="runLog" editable="false" layoutX="125.0" layoutY="150.0" prefHeight="400.0" prefWidth="639.0" />
    <!-- 底部按钮区域 -->
    <Button fx:id="btnConfirm" layoutX="300.0" layoutY="570.0" mnemonicParsing="false" onAction="#onBtnConfirmClick" text="确认稽核" />
    <Button fx:id="btnSendEmail" layoutX="420.0" layoutY="570.0" mnemonicParsing="false" onAction="#onBtnSendEmailClick" text="邮件发送" style="-fx-background-color: #007bff; -fx-text-fill: white;" />
    
    <!-- 版本号显示 -->
    <Label fx:id="versionLabel" layoutX="650.0" layoutY="575.0" text="v20250116143025" style="-fx-font-size: 10px; -fx-text-fill: #888888;" />
</Pane>
