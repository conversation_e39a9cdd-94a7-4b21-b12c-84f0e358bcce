<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.CheckBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.PasswordField?>
<?import javafx.scene.control.RadioButton?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.control.ToggleGroup?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<!--
  邮件发送设置对话框界面
  包含SMTP服务器配置、发件人信息等设置项
-->
<VBox prefHeight="450.0" prefWidth="500.0" spacing="15.0" style="-fx-background-color: #f8f9fa;" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.bboss.report.controller.SettingsController">
    <padding>
        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
    </padding>

    <!-- 标题 -->
    <Label text="邮件发送设置">
        <font>
            <Font name="System Bold" size="18.0" />
        </font>
    </Label>
    <Separator prefWidth="200.0" />

    <!-- SMTP服务器配置区域 -->
    <Label text="SMTP服务器配置">
        <font>
            <Font name="System Bold" size="14.0" />
        </font>
    </Label>
    
    <GridPane hgap="10.0" vgap="12.0">
        <columnConstraints>
            <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
            <ColumnConstraints hgrow="ALWAYS" />
        </columnConstraints>

        <!-- SMTP服务器地址 -->
        <Label text="SMTP服务器:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
        <TextField fx:id="smtpHostField" promptText="例如: smtp.139.com" GridPane.columnIndex="1" GridPane.rowIndex="0" />

        <!-- SMTP端口号 -->
        <Label text="端口号:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
        <TextField fx:id="smtpPortField" promptText="例如: 587 或 465" GridPane.columnIndex="1" GridPane.rowIndex="1" />

        <!-- SSL/TLS加密选项 -->
        <Label text="启用SSL/TLS:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
        <CheckBox fx:id="enableSSLCheckBox" text="启用加密连接" GridPane.columnIndex="1" GridPane.rowIndex="2" />
    </GridPane>

    <Separator prefWidth="200.0" />

    <!-- 发件人信息配置区域 -->
    <Label text="发件人信息">
        <font>
            <Font name="System Bold" size="14.0" />
        </font>
    </Label>
    
    <GridPane hgap="10.0" vgap="12.0">
        <columnConstraints>
            <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
            <ColumnConstraints hgrow="ALWAYS" />
        </columnConstraints>

        <!-- 发件人邮箱地址 -->
        <Label text="邮箱地址:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
        <HBox spacing="5.0" GridPane.columnIndex="1" GridPane.rowIndex="0">
            <ComboBox fx:id="senderEmailComboBoxSettings" editable="true" promptText="选择或输入邮箱地址" HBox.hgrow="ALWAYS" />
            <Button fx:id="deleteSenderEmailButton" text="删除" style="-fx-text-fill: red;" />
        </HBox>

        <!-- 发件人邮箱密码/授权码 -->
        <Label text="密码/授权码:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
        <PasswordField fx:id="senderPasswordField" promptText="请输入邮箱密码或授权码" GridPane.columnIndex="1" GridPane.rowIndex="1" />

        <!-- 发件人显示名称 -->
        <Label text="显示名称:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
        <TextField fx:id="senderNameField" promptText="发件人显示名称（可选）" GridPane.columnIndex="1" GridPane.rowIndex="2" />
    </GridPane>
    <!-- 提示信息 -->
    <Label fx:id="tipLabel" text="提示：139邮箱请使用授权码，可在邮箱设置中开启SMTP服务并生成授权码。"
           textFill="#666666" wrapText="true" style="-fx-font-size: 12px;" />
    <Separator prefWidth="200.0" />

    <!-- 附件处理配置区域 -->
    <Label text="附件处理设置">
        <font>
            <Font name="System Bold" size="14.0" />
        </font>
    </Label>

    <GridPane hgap="10.0" vgap="12.0">
        <columnConstraints>
            <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
            <ColumnConstraints hgrow="ALWAYS" />
        </columnConstraints>

        <!-- 目录处理模式 -->
        <Label text="目录处理模式:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
        <VBox spacing="5.0" GridPane.columnIndex="1" GridPane.rowIndex="0">
            <RadioButton fx:id="compressModeRadio" text="压缩模式 - 将目录压缩为ZIP文件" selected="true">
                <toggleGroup>
                    <ToggleGroup fx:id="directoryModeGroup" />
                </toggleGroup>
            </RadioButton>
            <RadioButton fx:id="expandModeRadio" text="展开模式 - 递归添加目录中的所有文件" toggleGroup="$directoryModeGroup" />
        </VBox>

        <!-- 最大附件大小 -->
        <Label text="附件总大小限制:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
        <HBox spacing="5.0" alignment="CENTER_LEFT" GridPane.columnIndex="1" GridPane.rowIndex="1">
            <TextField fx:id="maxSizeField" promptText="25" prefWidth="80.0" />
            <Label text="MB" />
        </HBox>

        <!-- 单个文件大小限制 -->
        <Label text="单文件大小限制:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
        <HBox spacing="5.0" alignment="CENTER_LEFT" GridPane.columnIndex="1" GridPane.rowIndex="2">
            <TextField fx:id="maxIndividualSizeField" promptText="10" prefWidth="80.0" />
            <Label text="MB" />
        </HBox>
    </GridPane>



    <!-- 底部按钮区域 -->
    <HBox alignment="CENTER_RIGHT" spacing="15.0" VBox.vgrow="NEVER">
        <VBox.margin>
            <Insets top="20.0" />
        </VBox.margin>
        
        <!-- 状态标签 -->
        <Label fx:id="statusLabel" text="" textFill="#666666" HBox.hgrow="ALWAYS" />
        
        <!-- 测试连接按钮 -->
        <Button fx:id="testConnectionButton" mnemonicParsing="false" text="测试连接" 
                style="-fx-background-color: #17a2b8; -fx-text-fill: white;" />
        
        <!-- 取消按钮 -->
        <Button fx:id="cancelButton" mnemonicParsing="false" text="取消" 
                style="-fx-background-color: #6c757d; -fx-text-fill: white;" />
        
        <!-- 保存按钮 -->
        <Button fx:id="saveButton" mnemonicParsing="false" text="保存" 
                style="-fx-background-color: #28a745; -fx-text-fill: white;" />
    </HBox>
</VBox>
