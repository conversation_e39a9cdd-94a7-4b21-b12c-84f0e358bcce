<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<!--
  收件人管理对话框界面
  包含收件人列表、标签管理、搜索筛选等功能
-->
<VBox prefHeight="600.0" prefWidth="800.0" spacing="15.0" style="-fx-background-color: #f8f9fa;" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.bboss.report.controller.RecipientManagementController">
    <padding>
        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
    </padding>

    <!-- 标题 -->
    <Label text="收件人管理">
        <font>
            <Font name="System Bold" size="18.0" />
        </font>
    </Label>
    <Separator prefWidth="200.0" />

    <!-- 搜索和筛选区域 -->
    <HBox spacing="10.0" alignment="CENTER_LEFT">
        <Label text="搜索:" />
        <TextField fx:id="searchField" promptText="输入邮箱地址或显示名称" HBox.hgrow="ALWAYS" />
        <Label text="标签筛选:" />
        <ComboBox fx:id="tagFilterComboBox" promptText="选择标签" />
        <Button fx:id="searchButton" text="搜索" />
        <Button fx:id="clearFilterButton" text="清除筛选" />
    </HBox>

    <!-- 主内容区域 -->
    <HBox spacing="15.0" VBox.vgrow="ALWAYS">
        <!-- 左侧：收件人列表 -->
        <VBox spacing="10.0" HBox.hgrow="ALWAYS">
            <Label text="收件人列表">
                <font>
                    <Font name="System Bold" size="14.0" />
                </font>
            </Label>
            
            <!-- 收件人表格 -->
            <TableView fx:id="recipientTableView" VBox.vgrow="ALWAYS">
                <columns>
                    <TableColumn fx:id="emailColumn" text="邮箱地址" prefWidth="200.0" />
                    <TableColumn fx:id="displayNameColumn" text="显示名称" prefWidth="150.0" />
                    <TableColumn fx:id="tagsColumn" text="标签" prefWidth="120.0" />
                    <TableColumn fx:id="useCountColumn" text="使用次数" prefWidth="80.0" />
                    <TableColumn fx:id="lastUsedColumn" text="最后使用" prefWidth="120.0" />
                    <TableColumn fx:id="typeColumn" text="类型" prefWidth="60.0" />
                </columns>
            </TableView>
            
            <!-- 收件人操作按钮 -->
            <HBox spacing="10.0">
                <Button fx:id="addRecipientButton" text="添加收件人" />
                <Button fx:id="editRecipientButton" text="编辑" />
                <Button fx:id="deleteRecipientButton" text="删除" style="-fx-text-fill: red;" />
                <Region HBox.hgrow="ALWAYS" />
                <Label fx:id="recipientCountLabel" text="共 0 个收件人" />
            </HBox>
        </VBox>

        <!-- 右侧：标签管理 -->
        <VBox spacing="10.0" prefWidth="250.0">
            <Label text="标签管理">
                <font>
                    <Font name="System Bold" size="14.0" />
                </font>
            </Label>
            
            <!-- 标签列表 -->
            <ListView fx:id="tagListView" VBox.vgrow="ALWAYS" />
            
            <!-- 标签操作 -->
            <VBox spacing="5.0">
                <HBox spacing="5.0">
                    <TextField fx:id="newTagField" promptText="新标签名称" HBox.hgrow="ALWAYS" />
                    <Button fx:id="addTagButton" text="+" style="-fx-font-weight: bold;" />
                </HBox>
                <HBox spacing="5.0">
                    <Button fx:id="editTagButton" text="编辑" HBox.hgrow="ALWAYS" />
                    <Button fx:id="deleteTagButton" text="删除" style="-fx-text-fill: red;" HBox.hgrow="ALWAYS" />
                </HBox>
            </VBox>
            
            <Separator />
            
            <!-- 为选中收件人添加/移除标签 -->
            <Label text="为选中收件人管理标签:">
                <font>
                    <Font name="System Bold" size="12.0" />
                </font>
            </Label>
            <ComboBox fx:id="assignTagComboBox" promptText="选择标签" />
            <HBox spacing="5.0">
                <Button fx:id="addTagToRecipientButton" text="添加标签" HBox.hgrow="ALWAYS" />
                <Button fx:id="removeTagFromRecipientButton" text="移除标签" HBox.hgrow="ALWAYS" />
            </HBox>
        </VBox>
    </HBox>

    <!-- 底部按钮 -->
    <HBox spacing="15.0" alignment="CENTER_RIGHT">
        <Label fx:id="statusLabel" text="准备就绪" HBox.hgrow="ALWAYS" />
        <Button fx:id="exportButton" text="导出" visible="false" />
        <Button fx:id="importButton" text="导入" visible="false" />
        <Button fx:id="closeButton" text="关闭" />
    </HBox>
</VBox>
