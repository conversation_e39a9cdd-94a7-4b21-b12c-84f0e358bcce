<?xml version="1.0" encoding="UTF-8"?>

<!-- 导入所有需要的 JavaFX 控件和布局 -->
<?import java.lang.*?>
<?import javafx.geometry.*?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<?import javafx.scene.text.Font?>
<!--
  【已修改】根容器改为 BorderPane，以更好地支持菜单栏布局。
-->

<BorderPane prefHeight="900.0" prefWidth="800.0" minHeight="750.0" minWidth="650.0" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.bboss.report.controller.EmailSenderController">
  <center>
    <!-- 主内容区域 -->
    <VBox spacing="12.0" style="-fx-background-color: #f4f4f9;" BorderPane.alignment="CENTER">
      <padding>
        <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
      </padding>
      <!-- 1. 标题 -->
      <Label text="邮件发送工具">
        <font>
          <Font name="System Bold" size="22.0" />
        </font>
      </Label>
      <Separator prefWidth="180.0" />
      <!-- 2. 邮件模板选择区域 -->
      <VBox spacing="8.0" style="-fx-background-color: #ffffff; -fx-border-color: #e0e0e0; -fx-border-radius: 5; -fx-background-radius: 5;">
        <padding>
          <Insets bottom="15.0" left="15.0" right="15.0" top="15.0" />
        </padding>
        <Label text="邮件模板:" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
        <HBox spacing="12.0" alignment="CENTER_LEFT">
          <ComboBox fx:id="templateComboBox" promptText="选择邮件模板（可选）" prefWidth="400.0" maxWidth="Infinity" HBox.hgrow="ALWAYS" />
          <Button fx:id="applyTemplateButton" text="应用模板" prefWidth="90.0" style="-fx-background-color: #4CAF50; -fx-text-fill: white;" />
          <Button fx:id="selectDirectoryButton" text="选择目录" prefWidth="90.0" style="-fx-background-color: #2196F3; -fx-text-fill: white;" />
          <Button fx:id="manageTemplatesButton" text="管理模板" prefWidth="90.0" style="-fx-background-color: #FF9800; -fx-text-fill: white;" />
        </HBox>
      </VBox>

      <!-- 3. 发件人、收件人、抄送、主题输入区域 -->
      <GridPane hgap="15.0" vgap="15.0">
        <columnConstraints>
          <ColumnConstraints hgrow="NEVER" minWidth="90.0" maxWidth="120.0" />
          <ColumnConstraints hgrow="ALWAYS" minWidth="300.0" />
        </columnConstraints>
        <!-- 发件人行 -->
        <Label text="发 件 人:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
        <ComboBox fx:id="senderEmailComboBox" editable="true" promptText="选择或输入发件人邮箱" maxWidth="Infinity" GridPane.columnIndex="1" GridPane.rowIndex="0" />
        <!-- 收件人行 -->
        <Label text="收 件 人:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
        <VBox spacing="3.0" GridPane.columnIndex="1" GridPane.rowIndex="1">
            <HBox spacing="5.0">
                <ComboBox fx:id="recipientComboBox" editable="true" promptText="选择或输入收件人" maxWidth="Infinity" HBox.hgrow="ALWAYS" />
                <Button fx:id="addRecipientButton" text="+" style="-fx-font-weight: bold;" prefWidth="35.0" />
                <Button fx:id="manageRecipientsButton" text="管理" prefWidth="70.0" />
            </HBox>
            <TextArea fx:id="recipientField" promptText="多个收件人用 ; 分隔" prefRowCount="2" maxWidth="Infinity" wrapText="true" />
        </VBox>
        <!-- 抄送行 -->
        <Label text="抄    送:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
        <VBox spacing="3.0" GridPane.columnIndex="1" GridPane.rowIndex="2">
            <HBox spacing="5.0">
                <ComboBox fx:id="ccComboBox" editable="true" promptText="选择或输入抄送人" maxWidth="Infinity" HBox.hgrow="ALWAYS" />
                <Button fx:id="addCcButton" text="+" style="-fx-font-weight: bold;" prefWidth="35.0" />
            </HBox>
            <TextArea fx:id="ccField" promptText="多个抄送人用 ; 分隔" prefRowCount="2" maxWidth="Infinity" wrapText="true" />
        </VBox>
        <!-- 主题行 -->
        <Label text="主    题:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
        <TextField fx:id="subjectField" promptText="请输入邮件主题" maxWidth="Infinity" GridPane.columnIndex="1" GridPane.rowIndex="3" />
      </GridPane>
      <!-- 4. 附件管理区域 -->
      <Label text="附件列表:" />
      <VBox spacing="8.0" VBox.vgrow="ALWAYS" prefHeight="220.0">
        <!-- 附件操作按钮 -->
        <HBox spacing="8.0">
          <Button fx:id="addFileButton" mnemonicParsing="false" text="添加文件..." prefWidth="100.0" />
          <Button fx:id="addFolderButton" mnemonicParsing="false" text="添加文件夹..." prefWidth="100.0" />
          <Button fx:id="removeAttachmentButton" mnemonicParsing="false" style="-fx-text-fill: red;" text="删除选中" prefWidth="80.0" />
          <Button fx:id="smartProcessButton" mnemonicParsing="false" style="-fx-background-color: #4CAF50; -fx-text-fill: white;" text="智能处理" prefWidth="100.0" visible="false" />
        </HBox>
        <!-- 附件显示列表 -->
        <ListView fx:id="attachmentListView" prefHeight="160.0" maxWidth="Infinity" minWidth="400.0" VBox.vgrow="ALWAYS" />
      </VBox>
      <!-- 5. 邮件正文输入区域 -->
      <Label text="邮件正文:" />
      <TextArea fx:id="bodyArea" prefHeight="180.0" maxWidth="Infinity" minWidth="400.0" promptText="在此处输入邮件内容..." wrapText="true" VBox.vgrow="SOMETIMES" />
      <!-- 6. 底部操作栏 -->
      <HBox alignment="CENTER_RIGHT" spacing="15.0">
        <!-- 【已修改】移除了左侧的设置按钮 -->
        <Label fx:id="statusLabel" text="状态：准备就绪" textFill="#555555" HBox.hgrow="ALWAYS" />
        <Button fx:id="sendButton" mnemonicParsing="false" prefHeight="35.0" prefWidth="100.0" text="发 送">
          <font>
            <Font name="System Bold" size="16.0" />
          </font>
        </Button>
      </HBox>
    </VBox>
  </center>
  <top>
    <!-- 【新增】顶部菜单栏 -->
    <MenuBar BorderPane.alignment="CENTER">
      <menus>
        <Menu mnemonicParsing="false" text="设置">
          <items>
            <!-- 【新增】设置菜单项 -->
            <MenuItem mnemonicParsing="false" text="发件人设置" fx:id="settingsMenuItem" />
          </items>
        </Menu>
      </menus>
    </MenuBar>
  </top>
</BorderPane>
