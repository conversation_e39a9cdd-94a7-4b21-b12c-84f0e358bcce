<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<!--
  变量输入对话框界面
  用于在应用模板时输入变量值
-->
<VBox prefHeight="500.0" prefWidth="600.0" spacing="15.0" style="-fx-background-color: #f8f9fa;" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.bboss.report.controller.VariableInputController">
    <padding>
        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
    </padding>

    <!-- 标题 -->
    <Label text="模板变量输入">
        <font>
            <Font name="System Bold" size="18.0" />
        </font>
    </Label>
    
    <!-- 模板信息 -->
    <VBox spacing="5.0">
        <Label fx:id="templateInfoLabel" text="模板：" style="-fx-text-fill: #666666;">
            <font>
                <Font size="12.0" />
            </font>
        </Label>
        <Separator prefWidth="200.0" />
    </VBox>

    <!-- 变量输入区域 -->
    <VBox spacing="10.0" VBox.vgrow="ALWAYS">
        <Label text="请填写以下变量值：">
            <font>
                <Font name="System Bold" size="14.0" />
            </font>
        </Label>
        
        <!-- 变量输入表单容器 -->
        <ScrollPane fx:id="variableScrollPane" fitToWidth="true" VBox.vgrow="ALWAYS">
            <VBox fx:id="variableContainer" spacing="15.0">
                <padding>
                    <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                </padding>
            </VBox>
        </ScrollPane>
    </VBox>

    <!-- 预设变量说明 -->
    <VBox spacing="5.0">
        <Label text="预设变量说明：" style="-fx-text-fill: #666666;">
            <font>
                <Font name="System Bold" size="12.0" />
            </font>
        </Label>
        <TextArea fx:id="presetVariablesInfo" editable="false" prefRowCount="3" 
                  style="-fx-background-color: #f0f0f0; -fx-border-color: #cccccc;" 
                  wrapText="true" />
    </VBox>

    <!-- 错误信息显示 -->
    <Label fx:id="errorLabel" text="" style="-fx-text-fill: #dc3545;" visible="false">
        <font>
            <Font name="System Bold" size="12.0" />
        </font>
    </Label>

    <!-- 按钮区域 -->
    <HBox spacing="10.0" alignment="CENTER_RIGHT">
        <Button fx:id="previewButton" text="预览效果" prefWidth="100.0" />
        <Button fx:id="resetButton" text="重置" prefWidth="80.0" />
        <Button fx:id="confirmButton" text="确认" prefWidth="80.0" 
                style="-fx-background-color: #28a745; -fx-text-fill: white;" />
        <Button fx:id="cancelButton" text="取消" prefWidth="80.0" 
                style="-fx-background-color: #6c757d; -fx-text-fill: white;" />
    </HBox>
</VBox>
