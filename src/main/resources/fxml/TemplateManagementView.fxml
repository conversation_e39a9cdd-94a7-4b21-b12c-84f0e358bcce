<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<!--
  邮件模板管理对话框界面
  包含模板创建、编辑、删除等功能
-->
<VBox prefHeight="970.0" prefWidth="840.0" spacing="15.0" style="-fx-background-color: #f8f9fa;" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.bboss.report.controller.TemplateManagementController">
    <padding>
        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
    </padding>

    <!-- 标题 -->
    <Label text="邮件模板管理">
        <font>
            <Font name="System Bold" size="18.0" />
        </font>
    </Label>
    <Separator prefWidth="200.0" />

    <!-- 主要内容区域 -->
    <HBox spacing="15.0" VBox.vgrow="ALWAYS">
        
        <!-- 左侧：模板列表 -->
        <VBox spacing="10.0" prefWidth="300.0">
            <Label text="模板列表">
                <font>
                    <Font name="System Bold" size="14.0" />
                </font>
            </Label>
            
            <!-- 模板列表操作按钮 -->
            <VBox spacing="5.0">
                <HBox spacing="5.0">
                    <Button fx:id="newTemplateButton" text="新建" style="-fx-background-color: #28a745; -fx-text-fill: white;" />
                    <Button fx:id="editTemplateButton" text="编辑" style="-fx-background-color: #007bff; -fx-text-fill: white;" />
                    <Button fx:id="deleteTemplateButton" text="删除" style="-fx-background-color: #dc3545; -fx-text-fill: white;" />
                    <Button fx:id="duplicateTemplateButton" text="复制" />
                </HBox>
            </VBox>
            
            <!-- 模板列表 -->
            <ListView fx:id="templateListView" VBox.vgrow="ALWAYS" />
        </VBox>
        
        <!-- 右侧：模板详情编辑 -->
        <VBox spacing="10.0" HBox.hgrow="ALWAYS">
            <Label text="模板详情">
                <font>
                    <Font name="System Bold" size="14.0" />
                </font>
            </Label>
            
            <!-- 模板基本信息 -->
            <GridPane hgap="10.0" vgap="10.0">
                <columnConstraints>
                    <ColumnConstraints hgrow="NEVER" minWidth="100.0" />
                    <ColumnConstraints hgrow="ALWAYS" />
                </columnConstraints>
                
                <!-- 模板名称 -->
                <Label text="模板名称:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                <TextField fx:id="templateNameField" promptText="请输入模板名称" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                
                <!-- 模板描述 -->
                <Label text="模板描述:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                <TextField fx:id="templateDescriptionField" promptText="可选的模板描述" GridPane.columnIndex="1" GridPane.rowIndex="1" />
            </GridPane>
            
            <!-- 邮件信息 -->
            <Label text="邮件信息">
                <font>
                    <Font name="System Bold" size="12.0" />
                </font>
            </Label>
            
            <GridPane hgap="10.0" vgap="10.0">
                <columnConstraints>
                    <ColumnConstraints hgrow="NEVER" minWidth="100.0" />
                    <ColumnConstraints hgrow="ALWAYS" />
                </columnConstraints>
                
                <!-- 收件人 -->
                <Label text="收件人:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                <TextArea fx:id="recipientsField" promptText="多个收件人用分号(;)分隔" prefRowCount="2" wrapText="true" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                
                <!-- 抄送人 -->
                <Label text="抄送人:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                <TextArea fx:id="ccRecipientsField" promptText="多个抄送人用分号(;)分隔" prefRowCount="2" wrapText="true" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                
                <!-- 邮件主题 -->
                <Label text="邮件主题:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                <TextField fx:id="subjectField" promptText="请输入邮件主题" GridPane.columnIndex="1" GridPane.rowIndex="2" />
            </GridPane>
            
            <!-- 邮件正文 -->
            <Label text="邮件正文:" />
            <TextArea fx:id="bodyField" promptText="请输入邮件正文内容..." prefRowCount="4" wrapText="true" VBox.vgrow="SOMETIMES" />
            
            <!-- 附件过滤条件 -->
            <Label text="附件包含条件">
                <font>
                    <Font name="System Bold" size="12.0" />
                </font>
            </Label>
            
            <GridPane hgap="10.0" vgap="10.0">
                <columnConstraints>
                    <ColumnConstraints hgrow="NEVER" minWidth="100.0" />
                    <ColumnConstraints hgrow="ALWAYS" />
                </columnConstraints>
                
                <!-- 文件前缀 -->
                <Label text="文件前缀:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                <VBox spacing="5.0" GridPane.columnIndex="1" GridPane.rowIndex="0">
                    <TextField fx:id="filePrefixesField" promptText="多个前缀用逗号(,)分隔，如: report_,audit_,summary_" />
                    <Label text="示例: report_, audit_, summary_" style="-fx-text-fill: #666666; -fx-font-size: 10px;" />
                </VBox>
                
                <!-- 文件扩展名 -->
                <Label text="文件扩展名:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                <VBox spacing="5.0" GridPane.columnIndex="1" GridPane.rowIndex="1">
                    <HBox spacing="5.0">
                        <TextField fx:id="fileExtensionsField" promptText="多个扩展名用逗号(,)分隔，如: pdf,xlsx,docx" HBox.hgrow="ALWAYS" />
                        <Button fx:id="selectExtensionsButton" text="选择" />
                    </HBox>
                    <Label text="示例: pdf, xlsx, docx, txt, zip" style="-fx-text-fill: #666666; -fx-font-size: 10px;" />
                </VBox>
            </GridPane>

            <!-- 附件排除条件 -->
            <Label text="附件排除条件（正则表达式）">
                <font>
                    <Font name="System Bold" size="12.0" />
                </font>
            </Label>

            <VBox spacing="10.0">
                <!-- 正则表达式输入区域 -->
                <VBox spacing="5.0">
                    <TextArea fx:id="excludeFilePatternField" promptText="输入正则表达式模式，如: ^temp_.*|.*\.tmp$"
                              prefRowCount="3" wrapText="true" />
                    <HBox spacing="10.0">
                        <Button fx:id="validatePatternButton" text="验证语法" />
                        <Button fx:id="selectPatternButton" text="选择模式" />
                        <Label fx:id="patternValidationLabel" text="" style="-fx-text-fill: #ff0000; -fx-font-size: 10px;" />
                    </HBox>
                </VBox>

            </VBox>

            <Label text="说明：排除条件优先级高于包含条件，匹配正则表达式的文件将被过滤掉"
                   style="-fx-text-fill: #ff6600; -fx-font-size: 11px; -fx-font-weight: bold;" />

            <!-- 文件压缩设置 -->
            <VBox spacing="10.0">
                <Label text="文件压缩设置">
                    <font>
                        <Font name="System Bold" size="12.0" />
                    </font>
                </Label>

                <VBox spacing="8.0">
                    <CheckBox fx:id="enableCompressionCheckBox" text="启用文件压缩" />

                    <VBox fx:id="compressionPasswordContainer" spacing="5.0" visible="false" managed="false">
                        <Label text="压缩密码（可选）:" style="-fx-font-size: 11px;" />
                        <PasswordField fx:id="compressionPasswordField" promptText="留空表示无密码保护" />
                        <Label text="提示：设置密码后，收件人需要密码才能解压文件"
                               style="-fx-text-fill: #666666; -fx-font-size: 10px;" />
                    </VBox>
                </VBox>
            </VBox>

            <!-- 变量管理区域 -->
            <VBox spacing="10.0">
                <Label text="模板变量管理">
                    <font>
                        <Font name="System Bold" size="14.0" />
                    </font>
                </Label>

                <HBox spacing="10.0" alignment="CENTER_LEFT">
                    <Button fx:id="manageVariablesButton" text="管理变量" />
                    <Label fx:id="variableCountLabel" text="变量数量: 0" style="-fx-text-fill: #666666;" />
                </HBox>

                <TextArea fx:id="variablePreviewArea" promptText="变量预览将显示在这里..."
                          prefRowCount="3" editable="false"
                          style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6;" />
            </VBox>

            <!-- 模板操作按钮 -->
            <HBox spacing="10.0" alignment="CENTER_RIGHT">
                <Button fx:id="testTemplateButton" text="测试过滤" />
                <Button fx:id="saveTemplateButton" text="保存模板" style="-fx-background-color: #28a745; -fx-text-fill: white;" />
                <Button fx:id="clearFormButton" text="清空表单" />
            </HBox>
        </VBox>
    </HBox>
    
    <!-- 底部状态栏和操作按钮 -->
    <Separator prefWidth="200.0" />
    <HBox spacing="15.0" alignment="CENTER_LEFT">
        <Label fx:id="statusLabel" text="状态：准备就绪" textFill="#555555" HBox.hgrow="ALWAYS" />
        <Button fx:id="importTemplatesButton" text="导入模板" />
        <Button fx:id="exportTemplatesButton" text="导出模板" />
        <Button fx:id="closeButton" text="关闭" prefWidth="80.0" />
    </HBox>
</VBox>
