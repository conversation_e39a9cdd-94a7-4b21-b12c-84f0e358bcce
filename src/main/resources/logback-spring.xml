<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <property name="LOG_PATH" value="${user.home}/.email-sender/logs"/>
    <!-- 输出到控制台 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <!-- 输出的格式 -->
        <encoder>
            <charset>UTF-8</charset>
            <pattern>%d{yy-MM-dd HH:mm:ss.SSS} %-5level %logger{20} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="TEXTAREA" class="com.bboss.report.component.TextAreaOutputStreamAppender">
        <encoder>
            <charset>UTF-8</charset>
            <pattern>%d{yy-MM-dd HH:mm:ss.SSS} %-5level %logger{20} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="ALL" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/audit-tool-%d{yyyyMMdd}.log</fileNamePattern>
            <!--日志文件保留天数-->
            <maxHistory>7</maxHistory>
            <!--每个文件最多100MB，保留15天的历史记录，但最多1GB-->
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <charset>UTF-8</charset>
            <pattern>%d{yy-MM-dd HH:mm:ss.SSS} %-5level %logger{20} - %msg%n</pattern>
        </encoder>
        <!--<append>true</append>
        <prudent>true</prudent>-->
    </appender>
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="TEXTAREA"/>
        <appender-ref ref="ALL"/>
    </root>
</configuration>
