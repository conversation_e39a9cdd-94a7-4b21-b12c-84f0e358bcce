# 邮件模板排除过滤功能实现文档

## 概述

本次更新为邮件模板系统添加了完整的附件排除过滤功能，允许用户在模板中配置排除条件，在文件扫描时自动过滤掉不需要的文件。排除条件的优先级高于包含条件，提供更精确的文件过滤控制。

## 功能特性

### 1. 数据模型扩展

#### EmailTemplate 新增字段
- `excludeFilePrefixes` (String): 排除的文件名前缀（逗号分隔）
- `excludeFileExtensions` (String): 排除的文件扩展名（逗号分隔）

#### 相关方法
- `getExcludeFilePrefixes()` / `setExcludeFilePrefixes(String)`: 排除前缀字符串操作
- `getExcludeFileExtensions()` / `setExcludeFileExtensions(String)`: 排除扩展名字符串操作
- `getExcludeFilePrefixList()` / `setExcludeFilePrefixList(List<String>)`: 排除前缀列表操作
- `getExcludeFileExtensionList()` / `setExcludeFileExtensionList(List<String>)`: 排除扩展名列表操作

### 2. 数据库架构更新

#### 新增数据库列
- `exclude_file_prefixes TEXT`: 存储排除的文件前缀（逗号分隔）
- `exclude_file_extensions TEXT`: 存储排除的文件扩展名（逗号分隔）

#### 数据库迁移
- 自动检测并添加新列到现有数据库
- 为现有模板设置默认值（空字符串，表示无排除条件）
- 向后兼容，不影响现有功能

### 3. 文件过滤逻辑优化

#### 新的过滤算法
```
最终结果 = (包含前缀 AND 包含扩展名) AND NOT (排除前缀 OR 排除扩展名)
```

#### 过滤优先级
1. **包含条件检查**：文件必须满足包含前缀 AND 包含扩展名
2. **排除条件检查**：文件不能满足排除前缀 OR 排除扩展名
3. **最终决策**：包含条件通过 AND 排除条件不通过

#### FileFilterUtil 扩展
- `filterFiles()` 方法重载，支持排除参数
- `checkIncludeConditions()`: 检查包含条件（AND逻辑）
- `checkExcludeConditions()`: 检查排除条件（OR逻辑）
- `cleanExtension()`: 统一扩展名格式处理

### 4. 模板管理界面增强

#### 新增UI控件
- **排除前缀输入框**: 配置要排除的文件名前缀
- **排除扩展名输入框**: 配置要排除的文件扩展名
- **视觉分隔**: 清晰区分"包含条件"和"排除条件"
- **帮助提示**: 提供示例和说明文字

#### UI布局优化
- 包含条件和排除条件分组显示
- 橙色警告文字说明排除条件优先级
- 示例文本帮助用户理解配置格式

### 5. 功能验证和集成

#### 模板测试功能
- 测试时同时应用包含和排除条件
- 显示详细的过滤结果统计
- 区分显示包含条件和排除条件

#### 模板应用功能
- 自动应用排除过滤逻辑
- 在确认对话框中显示过滤统计
- 与压缩功能无缝集成

#### 导入/导出功能
- XML格式包含排除条件字段
- 模板复制包含排除设置
- 向后兼容旧版本模板文件

## 使用示例

### 1. 基本排除配置

#### 排除临时文件
```
排除前缀: temp_, tmp_, ~, .
排除扩展名: tmp, bak, log, cache
```

#### 排除备份文件
```
排除前缀: backup_, old_, archive_
排除扩展名: old, backup, bak
```

#### 排除系统文件
```
排除前缀: ., ~, $
排除扩展名: sys, dll, exe, tmp
```

### 2. 复合过滤示例

#### 审计报告模板
```
包含条件:
- 前缀: audit_, review_, report_
- 扩展名: pdf, docx, xlsx

排除条件:
- 前缀: temp_, draft_, ~
- 扩展名: tmp, bak, log
```

#### 数据导出模板
```
包含条件:
- 前缀: export_, data_
- 扩展名: xlsx, csv, txt

排除条件:
- 前缀: backup_, old_
- 扩展名: tmp, log, cache
```

### 3. 过滤逻辑示例

对于文件 `report_2024.pdf`：
- 包含前缀匹配：`report_` ✓
- 包含扩展名匹配：`pdf` ✓
- 排除前缀匹配：无 ✗
- 排除扩展名匹配：无 ✗
- **最终结果**：包含 ✓

对于文件 `temp_report.pdf`：
- 包含前缀匹配：无 ✗
- **最终结果**：排除 ✗

对于文件 `report_draft.tmp`：
- 包含前缀匹配：`report_` ✓
- 包含扩展名匹配：无 ✗
- **最终结果**：排除 ✗

## 技术实现

### 1. 数据流程
```
模板配置 → 数据库存储 → 模板加载 → 文件扫描 → 过滤处理 → 结果显示
```

### 2. 关键类和方法

#### EmailTemplate
- 新增排除字段的存储和访问方法
- 列表格式转换方法
- 构造函数默认值设置

#### TemplateManager
- `addExcludeFilterColumns()`: 数据库迁移方法
- 更新的CRUD操作支持排除字段
- 导入/导出功能扩展

#### FileFilterUtil
- `filterFiles()`: 重载方法支持排除参数
- `matchesFilter()`: 核心过滤逻辑
- `checkIncludeConditions()` / `checkExcludeConditions()`: 分离的条件检查
- `generateSummary()`: 增强的结果摘要生成

#### TemplateManagementController
- 新增排除条件UI控件绑定
- 表单验证和数据保存逻辑
- 测试功能集成排除条件

### 3. 配置管理
- 排除设置持久化到SQLite数据库
- 支持模板导入/导出
- 模板复制包含排除配置
- 向后兼容性保证

## 用户界面设计

### 1. 布局结构
```
附件包含条件
├── 文件前缀: [输入框]
└── 文件扩展名: [输入框] [选择按钮]

附件排除条件
├── 排除前缀: [输入框]
└── 排除扩展名: [输入框]

[说明文字：排除条件优先级高于包含条件]
```

### 2. 用户体验优化
- 清晰的视觉分组和标签
- 示例文本帮助理解
- 优先级说明避免混淆
- 一致的输入格式（逗号分隔）

### 3. 表单验证
- 输入格式验证
- 实时保存状态更新
- 测试功能即时反馈

## 兼容性和安全性

### 1. 向后兼容
- 现有模板默认无排除条件
- 数据库自动迁移
- 旧版本导入文件兼容
- API接口保持兼容

### 2. 性能优化
- 高效的流式文件处理
- 条件检查顺序优化
- 内存使用控制

### 3. 错误处理
- 数据库迁移异常处理
- 文件扫描错误恢复
- 用户输入验证

## 测试建议

### 1. 功能测试
- 创建和编辑排除条件模板
- 测试各种排除条件组合
- 验证过滤逻辑正确性

### 2. 边界测试
- 空排除条件处理
- 特殊字符处理
- 大量文件扫描性能

### 3. 集成测试
- 与压缩功能的兼容性
- 模板导入/导出功能
- 数据库迁移测试

### 4. 用户体验测试
- UI响应性测试
- 错误提示友好性
- 帮助文档完整性

## 后续优化建议

### 1. 功能扩展
- 支持正则表达式排除
- 文件大小排除条件
- 修改时间排除条件

### 2. 用户体验
- 排除条件预设模板
- 可视化过滤结果预览
- 批量排除条件管理

### 3. 性能优化
- 并行文件扫描
- 缓存机制优化
- 大目录处理优化

## 总结

排除过滤功能的实现完善了邮件模板系统的文件过滤能力，提供了更精确和灵活的文件选择控制。通过清晰的优先级设计和直观的用户界面，用户可以轻松配置复杂的过滤条件，提高邮件发送的效率和准确性。

功能设计遵循了现有的架构模式，确保了代码的一致性和可维护性，同时保持了完全的向后兼容性。
