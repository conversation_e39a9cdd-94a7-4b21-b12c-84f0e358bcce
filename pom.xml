<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.bboss</groupId>
    <artifactId>report-audit-tool</artifactId>
    <version>1.0</version>
    <name>report-audit-tool</name>
    <description>报表稽核工具</description>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.7.RELEASE</version>
    </parent>
    <properties>
        <resource.delimiter>@</resource.delimiter>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot.version>2.3.7.RELEASE</spring-boot.version>

        <lombok.version>1.16.16</lombok.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-logging</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>4.5.6</version>
        </dependency>
        <dependency>
            <groupId>de.roskenet</groupId>
            <artifactId>springboot-javafx-support</artifactId>
            <version>2.1.6</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.3.2</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.24</version> <!-- 请使用最新版本 -->
        </dependency>
        <!-- JavaMail API for email sending -->
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
            <version>1.6.2</version>
        </dependency>
        <!-- JavaActivation Framework for email attachments -->
        <dependency>
            <groupId>javax.activation</groupId>
            <artifactId>activation</artifactId>
            <version>1.1.1</version>
        </dependency>
        <!-- Jackson for JSON processing -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.15.2</version>
        </dependency>

        <!-- Jackson Databind -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.15.2</version>
        </dependency>

        <!-- Jackson Annotations -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.15.2</version>
        </dependency>
        <!-- JavaFX Controls -->
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-controls</artifactId>
            <version>11.0.2</version>
        </dependency>
        <!-- JavaFX FXML -->
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-fxml</artifactId>
            <version>11.0.2</version>
        </dependency>
        <!-- SQLite JDBC Driver for template storage -->
        <dependency>
            <groupId>org.xerial</groupId>
            <artifactId>sqlite-jdbc</artifactId>
            <version>********</version>
        </dependency>
        <dependency>
            <groupId>net.lingala.zip4j</groupId>
            <artifactId>zip4j</artifactId>
            <version>2.11.5</version>
        </dependency>
    </dependencies>

    <build>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.yml</include>
                    <include>**/*.yaml</include>
                    <include>**/*.properties</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <excludes>
                    <exclude>**/*.yml</exclude>
                    <exclude>**/*.yaml</exclude>
                    <exclude>**/*.properties</exclude>
                </excludes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>timestamp-property</id>
                        <goals>
                            <goal>timestamp-property</goal>
                        </goals>
                        <configuration>
                            <name>timestamp</name>
                            <pattern>yyyyMMddHHmmss</pattern>
                            <timeZone>GMT+8</timeZone>
                        </configuration>
                    </execution>
                    <execution>
                        <id>short-timestamp-property</id>
                        <goals>
                            <goal>timestamp-property</goal>
                        </goals>
                        <configuration>
                            <name>shortTimestamp</name>
                            <pattern>yyyyMMddHHmmss</pattern>
                            <timeZone>GMT+8</timeZone>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>

                <groupId>com.zenjava</groupId>
                <artifactId>javafx-maven-plugin</artifactId>
                <version>8.8.3</version>
                <configuration>
                    <!-- 启动类 -->
                    <mainClass>com.bboss.report.AuditToolApplication</mainClass>

                    <jvmArgs>
                        <jvmArg>-Dfile.encoding=UTF-8</jvmArg>
                        <jvmArg>-Xmx2048m</jvmArg> <!-- 设置最大堆内存为2048MB -->
                        <jvmArg>-Xms256m</jvmArg> <!-- 设置初始堆内存为256MB -->
                    </jvmArgs>
                    <!-- 公司名称 -->
                    <vendor>bboss</vendor>
                    <!-- 应用名称 ${project.build.finalName} = ${project.artifactId}-${project.version} -->
                    <appName>AuditTool</appName>
                    <!-- 发行版本 -->
                    <nativeReleaseVersion>${project.version}</nativeReleaseVersion>
                    <!-- 需要生成快捷方式-->
                    <needShortcut>true</needShortcut>
                    <bundleArguments>
                        <!--设置为true将在Program Files中安装应用程序。设置为false将应用程序安装到用户的主目录中。默认值为false。-->
                        <systemWide>true</systemWide>
                        <!-- 让用户选择安装目标文件夹 -->
                        <installdirChooser>true</installdirChooser>
                        <icon>${project.basedir}/src/main/resources/icon/icon.ico</icon>
                    </bundleArguments>
                    <!-- 桌面图标 -->
                    <needShortcut>true</needShortcut>
                    <!-- 菜单设置 -->
                    <needMenu>true</needMenu>
                    <!--排除掉不想要打包进lib依赖库的依赖-->
                    <classpathExcludes>
                        <classpathExclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </classpathExclude>
                    </classpathExcludes>
                    <!--默认情况下，这将被设置为'ALL'，根据你安装的操作系统，以下值对于安装者来说是可能的:-->
                    <!--windows.app (只创建Windows可执行文件，不生成安装向导的exe)-->
                    <!--exe (通过InnoIDE生成exe安装程序)-->
                    <!--msi (Microsoft Windows MSI Installer, via WiX)-->
                    <!--可以同时添加多个bundler选项，下面是同时生成exe文件夹及exe安装包的-->
<!--                    <bundler>windows.app</bundler>-->
                    <bundler>ALL</bundler>
                    <!--如果构建过程中出现问题，可以打开这个，会显示详细的打包过程信息-->
                    <verbose>true</verbose>
                </configuration>
            </plugin>

        </plugins>
    </build>

</project>